// import ble from "../wxutil/ble";
import ble from "@/wxutil/newBle";
import { orderOutGoodsStatus, sendOpenDoorOrderComplete, recOnceOpenDoorOrderComplete, recOnceCloseDoorOrderComplete } from "@/common/http/api";
export const bleMixin = {
    methods: {
        //蓝牙相关
        // btConnectCallBack: function (obj, types) { // 链接后回调
        //     console.log("index.js回调函数" + obj, types);
        //     //this.onShowBuyModel()
        // },

        // btErrorCallBack: function (errorType, errorMessage) {  //处理错误码回调
        //     console.log('btErrorCallBack', errorType);
        //     ble.bluetoothStatus(errorType);
        // },

        // btWriteCallback: function (writeE, currentOrderSN) { // 蓝牙写入回调
        //     console.log('写入数据状态', writeE);
        //     //todo界面处理逻辑

        //     if (writeE == 'w') { // 写入
        //         //feedbackApi.hideTimerToast(); //清空loadding
        //         // clearInterval(commonBLEDatas.downSecondId); //清空倒计时
        //         uni.showToast({
        //             title: '连接失败,请再次尝试(0)',
        //             icon: 'none'
        //         })
        //         ble.closeBLEConnection();
        //     } else if (writeE == 'success') {
        //         //this.updateOrder(currentOrderSN);
        //     }
        // },

        //初始化蓝牙
        initBt: function () {
            console.log('initBt,ble=', ble);
            // ble.setConnectionActionType(this.btConnectCallBack); //连接后操作回调
            // ble.setBtErrorCallback(this.btErrorCallBack); //执行错误时错误码回调
            // ble.setWriteCallback(this.btWriteCallback); //写入数据回调
            ble.initBle();
        },

        //充电
        onRecharge: async function (time, flag = false) {
            console.log('准备写入充电指令，time=', time, flag)
            ble.rechargeDevice(time, flag ? () => { } : this.btRechargeCallback)
        },

        btRechargeCallback: function (flag) {
            console.log("🚀 ~ btRechargeCallback  log  回调")
            if (flag) {
                uni.showToast({
                    title: "充电指令成功",
                    icon: "success",
                    duration: 1500,
                });
                console.log("go   this.charge_sn", this.charge_sn);
                setTimeout(() => {
                    uni.redirectTo({
                        url: `/homePages/chargingDetails/index?charge_sn=${this.charge_sn}`,
                    });
                }, 1500);
            } else {
                uni.showToast({
                    title: "充电指令执行失败~",
                    icon: "none",
                    duration: 2000,
                });
            }
        },
        //出货
        onOpen: async function (num, type) {
            // console.log('准备写入出货指令',type,num)
            ble.openGoodsLock(this.btOpentCallback, num, this.vCreateOrderInfo?.orderInfo?.order_sn)
            if (type == '0x83') {
                let data = {
                    order_sn: this.vCreateOrderInfo?.orderInfo?.order_sn,
                }
                // console.log('调用几次2')
                sendOpenDoorOrderComplete(data)
            }

        },
        btOpentCallback: function (flag, msg, num, indexs, isRequest, order_sn) {
            console.log("🚀 ~ btOpentCallback  log  回调", flag, msg, num, indexs, isRequest, order_sn)

            // HLB协议特殊处理：不重试
            if (msg === 'HLB_NO_RETRY') {
                console.log('🔧 HLB协议：设备出货失败，更新订单状态');

                // 更新后台订单状态
                let data = {
                    order_sn: order_sn ? order_sn : (this.vCreateOrderInfo?.orderInfo?.order_sn),
                    status: 2 // 设置为订单异常状态
                }

                console.log('🔧 HLB协议：调用API更新订单状态', data);

                // 设置超时备用方案
                let eventSent = false;
                setTimeout(() => {
                    if (!eventSent) {
                        console.log('🔧 HLB协议：API超时，延迟发送状态变化事件');
                        // 🎯 延迟发送事件，确保订单详情页面已经加载并开始监听
                        setTimeout(() => {
                            uni.$emit('orderStatusChanged', {
                                order_sn: data.order_sn,
                                status: 2,
                                message: '出货失败'
                            });
                            console.log('🔧 HLB协议：已发送延迟状态变化事件');
                        }, 2000); // 再延迟2秒，确保页面已加载
                        eventSent = true;
                    }
                }, 2000); // 2秒超时

                orderOutGoodsStatus(data).then(res => {
                    console.log('🔧 HLB协议：订单状态更新成功', res);
                    if (!eventSent) {
                        // 🎯 延迟发送事件，确保订单详情页面已经加载并开始监听
                        setTimeout(() => {
                            uni.$emit('orderStatusChanged', {
                                order_sn: data.order_sn,
                                status: 2,
                                message: '出货失败'
                            });
                            console.log('🔧 HLB协议：已发送状态变化事件');
                        }, 2000); // 延迟2秒，确保页面已加载
                        eventSent = true;
                    }
                }).catch(err => {
                    console.log('🔧 HLB协议：订单状态更新失败', err);
                    if (!eventSent) {
                        // 🎯 即使API失败，也延迟发送事件更新页面状态
                        setTimeout(() => {
                            uni.$emit('orderStatusChanged', {
                                order_sn: data.order_sn,
                                status: 2,
                                message: '出货失败'
                            });
                            console.log('🔧 HLB协议：API失败，已发送状态变化事件');
                        }, 2000); // 延迟2秒，确保页面已加载
                        eventSent = true;
                    }
                })

                // uni.showToast({
                //     title: "出货失败，请联系客服",
                //     icon: "none",
                //     duration: 2000,
                // });
                return;
            }

            if (!flag && num && indexs < 5) {
                let time = 2000
                if (msg == '电机正忙') {
                    time = 5000
                } else if (msg == '开启超时') {
                    time = 2000
                }

                //如果超时每隔2s钟重新调用连续调用5次，成功了清除定时器
                return setTimeout(() => {
                    this.onOpen(num)
                }, time)
                // this.onOpen(num)

            }
            if (msg == 1) {
                let data = {
                    order_sn: order_sn,
                    once: num
                }
                recOnceOpenDoorOrderComplete(data)
            } else if (msg == 2) {
                let data = {
                    order_sn: order_sn,
                    once: num
                }
                recOnceCloseDoorOrderComplete(data)

            }

            if (flag && isRequest) {
                return
            }
            if (!flag && msg) {
                // console.log('进来了')
                uni.showToast({
                    title: msg || "出货失败~",
                    icon: "none",
                    duration: 2000,
                });
                return

            }
            let data = {
                order_sn: order_sn ? order_sn : (this.vCreateOrderInfo?.orderInfo?.order_sn),
                status: flag ? 1 : 2 // 成功=1，失败=2（订单异常）
            }

            orderOutGoodsStatus(data).then(res => {

                if (flag) {
                    uni.showToast({
                        title: "出货成功~",
                        icon: "none",
                        duration: 2000,
                    });
                    console.log('跳转订单号', this.vCreateOrderInfo?.orderInfo?.order_sn)
                    // uni.$emit('speking')
                    this.$u.vuex('vPayStart', false)

                } else {
                    uni.showToast({
                        title: msg || "出货失败~",
                        icon: "none",
                        duration: 2000,
                    });
                }

            })
        },
        //开始连接蓝牙设备
        startBleConnect() {
            this.initBt()
        },
        //查询充电状态
        readChargeStatus() {
            ble.readChargeStatus(this.readChargeStatusCallback);//这个回调 到chargeStatus.js里面找readChargeStatusCallback
        },
        //查询电量
        readBattery(callback, type) {
            console.log('查询电量类型', type)
            if (type == '0x83') {
                ble.readElectric(callback);//这个回调 到chargeStatus.js里面找readBatteryCallback
            } else {
                ble.readChargeStatus(callback);//这个回调 到chargeStatus.js里面找readBatteryCallback
            }
        },


    },

}
