
/**
 * 
 * @param {*} payInfo 订单信息
 * @param {*} url 支付完成跳转的页面
 */
function payOrder(that, payInfo, url) {
    uni.requestPayment({
        /* #ifdef MP-WEIXIN */
        provider: "wxpay",
        timeStamp: payInfo.timeStamp,
        nonceStr: payInfo.nonceStr,
        package: payInfo.package,
        signType: payInfo.signType,
        paySign: payInfo.paySign,
        /* #endif */
        /* #ifdef MP-ALIPAY */
        provider: "alipay",
        orderInfo: payInfo.trade_no,
        /* #endif */
        /* #ifdef MP-TOUTIAO */
        orderInfo: payInfo,
        service: 5,//固定值，拉起小程序收银台
        /* #endif */
        success: (res) => {
            let isSuccess = false
            console.log("支付成功", res);
            /* #ifdef MP-WEIXIN */
            isSuccess = true
            /* #endif */
            /* #ifdef MP-ALIPAY */
            if (res.resultCode != 9000)
                return uni.showToast({
                    title: "支付失败",
                    icon: "none",
                });
            isSuccess = true
            /* #endif */
            /* #ifdef MP-TOUTIAO */
            const { code } = res;

            const resCodeStatus = {
                0: '支付成功', 1: '支付超时', 2: '支付失败', 3: '支付关闭', 4: '支付取消'
            }
            console.log("🚀 ~ code", code)
            console.log("🚀 ~ resCodeStatus[code] ", resCodeStatus[code])
            if (code == 0) {
                console.log("🚀 ~ 抖音走成功")
                isSuccess = true
            } else {
                console.log("🚀 ~ 抖音走失败")

                console.log(uni.$u);
                return that.isShowErr(resCodeStatus[code] ?? '支付失败')
            }
            /* #endif */

            //跳转到订单详情页面
            isSuccess && that.isShowSuccess('支付成功', 0, () => {
                url && uni.redirectTo({
                    url,
                })

            });


        },
        fail: (err) => {
            console.log(uni.$u);
            console.log("支付失败", err);
            /* #ifdef MP-TOUTIAO */
            that.isShowErr(err?.errMsg || '支付失败')
            /* #endif */
            /* #ifndef MP-TOUTIAO */
            that.isShowErr('支付失败')
            /* #endif */
        },
        complete(res) {
            console.log("支付complete", res);
        },
    });
}
export { payOrder }