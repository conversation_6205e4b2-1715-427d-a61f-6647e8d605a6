<template>
    <view class="cart-buttom">
        <view class="buttom-left" @click="addShop()">加入购物车</view>
        <view class="buttom-right" @click="onClickBtnBuyNow()">立即购买</view>
    </view>
</template>

<script>

export default {
    name: "shopBtn",


    data() {
        return {

        };
    },

    methods: {
        addShop(){
            this.$emit("addShop");
        },
        onClickBtnBuyNow() {
            this.$emit("onClickBtnBuyNow");
        },
    },


};
</script>

<style scoped lang="scss">
.cart-buttom {
    // position: absolute;
    // bottom: 20rpx;
    display: flex;
    // left: 0;
    // right: 0;
    width: 90%;
    margin: 0 auto;

    /* 水平方向上的自动外边距将元素居中 */
    .buttom-left {
        border-top-left-radius: 50rpx;
        border-bottom-left-radius: 50rpx;
        background-color: rgb(255, 162, 41);
        padding: 18rpx 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        color: #fff;
    }

    .buttom-right {
        border-top-right-radius: 50rpx;
        border-bottom-right-radius: 50rpx;
        background-color: rgb(246, 92, 15);
        padding: 18rpx 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        color: #fff;
    }

}
</style>