<template>
  <!--充电套餐-组件-->
  <view class="comContent">
    <view class="rechargeList">
      <view class="rechargeItem flexColumnAllCenter" v-for="(item, index) in rechargeMealList" :key="index" :class="{
        selectRechargeItem: curSelectIndex == index,
      }" @click="onClickRechargeItem(item, index)">
        {{ item.value }}片
      </view>
    </view>
  </view>
</template>
<script>

export default {
  name: "RechargeMeal",
  props: {
    // chargeRule: { type: Object, default: {} },
    // isShowKey: { type: Boolean, default: false },
  },

  data() {
    return {
      rechargeMealList: 
      [
        { value: 1 },
        { value: 2 },
        {value: 3,}
      ],

      curSelectIndex: 0,

    };
  },

  methods: {
    onClickRechargeItem(item, index) {
      this.curSelectIndex = index;
      this.$emit("selectRechargeItem", { ...item });
    },
  },
};
</script>

<style scoped lang="scss">
.comContent {
  .rechargeList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .rechargeItem {
      width: 180rpx;
      height: 120rpx;
      background: #fff;
      border-radius: 10rpx;

      border: 2rpx solid $themeColor;
      box-sizing: border-box;
      color: $themeColor;

      &:nth-child(n + 4) {
        margin-top: 20rpx;
      }

      .other {
        width: 100%;
        height: 100%;
        padding: 16rpx;
        text-align: center;
        box-sizing: border-box;

        .input-box {
          display: flex;
          align-items: center;
          color: #fff;
        }

        .input {
          background-color: #fff;
          color: #333;
        }

        .input-tip {
          font-size: 24rpx;
          line-height: 24rpx;
          white-space: nowrap;
          margin-left: 10rpx;
        }
      }

      .name {
        font-weight: bold;
        font-size: $font-size-xlarge;
      }

      .price {
        // margin-top: 28rpx;
        // color: $mainRed;
        // display: flex;
        // align-items: flex-end;
        font-size: $font-size-xsmall;
        margin-top: 12rpx;

        .unit {
          margin-bottom: 0rpx;
          font-size: $font-size-xsmall;
        }

        .value {
          margin-left: 6rpx;
          font-weight: bold;
          font-size: $font-size-middle;
        }
      }
    }

    .selectRechargeItem {
      background-color: $themeColor;
      color: white;
    }
  }
}

//适配支付宝
.u-picker {
  ::v-deep .u-picker__view {
    .u-picker__view__column {
      :nth-child(n) {
        display: block !important;
      }
    }
  }
}
</style>
