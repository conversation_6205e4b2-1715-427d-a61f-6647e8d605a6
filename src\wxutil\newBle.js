import { globalEvents } from "../global/globalEvents";
var CRC32 = require('./crc32.js');
var crypto = require('crypto');
var appOptions = {
    showLoading: (tip, methodName = '') => {
        uni.showLoading({
            title: tip,
            success: (res) => {
                console.log('方法名 ' + methodName + ' showLoading Succcess ', res, new Date());
            },
            fail: (res) => {
                console.log('方法名 ' + methodName + ' showLoading fail ', res, new Date());
            }
        });
    },
    hideLoading: (methodName = '', tips, resMsg) => {
        uni.hideLoading({
            success: (res) => {
                console.log("🚀 ~ hideLoading success", methodName, tips, resMsg)
            },
            fail: (res) => {
                console.log("🚀 ~ hideLoading fail", methodName, tips, resMsg)
            }
        });
    },
    showModal: (content, showCancel = false) => {
        uni.showModal({
            title: '温馨提示',
            content,
            showCancel,
            success: ({ confirm, cancel }) => {
                if (confirm) {
                    //用户点击确定
                    // console.log("🚀 ~ 确定回调", tips, resMsg)
                    // return
                }
                else if (cancel) {
                    // return
                    //用户点击取消
                    // console.log("🚀 ~ 取消回调", tips, resMsg)
                }
            }
        })
    },
    showToast: (title, isSuccess = false, duration = 2000) => {
        uni.showToast({
            title,
            icon: isSuccess ? 'success' : 'none',
            mask: true,
            duration,
        })
    }

}
//1.初始化蓝牙
//2.搜索蓝牙设备
const app = getApp()
let _init = false;//是否正在初始化
let _discoveryStarted = false;//是否正在搜索
let _isSearchReach = false;//是否已经检索到设备

//回调
let readRechargeCallback = null;//读取充电状态回调
let writeCallback = null;//写入充电回调
var btReadBattery = null;//接收充电回调
let num = 1;//出货次数
let indexs = 0
var ljl_number = 1;
var ljl_time = 6;
var isRequest = false; //是否正在请求
var order_sn = '';
var currentOrderSN = ''; // 当前订单编号
var newNumber = 0;
var oldNumber = 0;

//当前设备的操作类型
const OptionEnum = {
    None: -1,
    Connection: 0, //连接
    Reconnection: 1, //重连
    CloseLock: 2, //关闭
    VendingOpenLock: 8, //打开售卖机
    ReadStatus: 9, //读售卖机状态
    ReadBattery: 10, //读售卖机电量
    Recharge: 11,     //充电
    GetVolt: 12,     //获取设备电压
    ReadRecharge: 13,//读取充电状态
    ReadRechargeCallback: 14,//读取充电状态，单独回调
    ReadElectricity: 15, //读电量
    GetRandom: 16,//获取随机数
    VerificateKey: 17,//验证密钥
    cleaStatus: 18,//出货

};
//这个是变量, 存储数据的变量
let action_type = OptionEnum.None;

//连接蓝牙的各种参数
let linkBleOption = {
    device_sn: '',//设备编号 也是搜索name
    device_type: '',//设备类型
    mkey: '',//秘钥
}
const BluetoothDetail = {
    deviceId: "",//连接蓝牙设备ID
    notify_id: '',//通知特征值ID
    read_id: '',//支持read的特征值
    write_id: '',//支持write的特征值
    serviceId: '',//服务ID
    mac: "", // mac 地址
}

//无线充
class vendingOne {
    serviceUUID = "0000F040-0000-1000-8000-00805F9B34FB"
    readUUID = "0000F042-0000-1000-8000-00805F9B34FB";// 读UUID
    writeUUID = "0000F041-0000-1000-8000-00805F9B34FB"; //写 UUID
    notifyUUID = "0000F042-0000-1000-8000-00805F9B34FB";//通知
    //充电指令
    rechargeDevice(min, hour, callback) {
        action_type = OptionEnum.Recharge; // 充电
        writeCallback = callback;
        // console.log('准备写入充电指令，min=', min)
        // console.log('准备写入充电指令，hour=', hour)
        // console.log('准备写入充电指令，callback=', writeCallback)
        //转16进制
        hour = parseInt(min / 60);//取整数
        min = Math.round(min % 60);//取余数
        // console.log('准备写入充电指令，转换后的：', `${hour} 小时,${min} 分钟`)
        let hexHour = 0x00;
        if (hour > 0) {
            hexHour = '0x' + parseInt(hour).toString(16);
        }
        let hexMin = 0x00;
        if (min > 0) {
            hexMin = '0x' + parseInt(min).toString(16);
        }
        // console.log('充电时间转16进制：', hexHour, hexMin)

        let buff_array = [];
        buff_array.push(0xFE); // 1
        buff_array.push(0xD5); // 2
        buff_array.push(hexHour); // 3  小时 0x00~0x30  0~48小时 最大支持48小时
        buff_array.push(hexMin); // 4  分钟 0x00~0x3c  0~60分钟
        buff_array.push(0x00);  // 5 固定数据
        buff_array.push(0x00);	// 6 固定数据
        buff_array.push(0x00);	// 7 固定数据
        buff_array.push(0x00);	// 8 固定数据
        buff_array.push(0x00);  // 9 固定数据
        buff_array.push(0x00);	// 10 固定数据
        buff_array.push(0x00);  // 11 固定数据
        buff_array.push(0x00);	// 12 固定数据
        buff_array.push(0x00);	// 13 固定数据
        buff_array.push(0x00);	// 14 固定数据
        buff_array.push(0x00);  // 15 固定数据
        buff_array.push(0x00);	// 16 固定数据
        buff_array.push(0x00);	// 17 固定数据
        buff_array.push(0x00);  // 18 固定数据
        buff_array.push(0x00);	// 19 固定数据
        buff_array.push(0X45); // 校验位
        // console.log('充电指令：', buff_array);
        // console.log('充电指令：', buf2hex(buff_array));
        writeData(buff_array,);
    }
    handleResData(data) {
        // console.log("🚀 ~ action_type 当前操作类型", data)
        this.handleCallback(data)
    }
    handleCallback(resData) {
        // console.log("🚀 ~ resData 充电指令回调", resData)
        if (resData && (action_type == OptionEnum.ReadRecharge || action_type == OptionEnum.ReadRechargeCallback)) {
            let length = resData.length;
            let status = resData.substr(length - 4, 4);
            // console.log("🚀 ~ status 截取状态 ", status)

            if (status == '0046') {
                //充电成功
                if (action_type == OptionEnum.ReadRecharge) {
                    if (writeCallback != null) {
                        writeCallback(true);
                    }
                } else if (actione_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(true);
                    }
                }
            } else if (status == '0000' || status == '3746') {
                //充电失败
                if (action_type == OptionEnum.ReadRecharge) {
                    if (writeCallback != null) {
                        writeCallback(false);
                    }
                } else if (action_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }
            }
            action_type = OptionEnum.None;
        }
    }
    readChargeStatus(flag) {
        // 无线充						
        // console.log("🚀 ~ 无线充   开始读状态 ", BluetoothDetail?.deviceId, BluetoothDetail?.serviceId, BluetoothDetail?.read_id)
        uni.readBLECharacteristicValue({
            // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
            deviceId: BluetoothDetail?.deviceId,
            // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
            serviceId: BluetoothDetail?.serviceId,
            // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
            characteristicId: BluetoothDetail?.read_id,
            success(res) {
                // console.log("🚀 ~ res readBLECharacteristicValue", res)
                action_type = flag ? OptionEnum.ReadRechargeCallback : OptionEnum.ReadRecharge;

                /* #ifdef MP-ALIPAY */
                res?.characteristic?.value && agreementBle.handleResData(res?.characteristic?.value);
                /* #endif */
                // console.log('readBLECharacteristicValue ', res);
                // console.log('readBLECharacteristicValue:', res.errCode)
                /* #ifndef MP-ALIPAY */
                if (res.errno === 1509000) {//未充电，直接回调
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }
                /* #endif */
            }, fail: (err) => {
                // console.log("🚀 ~ err readBLECharacteristicValue", err)
            }
        })


    }
}
class vendingTwo extends vendingOne {
    constructor() {
        super()
    }
    //充电指令
    rechargeDevice(min, hour, callback) {
        action_type = OptionEnum.Recharge; // 充电
        writeCallback = callback;
        console.log('准备写入充电指令，min=', min)
        console.log('准备写入充电指令，hour=', hour)
        //转16进制
        hour = parseInt(min / 60);//取整数
        min = Math.round(min % 60);//取余数
        console.log('准备写入充电指令，转换后的：', `${hour} 小时,${min} 分钟`)
        let hexHour = 0x00;
        if (hour > 0) {
            hexHour = '0x' + parseInt(hour).toString(16);
        }
        let hexMin = 0x00;
        if (min > 0) {
            hexMin = '0x' + parseInt(min).toString(16);
        }
        console.log('充电时间转16进制：', hexHour, hexMin)

        let buff_array = [];
        buff_array.push(0xA0); // 1
        buff_array.push(0xD5); // 2
        buff_array.push(hexHour); // 3  小时 0x00~0x30  0~48小时 最大支持48小时
        buff_array.push(hexMin); // 4  分钟 0x00~0x3c  0~60分钟
        buff_array.push(0x00);  // 5 固定数据
        buff_array.push(0x00);	// 6 固定数据
        buff_array.push(0x00);	// 7 固定数据
        buff_array.push(0x00);	// 8 固定数据
        buff_array.push(0x00);  // 9 固定数据
        buff_array.push(0x00);	// 10 固定数据
        buff_array.push(0x00);  // 11 固定数据
        buff_array.push(0x00);	// 12 固定数据
        buff_array.push(0x00);	// 13 固定数据
        buff_array.push(0x00);	// 14 固定数据
        buff_array.push(0x00);  // 15 固定数据
        buff_array.push(0x00);	// 16 固定数据
        buff_array.push(0x00);	// 17 固定数据
        buff_array.push(0x00);  // 18 固定数据
        buff_array.push(0x00);	// 19 固定数据
        buff_array.push(0X45); // 校验位
        console.log('充电指令：', buff_array);
        console.log('充电指令：', buf2hex(buff_array));
        writeData(buff_array,);
    }
}

// 充电线加秘钥
class rechargeKey {
    serviceUUID = "0000FF10-0000-1000-8000-00805F9B34FB"
    readUUID = "0000FF11-0000-1000-8000-00805F9B34FB";// 读UUID
    writeUUID = "0000FF12-0000-1000-8000-00805F9B34FB"; //写 UUID
    notifyUUID = "0000FF11-0000-1000-8000-00805F9B34FB";//通知

    //充电指令
    rechargeDevice(min, callback) {
        action_type = OptionEnum.Recharge; // 充电
        writeCallback = callback;

        let head = '710a'; // 头部

        // 随机数
        let random1 = getRandom(0, 100) + '';
        random1 = random1.padStart(2, '0');
        let random2 = getRandom(0, 100) + '';
        random2 = random2.padStart(2, '0');
        console.log("随机数：", random1, random2);
        // Mac地址
        let Mac = BluetoothDetail.mac;
        console.log("mac地址", Mac);

        //(充电时间)单位分钟
        console.log("充电时间", min);
        let minutes16 = to16(min) + '';
        minutes16 = minutes16.padStart(4, '0');
        if (minutes16.length > 5) {
            minutes16 = 'ffff'
        }
        console.log("充电时间 16进制", minutes16);

        // 编码
        // let sn = "360260644211797";     
        // let sn = linkBleOption.device_sn;
        // sn = sn.split('-')[0];
        let sn = "910614849511796";
        console.log("设备编码", sn);
        sn = strToHexCharCode(sn)
        console.log("设备编码 编码后：", sn);

        let crc32Str16 = random1 + random2 + Mac + sn;
        console.log("CRC16 : ", crc32Str16);
        let crc32Str16Arr = getStrArr(crc32Str16);
        console.log("CRC16Arr : ", crc32Str16Arr)
        let crc32StrAscii = '';
        crc32Str16Arr.forEach((item, index) => {
            crc32StrAscii += hexCharCodeToStr(item)
        })
        console.log("crc32StrAscii : ", crc32StrAscii);
        let crc32 = CRC32.bstr(crc32StrAscii).toString(16);
        console.log("crc32 : ", crc32);
        crc32 = crc32.padStart(8, '0');
        console.log("crc32 0 : ", crc32);

        let hex = head + random1 + random2 + Mac + minutes16 + crc32;

        console.log("充电指令 16进制字符串： ", hex);
        let buff16Array = getStrArr(hex);
        console.log('充电指令 16进制数组：', buff16Array);
        let buff_array = [];
        buff16Array.forEach((item, index) => {
            buff_array.push(to10(item));
        });
        console.log('充电指令 16进制数组 buff_array ：', buff_array);
        console.log('充电指令 16进制数组转buff_array：', buf2hex(buff_array));
        writeData(buff_array,);
    }
    handleResData(data) {
        console.log("🚀 ~ action_type 当前操作类型", action_type);
        this.handleCallback(data)
    }
    handleCallback(resData) {
        console.log("🚀 ~ resData 充电指令回调", resData)
        if (resData && (action_type == OptionEnum.ReadRecharge || action_type == OptionEnum.ReadRechargeCallback)) {
            let length = resData.length;
            let status = resData.substr(length - 4, 4);
            console.log("🚀 ~ status 截取状态 ", status)

            if (status == '0000') {
                //充电成功
                if (action_type == OptionEnum.ReadRecharge) {
                    if (writeCallback != null) {
                        writeCallback(true);
                    }
                } else if (action_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(true);
                    }
                }
            } else {
                //充电失败
                if (action_type == OptionEnum.ReadRecharge) {
                    if (writeCallback != null) {
                        writeCallback(false);
                    }
                } else if (action_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }
            }
            action_type = OptionEnum.None;
        }
    }
    readChargeStatus(flag) {
        // 无线充						
        console.log("🚀 ~ 无线充   开始读状态 ", BluetoothDetail?.deviceId, BluetoothDetail?.serviceId, BluetoothDetail?.read_id)
        uni.readBLECharacteristicValue({
            // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
            deviceId: BluetoothDetail?.deviceId,
            // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
            serviceId: BluetoothDetail?.serviceId,
            // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
            characteristicId: BluetoothDetail?.read_id,
            success(res) {
                console.log("🚀 ~ res readBLECharacteristicValue", res)
                action_type = flag ? OptionEnum.ReadRechargeCallback : OptionEnum.ReadRecharge;

                /* #ifdef MP-ALIPAY */
                res?.characteristic?.value && agreementBle.handleResData(res?.characteristic?.value);
                /* #endif */
                console.log('readBLECharacteristicValue ', res);
                console.log('readBLECharacteristicValue:', res.errCode)
                /* #ifndef MP-ALIPAY */
                if (res.errno === 1509000) {//未充电，直接回调
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }
                /* #endif */
            }, fail: (err) => {
                console.log("🚀 ~ err readBLECharacteristicValue", err)
            }
        })


    }
}
// 湿巾

class vendingFive {
    serviceUUID = "0000FCF0-0000-1000-8000-00805F9B34FB"
    writeUUID = "0000FCF2-0000-1000-8000-00805F9B34FB"; //写 UUID
    notifyUUID = "0000FCF1-0000-1000-8000-00805F9B34FB";//通知
    readUUID = "0000FCF1-0000-1000-8000-00805F9B34FB";// 读UUID
    //开锁指令
    openGoodsLock(callback, number,) {

        // console.log('callback:', callback);
        action_type = OptionEnum.VendingOpenLock; //打开售卖机
        let buff_array = [];
        writeCallback = callback;
        num = number
        buff_array.push(0xa5); // 数据头
        buff_array.push(0x03); // 长度


        const timeoutSeconds = 8; //
        const result = secondsToTimeoutBytes(timeoutSeconds);
        // console.log('超时时间高八位:', result.highByte);
        // console.log('超时时间高八位:', result.highByte);
        if (number != 1) {
            console.log('number', number)
            buff_array.push(0xb2); // 出货多次
            let hexNum = '0x' + parseInt(number * 1).toString(16);
            buff_array.push(hexNum); // 出货次数

        } else {
            buff_array.push(0x01); // 机器
            buff_array.push(result.highByte); // 长度
        }

        buff_array.push(result.lowByte); // 长度
        console.log('buff_array:', buff_array)

        writeData(buff_array, '正在出货...');
    }
    handleResData(data) {
        // console.log("🚀 ~ action_type 当前操作类型", data, action_type)
        this.handleCallback(data)
    }
    handleCallback(resData) {
        console.log("🚀 ~ resData 写入指令回调", writeCallback, resData, action_type == OptionEnum.VendingOpenLock)
        if (action_type == OptionEnum.VendingOpenLock) { //操作的是 开锁
            /* #ifdef MP-ALIPAY */
            resData = resData.toLowerCase()

            /* #endif */
            console.log("🚀 ~ resData 写入指令回调", resData)
            if (resData == 'a5a5ff') {
                indexs++
                //超时
                writeCallback(false, '开启超时', num, indexs)
            } else if (resData == 'a5a500') {
                indexs++
                //正在忙碌
                writeCallback(false, '电机正忙', num, indexs)
            } else if (resData.substr(0, 4) == 'a5a5') {
                indexs = 0
                writeCallback(true, '出货成功')
            } else {
                indexs = 0
                // console.log('🚀 ~ resData 写入指', resData)
                writeCallback(false)
            }
            // writeCallback(resData == 'a5a5a5')
        } else if (action_type == OptionEnum.ReadRechargeCallback) {
            let mv = hexToVoltage(resData)
            //判断电量是否充足，如果电量不足，返回false
            readRechargeCallback(mv)
        }
        action_type = OptionEnum.None;
    }
    readChargeStatus(flag) {
        // 无线充						
        // console.log("🚀 ~ 湿纸巾   开始读状态 ", BluetoothDetail?.deviceId, BluetoothDetail?.serviceId, BluetoothDetail?.read_id)
        uni.readBLECharacteristicValue({
            // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
            deviceId: BluetoothDetail?.deviceId,
            // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
            serviceId: BluetoothDetail?.serviceId,
            // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
            characteristicId: BluetoothDetail?.read_id,
            success(res) {
                // console.log("🚀 ~ res readBLECharacteristicValue", res)
                action_type = flag ? OptionEnum.ReadRechargeCallback : OptionEnum.ReadRecharge;

                /* #ifdef MP-ALIPAY */
                res?.characteristic?.value && agreementBle.handleResData(res?.characteristic?.value);
                /* #endif */
                // console.log('readBLECharacteristicValue ', res);
                // console.log('readBLECharacteristicValue:', res.errCode)
            }, fail: (err) => {
                // console.log("🚀 ~ err readBLECharacteristicValue", err)
            }
        })


    }
}
//湿纸巾新主板
class vendingSixe {
    serviceUUID = "0000FFF0-0000-1000-8000-00805F9B34FB"
    writeUUID = "0000FFF2-0000-1000-8000-00805F9B34FB"; //写 UUID
    notifyUUID = "0000FFF1-0000-1000-8000-00805F9B34FB";//通知
    readUUID = "0000FFF1-0000-1000-8000-00805F9B34FB";// 读UUID
    //开锁指令
    openGoodsLock(callback, number) {

        ljl_number = number || 1;
        isRequest = false
        action_type = OptionEnum.GetRandom; //获取随机数
        writeCallback = callback;
        let buff_array = [];
        let str = 'AT+SN#'
        let arr = stringToPrefixedHexArray(str)
        for (let i = 0; i < arr.length; i++) {
            buff_array.push(arr[i])
        }
        getBLEDeviceMTU()
        console.log('输入指令', buf2hex(buff_array))
        writeData(buff_array, '正在出货...');


    }
    stop() {
        let buff_array = [];
        let str = 'AT+DISCONNECT#'
        let arr = stringToPrefixedHexArray(str)
        for (let i = 0; i < arr.length; i++) {
            buff_array.push(arr[i])
        }
        console.log('输入指令', buf2hex(buff_array))
        writeData(buff_array, '断开蓝牙...');
    }
    setNameLock(str) {
        let buff_array = [];
        let arr = stringToPrefixedHexArray(str)
        for (let i = 0; i < arr.length; i++) {
            buff_array.push(arr[i])
        }
        console.log('修改名称', buf2hex(buff_array))

        writeData(buff_array, '修改名字...');

    }

    handleResData(data, num, order_sn,) {
        console.log("🚀 ~ action_type 当前操作类型", action_type)
        this.handleCallback(data, num, order_sn)
    }
    handleCallback(resData, num, order_sn) {
        console.log("🚀 ~ resData 出货指令回调", resData, num, order_sn)

        if (action_type == OptionEnum.ReadElectricity) {
            console.log('readBLECharacteristicValue ', resData);
            btReadBattery(resData)
        } else {
            if (writeCallback != null) { //操作的是 开锁
                if (resData == 0) {
                    // console.log('num值', num)
                    let a = ''
                    indexs = 0
                    if (app.globalData.mtu < 100) {
                        a = 'a'
                    } else {
                        a = order_sn
                    }
                    newNumber = num
                    console.log('newNumber', newNumber, oldNumber)
                    writeCallback(true, '1', newNumber - oldNumber + 1, 0, isRequest, a)
                } else if (resData == 1) {
                    // console.log('num值', num)
                    let a = ''
                    indexs = 0
                    if (app.globalData.mtu < 100) {
                        a = 'a'
                    } else {
                        a = order_sn
                    }
                    newNumber = num
                    writeCallback(true, '2', newNumber - oldNumber + 1, 0, isRequest, a)
                } else if (resData == 2) {
                    indexs++
                    writeCallback(false, '开启超时', ljl_number, indexs)
                } else if (resData == 3) {
                    writeCallback(false, '密钥验证失败',)
                }
            }
        }
    }
    readBattery(flag) {
        // console.log('callback:', callback);
        action_type = OptionEnum.ReadElectricity; //打开售卖机
        // btOpenCallback = callback;
        let buff_array = [];
        let str = 'AT+ADC#'
        let arr = stringToPrefixedHexArray(str)
        for (let i = 0; i < arr.length; i++) {
            buff_array.push(arr[i])
        }
        console.log('输入指令', buf2hex(buff_array))
        writeData(buff_array, '读取电量...');
    }
}

// HLB系列蓝牙主板通讯协议_V1.2 (电机、履带)
class vendingSeven {
    serviceUUID = "0000FCF0-0000-1000-8000-00805F9B34FB"
    writeUUID = "0000FCF2-0000-1000-8000-00805F9B34FB"; //写 UUID
    notifyUUID = "0000FCF1-0000-1000-8000-00805F9B34FB";//通知
    readUUID = "0000FCF1-0000-1000-8000-00805F9B34FB";// 读UUID

    //开锁指令
    openGoodsLock(callback, number, order_sn) {
        action_type = OptionEnum.VendingOpenLock;
        writeCallback = callback;
        num = number;
        currentOrderSN = order_sn; // 保存订单号
        indexs = 0; // 重置重试次数

        let buff_array = [];
        buff_array.push(0xa5); // 数据头
        buff_array.push(0x02); // 长度：指令部分的字节数
        buff_array.push(parseInt(number) || 1); // data1: 电机编号，有效值为：1~6
        buff_array.push(3); // data2: 电机转动超时时间，单位为秒，有效值为：1~255

        console.log('HLB协议启动电机指令:', buf2hex(buff_array));
        writeData(buff_array, '正在出货...');
    }



    handleResData(data) {
        this.handleCallback(data)
    }

    handleCallback(resData) {
        console.log("🚀 ~ HLB协议 电机指令回调", resData)

        if (action_type == OptionEnum.VendingOpenLock) {
            /* #ifdef MP-ALIPAY */
            resData = resData.toLowerCase()
            /* #endif */

            // 根据HLB协议文档解析响应：0xa5 0x02 n1 n2
            // 响应格式可能是：0a01a5020101 (倒序) 或 a5020101
            let responseData = resData.toLowerCase();
            console.log("🔍 HLB响应数据分析:", responseData);

            // 查找a502模式
            let a502Index = responseData.indexOf('a502');
            if (a502Index !== -1) {
                // 从a502开始解析
                let dataStart = a502Index + 4; // 跳过a502
                if (dataStart + 4 <= responseData.length) {
                    const motorNum = responseData.substring(dataStart, dataStart + 2); // n1: 电机编号
                    const status = responseData.substring(dataStart + 2, dataStart + 4); // n2: 状态位

                    console.log("🔍 解析结果 - 电机编号:", motorNum, "状态:", status);
                    if (status === '01') {
                        // n2=0x01：电机执行成功
                        writeCallback(true, '出货成功', num, 0, false, currentOrderSN)
                    } else if (status === '00') {
                        // n2=0x00：电机未收到反馈信号，使用特殊消息避免重试
                        writeCallback(false, 'HLB_NO_RETRY', num, 5, false, currentOrderSN)
                    } else if (status === 'ff') {
                        // n2=0xff：电机正忙，视为失败
                        writeCallback(false, '设备忙碌，请稍后重试', num, 0, false, currentOrderSN)
                    } else {
                        // 其他未知状态码，视为失败
                        writeCallback(false, `设备响应异常: ${status}`, num, 0, false, currentOrderSN)
                    }
                } else {
                    writeCallback(false, '设备响应数据异常', num, 0, false, currentOrderSN)
                }
            } else {
                writeCallback(false, '设备响应格式错误', num, 0, false, currentOrderSN)
            }
        } else if (action_type == OptionEnum.ReadRechargeCallback) {
            // 读取电量回调
            let batteryPercent = this.hexToBattery(resData)
            readRechargeCallback(batteryPercent)
        }
        action_type = OptionEnum.None;
    }

    // 读取电量
    readChargeStatus(flag) {
        console.log("🚀 ~ HLB协议 开始读取电量")
        console.log("🔋 HLB协议 设备信息:", {
            deviceId: BluetoothDetail?.deviceId,
            serviceId: BluetoothDetail?.serviceId,
            characteristicId: BluetoothDetail?.read_id
        })

        uni.readBLECharacteristicValue({
            deviceId: BluetoothDetail?.deviceId,
            serviceId: BluetoothDetail?.serviceId,
            characteristicId: BluetoothDetail?.read_id,
            success(res) {
                console.log("🔋 HLB协议 读取电量成功，原始响应:", res)
                action_type = flag ? OptionEnum.ReadRechargeCallback : OptionEnum.ReadRecharge;
                console.log("🔋 HLB协议 设置action_type:", action_type, "flag:", flag)
                /* #ifdef MP-ALIPAY */
                res?.characteristic?.value && agreementBle.handleResData(res?.characteristic?.value);
                /* #endif */
            },
            fail: (err) => {
                console.log("🚀 ~ HLB协议 读取电量失败", err)
            }
        })
    }

    // 电量转换函数 - 基于线性比例关系
    hexToBattery(hexStr) {
        console.log("🔋 HLB协议 原始电量数据:", hexStr)
        console.log("🔋 HLB协议 数据长度:", hexStr.length)
        console.log("🔋 HLB协议 数据类型:", typeof hexStr)

        // 解析16bit ADC值（高位在前）
        const adcValue = parseInt(hexStr, 16);
        console.log("🔋 HLB协议 ADC值:", adcValue)
        console.log("🔋 HLB协议 ADC值(二进制):", adcValue.toString(2))
        console.log("🔋 HLB协议 ADC值(十六进制):", adcValue.toString(16))

        // 使用固定换算公式：十进制数据 ÷ 0.018，然后取整
        const conversionFactor = 0.018;
        console.log("🔋 HLB协议 换算系数:", conversionFactor)

        // 计算实际电压值
        const actualVoltage = adcValue / conversionFactor;
        console.log("🔋 HLB协议 计算过程:", adcValue, "÷", conversionFactor, "=", actualVoltage.toFixed(2))

        // 取整得到最终电压值
        const voltageValue = Math.round(actualVoltage);
        console.log("🔋 HLB协议 最终电压值:", voltageValue, "mV (", (voltageValue / 1000).toFixed(2), "V)")

        return voltageValue;
    }
}
const headBle = (type) => {

    switch (type) {
        case '0x88': {
            return new vendingOne()
        }
        case '0x89': {
            return new vendingTwo()
        }
        case '0x87': {
            return new rechargeKey()
        }
        case '0x82': {
            return new vendingFive
        }
        case '0x83': {
            return new vendingSixe()
        }
        case '0x67': {
            return new vendingSeven()
        }
        default: return new vendingTwo()
    }

}
//字符串转数组方法
const stringToPrefixedHexArray = (str) => {
    let hexArray = [];
    for (let i = 0; i < str.length; i++) {
        // 获取字符的Unicode码点  
        let code = str.charCodeAt(i);
        // 转换为两位十六进制字符串，并添加0x前缀  
        let hex = '0x' + code.toString(16).padStart(2, '0');
        hexArray.push(hex);
    }
    return hexArray;
}
//时间高低位转换
const secondsToTimeoutBytes = (seconds) => {
    // 确保输入是有效的数字
    if (typeof seconds !== 'number' || isNaN(seconds)) {
        console.error('输入不是有效的数字');
        return null;
    }

    // 将秒数限制在 0 到 600 之间
    const clampedSeconds = Math.max(0, Math.min(600, Math.floor(seconds)));

    // 计算高八位和低八位
    const highByte = (clampedSeconds >>> 8) & 0xFF;
    const lowByte = clampedSeconds & 0xFF;

    // 返回结果
    return {
        highByte: highByte,
        lowByte: lowByte
    };
}


var agreementBle = ''

/**
 * 初始化连接的参数
 * @param {*} data 是个对象，linkBleOption参数
 */
const initBleData = (data) => {
    _init = false;//是否初始化了
    _isSearchReach = false;//是否搜索到了
    action_type = OptionEnum.None;
    linkBleOption = {
        ...linkBleOption,
        ...data,
    }

    linkBleOption.device_type = linkBleOption?.device_type || app.globalData.device_type
    linkBleOption.device_sn = linkBleOption?.device_sn || app.globalData.device_sn
    linkBleOption.mkey = linkBleOption?.mkey || app.globalData.mkey
    // console.log('链接蓝牙设备号', linkBleOption)
    agreementBle = headBle(linkBleOption.device_type)
}


/**
 *  初始化蓝牙
 * @param {*} data 初始化数据  同linkBleOption参数
 */
const initBle = async (data) => {
    // console.log("🚀 ~ initBle 进入初始化")
    if (app.globalData.connected) return console.log("🚀 ~ initBle 已经连接了");
    if (_init) return console.log('🚀 ~ 已经初始化过了')
    initBleData(data);
    _init = true
    /* #ifdef MP-ALIPAY */
    try {
        await closeBle();
    } catch {
        console.log("🚀 ~ 关闭蓝牙失败")
    }
    /* #endif */
    appOptions.showLoading('初始化蓝牙', 'initBle start');
    uni.openBluetoothAdapter({
        success(res) {
            // if (!res?.isSupportBLE) {
            //     appOptions.hideLoading('initBle 抱歉，您的手机蓝牙暂不可用');
            //     appOptions.showModal({ content: '抱歉，您的手机蓝牙暂不可用' });
            //     return;
            // }
            appOptions.showLoading('蓝牙初始化成功', 'initBle success');
            startBluetoothDevicesDiscovery();
        },
        fail(error) {
            console.log; ("🚀 ~ error 蓝牙初始化失败", error)
            appOptions.hideLoading('蓝牙初始化失败', 'initBle fail', error);
            /* #ifdef MP-ALIPAY */
            if (error?.error == 12) {
                let rtnMsg = '蓝牙未打开';
                app.globalData?.SystemInfo?.platform == 'android' && (rtnMsg = '请检查手机蓝牙和定位是否打开')
                appOptions.showModal(rtnMsg)

            } else {
                appOptions.showModal(error?.errorMessage)
            }
            /* #endif */
            /* #ifndef MP-ALIPAY */
            bleErrState(error);
            /* #endif */
            offBluetoothDeviceFound();
            // uni.stopBluetoothDevicesDiscovery();
            closeBle();
        }
    });
}


/**
 * 开始搜寻附近的蓝牙外围设备。此操作比较耗费系统资源，请在搜索并连接到设备后调用 uni.stopBluetoothDevicesDiscovery 方法停止搜索。
 */
const startBluetoothDevicesDiscovery = () => {

    uni.startBluetoothDevicesDiscovery({
        allowDuplicatesKey: false,
        success(result) {
            // console.log("🚀 ~ result 开始搜寻附近的蓝牙外围设备", result)
            onBluetoothDeviceFound()
        },

        fail(error) {
            appOptions.hideLoading('搜索设备失败', '搜索设备失败 startBluetoothDevicesDiscovery fail', error);
            appOptions.showToast('搜索设备失败');
            // bleErrState(error);
            closeBle();
        }
    })
}
/**
 * 监听寻找到新设备的事件
 */
const onBluetoothDeviceFound = () => {
    appOptions.showLoading('搜索设备中', '搜索设备中 onBluetoothDeviceFound start');
    offBluetoothDeviceFound();//先停止监听获取新设备
    Timer && clearInterval(Timer)
    linkTimer(10).catch((error) => {
        appOptions.hideLoading('搜索设备超时', '搜索设备超时 onBluetoothDeviceFound fail', error);
        closeBle();
        return appOptions.showModal(`1、请查看周围是否有连接此设备的蓝牙
2、请先关闭手机蓝牙在打开一次
3、请检查位置信息是否打开
4、退出小程序重新扫码进入`)
    })
    //这里加loading主要是避免其他请求隐藏了loading,让loading重新显示
    let temporary = setTimeout(() => {
        appOptions.showLoading('搜索设备中', '搜索设备中 onBluetoothDeviceFound start');
        clearTimeout(temporary);
    }, 200);
    uni.onBluetoothDeviceFound(({ devices }) => {
        // console.log("🚀 ~ devices 开始搜索设备", devices, linkBleOption.device_sn)
        // let searchDevice = devices.find(item => item.name == linkBleOption.device_sn);

        let searchDevice = devices.find(item => {
            let name = ''
            if (item.localName) {
                // console.log('查询localName',item.localName,item.localName===linkBleOption.device_sn)
                name = item.localName
            } else if (!item.localName && item.name) {
                name = item.name
            } else if (!item.localName && !item.name && item.devices_name) {
                name = item.devices_name
            }
            item.name = name
            // console.log('item.name', item.name,name,name.indexOf(linkBleOption.device_sn) > -1)
            return name.indexOf(linkBleOption.device_sn) > -1
        });
        // console.log("🚀 ~ searchDevice  搜索到对应的设备信息", searchDevice?.name,searchDevice?.localName)
        if (searchDevice?.name == linkBleOption.device_sn && !_isSearchReach) {
            _isSearchReach = true;//搜索到了设备,不再让搜索进来
            Timer && clearInterval(Timer)
            temporary && clearTimeout(temporary)
            BluetoothDetail.deviceId = searchDevice.deviceId;
            // console.log("🚀 ~ searchDevice.advertisData", searchDevice.advertisData);
            if (searchDevice.advertisData) {
                BluetoothDetail.mac = ab2hex(searchDevice.advertisData).slice(4, 16);
            }
            // console.log("🚀 ~ BluetoothDetail", BluetoothDetail)

            appOptions.showLoading('搜索设备成功', '搜索设备成功 onBluetoothDeviceFound success');
            stopBluetoothDevicesDiscovery();//停止搜寻附近的蓝牙外围设备
            offBluetoothDeviceFound();//取消监听获取新设备
            //连接设备
            createBLEConnection()
        }
    })



}
//监听mtu变化
// const onBleMTuChageMtu=()=>{
//     uni.onBLEMTUChange(function (res) {
//         console.log('监听mtu',res.mtu);
//         MTU = res.mtu
//       });
// }

//获取蓝牙MTU
const getBLEDeviceMTU = () => {
    uni.getBLEMTU({
        deviceId: BluetoothDetail?.deviceId,
        writeType: "write",
        success: (res) => {
            console.log('获取到的mtu', res.mtu);
            if (res.mtu < 500) {
                uni.setBLEMTU({
                    deviceId: BluetoothDetail?.deviceId,
                    mtu: 215,
                    success: (res) => {
                        console.log('修改成功', res);
                        //   return res.mtu
                        app.globalData.mtu = res.mtu
                    },
                    fail: (err) => {
                        //   console.log('修改失败',err);
                        app.globalData.mtu = err.mtu
                    },
                });

            } else {
                app.globalData.mtu = res.mtu
            }
        },
        fail(error) {
            console.log('获取mtu失败', error);
            return flag
        }
    });

}

/**
 * 创建连接 连接蓝牙设备
 */
const createBLEConnection = () => {
    appOptions.showLoading('正在连接', '连接设备中 createBLEConnection start');
    console.log("🚀 ~ createBLEConnection 正在连接")
    return new Promise((resolve, reject) => {
        uni.createBLEConnection({
            deviceId: BluetoothDetail?.deviceId,
            success(result) {
                // console.log("🚀 ~ result 连接成功", result)
                getBLEDeviceMTU();//获取mtu
                // onBleMTuChageMtu();
                getBLEDeviceServices();//获取蓝牙所有服务
                onBLEConnectionStateChange();//监听连接状态
                resolve(result)
            },
            fail(error) {
                appOptions.hideLoading('连接设备失败', '连接设备失败 createBLEConnection fail', error);
                appOptions.showToast('连接设备失败')
                // bleErrState(error);
                closeBle();
                reject(error)
            }
        })
    })
}


/**
 * 监听连接状态
 */
const onBLEConnectionStateChange = () => {
    //防止重复调用监听事件，先关闭监听
    // console.log("🚀 ~ onBLEConnectionStateChange 监听连接状态")
    offBLEConnectionStateChange();
    uni.onBLEConnectionStateChange(({ deviceId, connected }) => {
        console.log("🚀 ~ connecte 监听连接状态", connected)
        if (!connected) {
            setBleConnected(connected)
            appOptions.hideLoading('设备断开连接', '设备断开连接 onBLEConnectionStateChange fail', connected, deviceId);
            appOptions.showToast('与设备连接已断开,请查看蓝牙设备是否打开');
            closeBle();
        }
    })
}
/**
 * 获取蓝牙所有服务
 */
const getBLEDeviceServices = () => {
    appOptions.showLoading('正在连接', '连接设备中 createBLEConnection start');
    // console.log("🚀 ~ getBLEDeviceServices 获取蓝牙所有服务")
    Timer && clearInterval(Timer)
    linkTimer(10).catch((error) => {
        appOptions.hideLoading('搜索设备超时', '搜索设备超时 onBluetoothDeviceFound fail', error);
        closeBle();
        return appOptions.showModal(`1、请查看周围是否有连接此设备的蓝牙
2、请先关闭手机蓝牙在打开一次
3、请检查位置信息是否打开
4、退出小程序重新扫码进入`)
    })
    uni.getBLEDeviceServices({
        deviceId: BluetoothDetail.deviceId,
        success({ services }) {
            // console.log("🚀 ~ services  获取服务", services)
            services?.filter(item => item.isPrimary)?.forEach(item => {
                let { uuid } = item
                let serviceFlag = false;
                /* #ifdef MP-ALIPAY */
                serviceFlag = (agreementBle?.serviceUUID)?.toLowerCase().includes(uuid.toLowerCase());
                /* #endif */
                /* #ifdef MP-WEIXIN */
                serviceFlag = uuid.toLowerCase() == (agreementBle?.serviceUUID)?.toLowerCase()
                /* #endif */
                serviceFlag && getBLEDeviceCharacteristics(BluetoothDetail?.deviceId, uuid)
                // console.log("🚀 ~ serviceFlag", serviceFlag)
            });
        },
        fail: (error) => {
            // console.log("🚀 ~ error 获取设备服务失败", error)
            appOptions.hideLoading('获取设备服务失败', '获取设备服务失败 getBLEDeviceServices fail', error);
            appOptions.showToast('获取设备服务失败')
            // bleErrState(error);
            closeBle();
        }, complete: () => {
            Timer && clearInterval(Timer)
        }
    })
}
/**
 * 获取蓝牙特征值
 * @param {*} deviceId 
 * @param {*} serviceId 
 */
const getBLEDeviceCharacteristics = (deviceId, serviceId) => {
    BluetoothDetail.serviceId = serviceId;
    // appOptions.showLoading('正在连接', '连接设备中 createBLEConnection start');
    uni.getBLEDeviceCharacteristics({
        deviceId,
        serviceId,
        success: ({ characteristics }) => {
            appOptions.hideLoading('获取设备特征值成功', '获取设备特征值成功 getBLEDeviceCharacteristics success', characteristics);
            appOptions.showToast('蓝牙连接成功', true);  // 🎯 恢复连接成功弹窗
            setBleConnected(true)
            characteristics?.forEach(item => {
                // console.log("循环特征值：", item);
                let uuid = "", notifyFlag = false, readFlag = false, writeFlag = false;
                /* #ifdef MP-ALIPAY */
                uuid = item.characteristicId;
                /* #endif */
                /* #ifndef MP-ALIPAY */
                uuid = item.uuid;
                /* #endif */
                const { indicate, notify, read, write } = item?.properties
                /* #ifdef MP-ALIPAY */
                notifyFlag = agreementBle?.notifyUUID && agreementBle?.notifyUUID?.toLowerCase().includes(uuid.toLowerCase());
                readFlag = agreementBle?.readUUID && agreementBle?.readUUID?.toLowerCase().includes(uuid.toLowerCase());
                writeFlag = agreementBle?.writeUUID && agreementBle?.writeUUID?.toLowerCase().includes(uuid.toLowerCase());
                /* #endif */
                /* #ifndef MP-ALIPAY */
                notifyFlag = agreementBle?.notifyUUID && uuid.toLowerCase() == agreementBle?.notifyUUID?.toLowerCase();
                readFlag = agreementBle?.readUUID && uuid.toLowerCase() == agreementBle?.readUUID?.toLowerCase();
                writeFlag = agreementBle?.writeUUID && uuid?.toLowerCase() == agreementBle?.writeUUID?.toLowerCase();
                /* #endif */
                if ((indicate || notify) && notifyFlag) {
                    //通知特征
                    BluetoothDetail.notify_id = uuid;
                    notifyBLECharacteristicValueChange();//开启通知
                }

                if (write && writeFlag) {

                    //写特征
                    BluetoothDetail.write_id = uuid;
                }
                if (read && readFlag) {
                    //读特征
                    BluetoothDetail.read_id = uuid;
                }
                // console.log("🚀 ~ BluetoothDetail", BluetoothDetail)
            })
            /* #ifndef MP-ALIPAY */
            onBLECharacteristicValueChange();
            /* #endif */
            action_type = OptionEnum.None
        },
        fail: (error) => {

            appOptions.hideLoading('获取设备特征值失败', '获取设备特征值失败 getBLEDeviceCharacteristics fail', error);
            appOptions.showToast('获取设备特征值失败')
            // bleErrState(error);
            closeBle();
        }

    })
}

/**
 * 开启蓝牙通知服务器
 */
const notifyBLECharacteristicValueChange = () => {
    // console.log("🚀 ~ notifyBLECharacteristicValueChange  调用通知Api", BluetoothDetail?.deviceId, BluetoothDetail?.serviceId, BluetoothDetail?.notify_id)
    uni.notifyBLECharacteristicValueChange({
        deviceId: BluetoothDetail?.deviceId,
        serviceId: BluetoothDetail?.serviceId,
        characteristicId: BluetoothDetail?.notify_id,
        state: true,
        success: (result) => {
            // console.log("🚀 ~ result 调用通知Api成功", result)

        },
        fail: (error) => {
            // console.log("🚀 ~ error 调用通知Api失败", error)

        }
    })
    // console.log("🚀 ~ BluetoothDetail?.deviceId", BluetoothDetail?.deviceId)

}

/**
 * 根据 uuid 获取处于已连接状态的设备
 */
const getConnectedBluetoothDevices = () => {
    return new Promise((resolve, reject) => {
        uni.getConnectedBluetoothDevices({
            services: [],
            success: (res) => {
                if (res?.devices?.length === 0) {
                    reject(res)
                    return;
                }
                resolve(res)
            },
            fail: (error) => {
                reject(error)
            }
        })
    })
}
/**
 * 监听低功耗蓝牙设备的特征值变化事件
 */
const onBLECharacteristicValueChange = () => {
    // console.log("🚀 ~  onBLECharacteristicValueChange  监听数据");
    offBLECharacteristicValueChange();//避免重复监听，先取消
    uni.onBLECharacteristicValueChange(({
        deviceId,
        serviceId,
        characteristicId,
        value
    }) => {
        let resData = null;
        // #ifdef MP-WEIXIN
        // console.log('没转之前的value', value)
        resData = ab2hex(value);
        // #endif
        // #ifdef MP-ALIPAY
        resData = value
        // #endif
        // console.log("🚀 ~ resData  设备返回特征值", resData)
        if (linkBleOption.device_type == '0x83') {
            let buff_array = [];
            // let AtStr=`AT+MWORK=1,1&${ljl_number}&${ljl_time}&${order_sn}#`
            let str = Buffer.from(resData, 'hex').toString('utf8').replace(/\r\n/g, '');
            console.log('写入指令返回', str)
            if (str.substring(0, 2) != 'AT' && action_type == OptionEnum.GetRandom && resData.length != 24) {
                let md5 = generateHMACMD5(str, linkBleOption.mkey)
                console.log('md5', str, md5)
                let buff_array = [];
                let AtStr = 'AT+SD=' + md5 + '#'
                let arr = stringToPrefixedHexArray(AtStr)
                for (let i = 0; i < arr.length; i++) {
                    buff_array.push(arr[i])
                }
                //验证密钥
                action_type = OptionEnum.VerificateKey
                console.log('准备发送md5', buf2hex(buff_array), action_type)
                console.log('验证密钥')
                writeData(buff_array, '验证密钥');
            } else if (str.substring(0, 7) == 'AT+OK:0' && action_type == OptionEnum.VerificateKey) {
                console.log('验证密钥成功', str)
                console.log('订单号', order_sn)
                let a = ''
                if (app.globalData.mtu < 100) {
                    a = 'a'
                } else {
                    a = order_sn
                }
                console.log('a', a, app.globalData.mtu)
                let AtStr = `AT+MWORK=1,1&${ljl_number}&${ljl_time}&${a}#`
                let arr = stringToPrefixedHexArray(AtStr)
                for (let i = 0; i < arr.length; i++) {
                    buff_array.push(arr[i])
                }

                console.log('准备发送出货指令', AtStr)
                console.log('准备发送出货指令', AtStr)
                //发送出货
                action_type = OptionEnum.cleaStatus
                writeData(buff_array, '正在出货...');
            } else if (str.substring(0, 7) == 'AT+OK:1' && action_type == OptionEnum.VerificateKey) {
                //密钥验证失败
                agreementBle.handleResData(3); //处理返回数据

            } else if (action_type == OptionEnum.cleaStatus && str.substring(0, 10) == 'AT+OPEN:0,') {
                if (!isRequest) {
                    oldNumber = str.substring(10).split(',')[0]
                    console.log('进来了旧值', oldNumber)
                }
                // console.log('返回次数', str,str.substring(10), str.substring(10).split(',')[0],str.substring(10).split(',')[1])
                agreementBle.handleResData(0, str.substring(10).split(',')[0], str.substring(10).split(',')[1]); //处理返回数据

                isRequest = true

            } else if (action_type == OptionEnum.cleaStatus && str.substring(0, 11) == 'AT+CLOSE:0,') {
                //回退完成
                // console.log('返回次数', str,str.substring(11), str.substring(11).split(',')[0],str.substring(11).split(',')[1])
                agreementBle.handleResData(1, str.substring(11).split(',')[0], str.substring(11).split(',')[1]); //处理返回数据
            } else if (action_type == OptionEnum.cleaStatus && str.substring(0, 10) == 'AT+OPEN:2,') {
                agreementBle.handleResData(2); //处理返回数据
            } else if (action_type == OptionEnum.cleaStatus && str.substring(0, 11) == 'AT+CLOSE:2,') {
                agreementBle.handleResData(2); //处理返回数据
            }
            if (action_type == OptionEnum.ReadElectricity && str.split(',').length < 2 && str.substring(0, 6) == 'AT+OK:') {
                let electric = str.substring(6) * 10
                console.log('电量', electric, str.substring(6))
                agreementBle.handleResData(electric); //处理返回数据
            }
        } else {

            agreementBle.handleResData(resData)
        }
    })

}


/**
 * 写入数据
 * @param {*} hex  写入数据
 * @param {*} title 提示文字
 */
const writeData = (hex, title = '正在写入指令') => {
    if (title) {
        appOptions.showLoading(title, 'writeData');
    }
    let enDataBuf = new Uint8Array(hex);
    let buffer1 = enDataBuf.buffer;
    // console.log("🚀 ~ BluetoothDetail?.write_id", BluetoothDetail?.write_id)
    // console.log("🚀 ~ BluetoothDetail?.deviceId", BluetoothDetail?.deviceId)
    uni.writeBLECharacteristicValue({
        deviceId: BluetoothDetail?.deviceId,
        serviceId: BluetoothDetail?.serviceId,
        characteristicId: BluetoothDetail?.write_id,
        value: buffer1,
        success: (res) => {
            // console.log("🚀 ~ res 写入指令成功", res)
            // appOptions.hideLoading('写入指令成功', res);
            // appOptions.showToast('写入指令成功', true)
            if ([OptionEnum.ReadRecharge, OptionEnum.ReadRechargeCallback, OptionEnum.Recharge].includes(action_type)) {
                agreementBle?.readChargeStatus();//无线充的读取
                /* #ifndef MP-ALIPAY */
                onBLECharacteristicValueChange();
                /* #endif */
            } else {
                onBLECharacteristicValueChange();
                // console.log('action_type', OptionEnum.ReadRecharge, OptionEnum.ReadRechargeCallback, action_type)

            }

        },
        fail(err) {
            // console.log("🚀 ~ err 写入指令失败", err)
            appOptions.hideLoading('写入数据失败 writeData', err);
            appOptions.showToast('写入指令失败');
        }
    })
}

/**
 * 关闭蓝牙模块
 */
const closeBle = () => {
    initBleData()
    return new Promise((resolve, reject) => {
        if (BluetoothDetail?.deviceId) {
            uni.closeBLEConnection({
                deviceId: BluetoothDetail?.deviceId,
                success: (result) => { },
                fail: (error) => { }
            })
        }
        uni.closeBluetoothAdapter({
            success(result) {
                if (Timer) {
                    clearInterval(Timer)
                    Timer = null
                }
                setBleConnected(false);
                offBLEConnectionStateChange();
                offBLECharacteristicValueChange();
                offBluetoothAdapterStateChange();
                offBluetoothDeviceFound();
                stopBluetoothDevicesDiscovery();
                resolve(result);
            },
            fail(error) {
                // console.log("🚀 ~ error 关闭蓝牙失败", error)
                reject(error)
            }
        })
    })


}

/**
 * 停止搜寻附近的蓝牙外围设备
 */
const stopBluetoothDevicesDiscovery = () => {
    uni.stopBluetoothDevicesDiscovery({
        success: (result) => {
            // console.log("🚀 ~ result  停止搜索",)

        },
        fail: (error) => { }
    })
}


//取消监听事件》》》》》》》》》》》  start
//取消监听蓝牙连接状态的改变事件
const offBLEConnectionStateChange = () => {

    /* #ifdef MP-ALIPAY */
    my.offBLEConnectionStateChanged();
    /* #endif */

    /* #ifdef MP-WEIXIN */
    wx.offBLEConnectionStateChange()
    /* #endif */

}

//取消监听蓝牙设备的特征值变化事件
const offBLECharacteristicValueChange = () => {
    /* #ifdef MP-ALIPAY */
    my.offBLECharacteristicValueChange();
    /* #endif */
    /* #ifdef MP-WEIXIN */
    wx.offBLECharacteristicValueChange()
    /* #endif */
}
//取消监听蓝牙适配器状态变化事件
const offBluetoothAdapterStateChange = () => {
    /* #ifdef MP-ALIPAY */
    my.offBluetoothAdapterStateChange();
    /* #endif */
    /* #ifdef MP-WEIXIN */
    wx.offBluetoothAdapterStateChange()
    /* #endif */
}
//取消监听寻找到新设备的事件
const offBluetoothDeviceFound = () => {
    /* #ifdef MP-ALIPAY */
    my.offBluetoothDeviceFound();
    /* #endif */
    /* #ifdef MP-WEIXIN */
    wx.offBluetoothDeviceFound()
    /* #endif */
}
//取消监听事件<《《《《《《《《《《《  end


/**
 * 蓝牙 API 错误码对照表
 * @param {*} errorCodeOption 错误信息
 */
const bleErrState = (errorCodeOption) => {
    //{error: 12, errorMessage: '蓝牙未打开'}
    //{errno: 1500102, errMsg: "openBluetoothAdapter:fail open fail", state: 4, errCode: 10001}
    let errorCode = null, rtnMsg = ''
    errorCode = errorCodeOption?.errCode
    if (errorCode) {
        switch (errorCode) {
            case 10001: rtnMsg = '蓝牙未打开';
                app.globalData?.SystemInfo?.platform == 'android' && (rtnMsg = '请检查手机蓝牙和定位是否打开')
                appOptions.showModal(rtnMsg)
                break;
            case 10002:
                appOptions.showToast('没有找到指定设备')
                break;
            case 10003:
                appOptions.showToast('连接失败')
                break;
            case 10004:
                appOptions.showToast('没有找到指定服务')
                break;
            case 10005:
                appOptions.showToast('没有找到指定特征')
                break;
            case 10006: appOptions.showToast('当前连接已断开')
                break;
            case 10007: appOptions.showToast('当前特征不支持此操作')
                break;
            case 10008:
                appOptions.showToast('其余所有系统上报的异常')
                break;
            case 10009:
                appOptions.showToast('系统版本低于 4.3 不支持蓝牙')
                break;
            case 100010:
                appOptions.showToast('蓝牙已连接')
                break;
            case 100011:
                appOptions.showToast('配对设备需要配对码')
                break;
            case 100012:
                appOptions.showToast('连接超时')
                break;
            case 10013:
                appOptions.showToast('连接设备格式不正确')
                break;
        }
    } else {
        errorCodeOption?.errMsg && appOptions.showToast(errorCodeOption?.errMsg)
    }

}

/**
 * 一个定时器 
 * @param {*} time 定时器
 * @returns 
 */
let Timer = null;
const linkTimer = (time) => {
    return new Promise((resolve, rej) => {
        let _time = 0
        Timer = setInterval(function () {
            if (_time < time) {
                _time++

            } else {
                rej(false)
                clearInterval(Timer)
            }
        }, 1000)
    })
}
/**
 * 设置蓝牙连接状态
 * @param {*} connected 
 */
const setBleConnected = (connected = false) => {
    !connected && (_init = false)
    app.globalData.connected = connected
    uni.$emit(globalEvents.EVENT_BLE_CONNECT_CHANGED, {
        connected: connected,
    })
}


//一些操作
//读取充电状态
const readChargeStatus = (callback) => {
    // console.log("🚀 ~ readChargeStatus 读状态",)
    agreementBle.readChargeStatus(true);
    readRechargeCallback = callback
}

//读取电量
function readElectric(callback) {
    btReadBattery = callback
    agreementBle.readBattery(true);
}

/**
 * 设备执行充电
 * @param {*} time 充电分钟
 * @param {*} callback 充电回调
 * @param {*} hour 充电小时
 */
const rechargeDevice = (time, callback, hour = 0) => {
    agreementBle.rechargeDevice(time, hour, callback)
}
const openGoodsLock = (callback, num, sn) => {
    order_sn = sn
    console.log('开启传送的值', num, order_sn)
    agreementBle.openGoodsLock(callback, num, order_sn)
}

//一些处理方法
// 二进制处理方法 buf转16进制
const buf2hex = (buffer) => {
    return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('')
}
// ArrayBuffer转16进度字符串示例
const ab2hex = (buffer) => {
    if (!(buffer instanceof ArrayBuffer)) {
        throw new TypeError('Expected an ArrayBuffer');
    }
    const bytes = new Uint8Array(buffer);
    const hexArr = Array.from(bytes, byte => ('00' + byte.toString(16)).slice(-2));
    return hexArr.join('');
}
//md5生成方法
function generateHMACMD5(sn, key) {
    // 创建一个HMAC hash  
    const hmac = crypto.createHmac('md5', key);

    // 更新HMAC hash的数据  
    hmac.update(sn);

    // 计算摘要  
    const digest = hmac.digest('hex');

    // 返回前12个字符  
    return digest.toUpperCase().substring(0, 12);
}

//计算电压
const hexToVoltage = (hexStr) => {
    // 将十六进制字符串转换为整数
    let voltageInt = parseInt(hexStr, 16);
    return voltageInt;
}


// 获取MAC地址
const buf2Mac = (buffer, join = '') => {
    // #ifdef MP-WEIXIN
    let bytes = new Uint8Array(buffer);
    return Array.from(bytes, bit => bytes[bit]).join(join);
    // #endif
    // #ifdef MP-ALIPAY
    return buffer.match(/[\da-f]{2}/gi).join(join);
    // #endif
}
// 清除冒号
const clearSymbol = (str) => {
    str = str.replace(/:/g, ""); //取消字符串中出现的所有冒号
    return str;
}
/**
 * 前面自动补零
 *  num传入的数字，n需要的字符长度
 */
const prefixZero = (num, n) => {
    return (Array(n).join(0) + num).slice(-n);
}
//按个数切割数组
const getStrArr = (str) => {
    var strArr = [];
    var n = 2;
    for (var i = 0, l = str.length; i < l / n; i++) {
        let a = str.slice(n * i, n * (i + 1));
        strArr.push(a);
    }
    return strArr
}
// ASCII码转16进制
const strToHexCharCode = (str) => {
    if (str === "") {
        return "";
    } else {
        var hexCharCode = [];
        for (var i = 0; i < str.length; i++) {
            hexCharCode.push((str.charCodeAt(i)).toString(16));
        }
        return hexCharCode.join("");
    }
}
//十六进制转ASCII码
const hexCharCodeToStr = (hexCharCodeStr) => {
    var trimedStr = hexCharCodeStr.trim();
    var rawStr = trimedStr.substr(0, 2).toLowerCase() === "0x" ? trimedStr.substr(2) : trimedStr;
    var len = rawStr.length;
    if (len % 2 !== 0) {
        // console.log("存在非法字符!");
        return "";
    }
    var curCharCode;
    var resultStr = [];
    for (var i = 0; i < len; i = i + 2) {
        curCharCode = parseInt(rawStr.substr(i, 2), 16);
        resultStr.push(String.fromCharCode(curCharCode));
    }
    return resultStr.join("");
}

/*
    16进制转10进制
*/
const to10 = (str) => {
    str = "0x" + str
    return parseInt(str, 16)
}
/*
    10进制转16进制
*/
const to16 = (num) => {
    return num.toString(16)
}

//随机数
const getRandom = (min, max) => {
    return Math.floor(Math.random() * (max - min)) + min
}

export default {
    initBleData,//初始化需要连接的参数
    initBle,//蓝牙初始化
    readChargeStatus,//读取充电状态
    rechargeDevice,//充电
    closeBle,//关闭蓝牙
    openGoodsLock,//出货
    readElectric,//读取电量
    getBLEDeviceMTU,//获取并改变MTU值

}