<template>
    <!--  订单列表项-->
    <view class="comContent" @click="gotoOrderDetail">
        <view class="topLine ">
            <view class="leftNo">订单编号：{{ itemInfo.order_sn }}</view>
            <view class="rightStatus">{{ status[itemInfo.this_status] || '订单异常' }}</view>
        </view>

        <view class="bottomInfo">
            <view class="left">
                <image class="cover" :src="itemInfo.goods_img"></image>
            </view>

            <view class="rightInfo">
                <view class="name textMaxOneLine">
                    {{ itemInfo.goods_name }}
                </view>
                <view class="count">x{{ itemInfo.outnum }}</view>
                <view class="count">{{ orderType[itemInfo.prom_type] }}</view>
                <view class="count">{{ itemInfo.add_time }}</view>
                <view class="priceLine ">
                    <view class="realPay ">
                        <view class="label">实付：</view>
                        <view class="payValue">
                            <view class="value">{{ itemInfo.prom_type == 1 ? itemInfo.order_amount : '0.00' }}</view>
                            <view class="unit">元</view>
                        </view>
                    </view>

                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "OrderCard",
    props: {
        itemInfo: Object,
    },

    data() {
        return {

            status: {
                1: '已完成',
                2: '订单异常',
                3: '订单已取消',
                4: '订单异常',
                50: '退款中',
                0: '待付款',
                60: '已出货',
                70: '已退款',
                80: '出货失败',
            },
            orderType: {
                1: '普通订单',
                2: '免费订单',
                3: '免费订单',
                5: '积分订单'

            }
        };
    },

    methods: {
        gotoOrderDetail() {
            uni.navigateTo({
                url: `/pagesB/order/OrderDetails?from=orderList&order_sn=${this.itemInfo.order_sn}`,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.comContent {
    background: white;
    box-shadow: 0px 3rpx 7rpx 0px rgba(32, 32, 32, 0.15);
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;

    .topLine {
        @include flexRowVertCenter();

        .leftNo {
            color: $textGray;
            font-size: $font-size-xsmall;
        }

        .rightStatus {
            margin-left: auto;
            font-size: $font-size-xsmall;
            color: $mainRed;
        }
    }

    .bottomInfo {
        margin-top: 30rpx;
        display: flex;
        height: 190rpx;

        .left {
            width: 190rpx;
            height: 190rpx;
            border-radius: 10rpx;
            flex-shrink: 0;

            .cover {
                width: 100%;
                height: 100%;
                border-radius: 10rpx;
            }
        }

        .rightInfo {
            flex: 1;
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            height: 190rpx;
            overflow: hidden;

            .name {
                font-size: $font-size-xlarge;
                color: $textBlack;
                font-weight: bold;
            }

            .count {
                margin-top: auto;
                font-size: $font-size-xsmall;
                color: $textDarkGray;
            }

            .priceLine {
                @include flexRowVertCenter();
                margin-top: auto;

                .realPay {
                    @include flexRowVertCenter();

                    .label {
                        font-size: $font-size-xsmall;
                        color: $textDarkGray;
                    }

                    .payValue {
                        margin-left: 0rpx;
                        color: $mainRed;
                        white-space: nowrap;
                        display: flex;
                        align-items: flex-end;

                        .value {
                            font-size: $font-size-xlarge;
                            font-weight: bold;
                        }

                        .unit {
                            font-size: $font-size-xsmall;
                            margin-left: 8rpx;
                            font-weight: bold;
                            margin-bottom: 5rpx;
                        }
                    }
                }

                .btn {
                    margin-left: auto;
                }
            }
        }
    }
}
</style>