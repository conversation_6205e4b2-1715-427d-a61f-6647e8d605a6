<template>
    <view class="searchContent"
        @click="click">
        <u-search class="searchValue" placeholder-color="#999999" bg-color="#F2F2F2" height="30"
            :placeholder="placeholder" :shape="shape" :clearabled="clear" :animation="true" :show-action="false"
            :disabled="disabled" v-model="keyword" @search="search" @focus="focus" @blur="clears"></u-search>
    </view>
</template>

<script>

export default {
    // components: { BaseIcon },
    name: 'ShopSearch',
    props: {
        placeholder: { type: String | Object, default: '请输入搜索内容' }, //提示文字
        shape: { type: String, default: 'square' }, //是否圆角
        clear: { type: Boolean, default: false }, //显示清除控件
        typeImg: { type: String | Object, default: '' }, //右侧icon类型
        showScan: { type: Boolean, default: false }, //是否显示扫码icon
        width: { type: [Number, String], default: '100' },
        disabled: { type: <PERSON>olean, default: false },
        hotelName: { type: String, default: '' },
        listType: { type: String, default: 'analysisList' },
        isShowSerch: { type: <PERSON>olean, default: true },//是否加搜索提示
    },
    data() {
        return {
            keyword: '', // 查询数据
            isShow: false
        }
    },
    mounted() {
        this.keyword = this.hotelName
    },
    watch: {
        hotelName: {
            handler: function () {
                if (this.hotelName) {
                    // 检查 this.leftImg 是否存在
                    this.keyword = this.hotelName
                } else {
                    this.keyword = ''
                }
            },
            deep: true,
            immediate: true,
        },
    },
    options: { styleIsolation: 'isolated' }, //组件必须加,才能修改内部样式
    methods: {
        onClickScan() {
            uni.scanCode({
                onlyFromCamera: false,
                scanType: ['qrCode', 'barCode'],
                success: ({ result, scanType, charSet, path }) => {
                    if (!result) return this.isShowErr('请扫描正确二维码~')
                    result = decodeURIComponent(result)
                    let option = {},
                        vscode,
                        device_sn,
                        mid
                    if (result.includes('vscode')) {
                        vscode = getUrlParams(result, 'vscode')
                        this.keyword = vscode
                    } else if (result.includes('mid')) {
                        mid = getUrlDynamicData(result, 'mid')
                    } else {
                        if (result.includes('device_sn')) {
                            device_sn = getUrlDynamicData(result, 'device_sn')
                        } else {
                            device_sn = result
                        }
                        this.keyword = device_sn
                    }
                    option = {
                        vscode,
                        device_sn,
                        mid,
                    }
                    this.$emit('onClickScan', result, option)
                },
                fail: (error) => {
                    if (error?.errMsg.includes('cancel')) return
                    this.isShowErr('请扫描正确二维码~')
                },
            })
        },
        onClickIconImg(e) {
            this.$emit('onClickIcon', e)
        },
        search(val) {
            if (!val) {
                return
            }
            this.$emit('search', val)
        },

        click() {
            this.$emit('click')
        },
        searchClick(val) {
            this.keyword = val
            this.search(val)
        },
        exit(val) {
            this.$emit('exit', val)
           
        },
        focus() {
            this.isShow = true
        },
        clears() {
            this.isShow = false
        }
    },
}
</script>

<style lang="scss" scoped>
.searchContent{
    border: 2rpx solid rgb(211, 211, 211);
    margin: 10rpx 20rpx;
}
</style>