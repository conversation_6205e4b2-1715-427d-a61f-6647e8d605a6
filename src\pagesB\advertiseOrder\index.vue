<template>
    <view>
        <BaseTabs :list="orderTypeNav" @change="onTabChangeType" />
        <CommonAd :ad="vAd.personalCenterListCustomAd" type="custom" />
        <ComList :loadingType="loadingType">
            <AdvertiseOrderCard v-for="item in listData" :key="item.id" :info="item" />
        </ComList>
        <LoginPopup />
    </view>
</template>

<script>
import ComList from "../../components/list/ComList.vue";
import { getMemberApplyAdList } from "@/common/http/api";
import myPull from "@/mixins/myPull.js";
import AdvertiseOrderCard from '../components/AdvertiseOrderCard.vue';
import BaseTabs from '../../components/base/BaseTabs.vue';
import LoginPopup from '../../components/LoginPopup.vue';
export default {
    components: { ComList, AdvertiseOrderCard, BaseTabs, LoginPopup },

    data() {
        return {
            orderTypeNav: [
                {
                    name: "全部",
                    status: "",
                },
                {
                    name: "待审核",
                    status: 0,
                },
                {
                    name: "已审核",
                    status: 1,
                },
            ],
            curTabInfo: {},
        };
    },
    methods: {
        onTabChangeType(e) {
            this.curTabInfo = e;
            this.refresh();
        },
        getList(page, done) {
            let data = {
                page,
                status: this.curTabInfo.status,
            };
            getMemberApplyAdList(data).then((res) => {
                done(res);
            });
        },
    },
    onLoad(opt) {
        this.refresh();
    },
    mixins: [myPull()],
};
</script>
<style lang="scss">
page {
    background-color: #f4f4f4;
}
</style>
<style lang="scss" scoped>
.orderTypeBar {
    background-color: #fff;
    padding-bottom: 6rpx;
}
</style>
