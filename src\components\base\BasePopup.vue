<template>
    <u-popup :show="newShow" :mode="mode" :closeable="closeable" :safeAreaInsetBottom="safeArea" @close="close">
        <slot />
    </u-popup>
</template>
<script>
export default {
    props: {
        show: { type: Boolean, default: false },
        mode: { type: String, default: 'bottom' },
        closeable: { type: Boolean, default: false },//是否显示关闭图标
        safeArea: { type: Boolean, default: true },
    },
    computed: {
        newShow: {
            get: function () {
                return this.show
            },
            set: (val) => {
                console.log("🚀 ~ val", val)
                this.$emit("update:show", val)
            }
        }
    },

    data() {
        return {

        };
    },

    methods: {
        close() {
            this.$emit("update:show", false)
        }
    },

}
</script>


<style scoped  lang='scss'>
</style>