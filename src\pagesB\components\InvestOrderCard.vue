<template>
    <view class="card">
        <view class="content">
            <view class="content-box">
                <view class="content-box-title">订单编号：</view>
                <view class="content-box-txt">{{ info.order_sn }}</view>
            </view>
            <view class="content-box">
                <view class="content-box-title">投资编号：</view>
                <view class="content-box-txt">{{
                        info.device_sn
                }}</view>
            </view>
            <view class="content-box">
                <view class="content-box-title">投资年限：</view>
                <view class="content-box-txt">{{ info.year }}</view>
            </view>
            <view class="content-box">
                <view class="content-box-title">投资金额：</view>
                <view class="content-box-txt">{{ info.amount }}</view>
            </view>
            <view class="content-box">
                <view class="content-box-title">预留号码：</view>
                <view class="content-box-txt">{{ info.phone }}</view>
            </view>
            <view class="content-box">
                <view class="content-box-title">投资时间：</view>
                <view class="content-box-txt">
                    <view>
                        {{ $u.timeFormat(info.start_time * 1000, "yyyy-mm-dd hh:MM:ss") }}
                    </view>
                    <view>
                        {{ $u.timeFormat(info.end_time * 1000, "yyyy-mm-dd hh:MM:ss") }}
                    </view>
                </view>
            </view>
        </view>
        <view class="user" v-if="info.status >= 2">
            <view class="user-box">
                <view class="user-box-item">
                    <view class="title">投资账号：</view>

                    <view>
                        <u-link :style="{ lineHeight: 1.5 }" :text="info.user_login" mpTips="账号已复制,请登录管理后台查看数据">
                        </u-link>
                    </view>
                </view>
                <view class="user-box-item">
                    <view class="title">投资密码：</view>
                    <view class="txt">123456</view>
                </view>
            </view>
            <view>
                <BaseButton text="前往登录" shape="circle" @onClick="goManage" />
            </view>
        </view>
    </view>
</template>
<script>
import BaseButton from "../../components/base/BaseButton.vue";
export default {
    props: {
        info: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {};
    },
    methods: {
        goManage() {
            uni.navigateToMiniProgram({
                appId: "wx50be0622d644a4ee",
                path: `/pages/login/Login?from=user&userName=${this.info.user_login}&passWord=123456`,
                success: (result) => { },
                fail: (error) => { },
            });
        },
    },
    onLoad() { },
    components: { BaseButton },
};
</script>

<style scoped lang="scss">
.card {
    padding: 20rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
}

.content {
    &-box {
        display: flex;
        line-height: 1.6;
        font-size: 30rpx;

        &-title {
            color: #666;
            flex-shrink: 0;
        }

        &-txt {
            color: #333;
        }
    }
}

.user {
    display: flex;
    justify-content: space-between;

    .user-box {
        &-item {
            display: flex;
            align-items: center;

            .title {
                color: #666;
                flex-shrink: 0;
            }

            .txt {
                color: #333;
            }
        }
    }
}
</style>
