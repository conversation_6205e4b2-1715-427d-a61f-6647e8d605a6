<template>
    <view class="container" :style="{ paddingBottom: vIphoneXBottomHeight + 'rpx' }">
        <view class="tabbar">
            <view v-for="item in tabbar" :key="item.name" class="tabbar-item" @click="go(item.path)">
                <button open-type="contact" class="tabbar-item" v-if="item.name == '客服'">
                    <BaseIcon :name="item.icon" size="26" color="#333333" />
                    <view class="name">{{ item.name }}</view>
                </button>
                <block v-else>
                    <BaseIcon :name="item.icon" size="26" color="#333333" />
                    <view class="name">{{ item.name }}</view>
                </block>
            </view>
        </view>
    </view>
</template>
<script>
import BaseIcon from '@/components/base/BaseIcon.vue';
export default {
    components: { BaseIcon },
    name: 'Tabbar',
    data() {
        return {
            tabbar: [
                { name: '首页', icon: 'home', path: '/pages/index/index' },
                { name: '客服', icon: 'kefu-ermai', },
                { name: '订单', icon: 'order', path: '/pagesB/order/Order' },
                { name: '我的', icon: 'account', path: '/pagesC/profile/Profile' },
            ]
        };
    },

    methods: {
        go(url) {
            if (!url) return
            uni.navigateTo({ url })
        }

    },

}
</script>


<style lang="scss">
/*当button里面包含图文时，图文之间会有间距，消除间距的方式是首先对button采用flex竖向布局，然后将子元素的margin和padding设置为0*/
button {
    &:after {
        border: none;
    }

    .button[plain] {
        border: none;
    }

    &:first-child {
        margin-top: 0;
    }

    &:active {
        background-color: white;
    }

    background-color: white;
    border-radius: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
</style>
<style scoped  lang='scss'>
.container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
}

.tabbar {
    display: flex;
    justify-content: space-between;
    height: 100rpx;

    &-item {
        width: 100%;
        border: 0;
        flex: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .name {
            font-size: $font-size-base;
            line-height: 1.2 !important;
        }
    }
}
</style>