import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

let lifeData = {};
try {
    // 尝试获取本地是否存在lifeData变量，第一次启动APP时是不存在的
    lifeData = uni.getStorageSync('lifeData');
} catch (e) {

}
// 需要永久存储，且下次APP启动需要取出的，在state中的变量名
let saveStateKeys = ['vSession3rd', 'vUserInfo', 'vIsLogin', 'vIsLocation', 'vIsConnected', 'vMemberInfo', 'vPayStart', 'vBaseURL'];

// 保存变量到本地存储中
const saveLifeData = function (key, value) {
    // 判断变量名是否在需要存储的数组中
    if (saveStateKeys.indexOf(key) != -1) {
        // 获取本地存储的lifeData对象，将变量添加到对象中
        let tmp = uni.getStorageSync('lifeData');
        // 第一次打开APP，不存在lifeData变量，故放一个{}空对象
        tmp = tmp ? tmp : {};
        tmp[key] = value;
        // 执行这一步后，所有需要存储的变量，都挂载在本地的lifeData对象中
        uni.setStorageSync('lifeData', tmp);
    }
}

const store = new Vuex.Store({
    // 下面这些值仅为示例，使用过程中请删除
    state: {
        // 如果上面从本地获取的lifeData对象下有对应的属性，就赋值给state中对应的变量
        // 加上vuex_前缀，是防止变量名冲突，也让人一目了然
        // vuex_user: lifeData.vuex_user ? lifeData.vuex_user : {name: '明月'},
        // vuex_token: lifeData.vuex_token ? lifeData.vuex_token : '',
        // 如果vuex_version无需保存到本地永久存储，无需lifeData.vuex_version方式
        // vuex_version: '1.0.1',
        vBaseURL: 'https://szj.bjllz.com',
        // vBaseURL: 'https://hd.handaiwulian.com',
        vIphoneXBottomHeight: 0,//iphonex 底部安全区域高度
        vUserInfo: lifeData.vUserInfo || {},//用户信息
        vIsLogin: lifeData.vIsLogin ?? false,//是否已经登录
        vSession3rd: lifeData.vSession3rd,//用户Token
        // vTel: '4000-027-115',//客服热线
        vSiteConfig: {},//网站配置信息
        vAppId: '',//Appid
        vCurLocation: {
            longitude: '',//经度
            latitude: '',//纬度
        },
        vIsLocation: JSON.parse(lifeData.vIsLocation ?? false) ?? false,//是否获取到地理位置信息
        vServicePhone: "",//联系电话
        vAppName: lifeData.vAppName || '乐乐纸',//小程序名称
        vDeviceSn: '',//保存的全局设备编号
        vDeviceType: '',//设备类型
        vPointInfo: {},//位置信息
        vCreateOrderInfo: {},//创建的订单信息
        vBaseInfo: {},//网站配置信息
        vRewardedVideoAd: "",//激励视频广告引用
        vMemberInfo: lifeData.vMemberInfo, // 会员信息，后台服务器的用户信息， vUserInfo 是授权获取的信息
        vAd: {
            //各种广告id
            //激励视频广告id
            rewardedVideoAdId: "",
            //首页插屏广告位
            homeInsertScreenAd: "",
            //我的个人中心列表
            personalCenterInsertScreenAd: "",
            //我的出袋订单详情插屏广告
            myOutBagDetailsInsertScreenAd: "",
            //招募合伙人页面插屏广告
            companionInsertScreenAd: "",

            //个人中心banner广告位
            personalCenterBannerAd: "",
            //购买商品 免费领取页面banner广告
            buyProductBannerAd: "",
            //商铺详情页banner广告
            pointPlaceDetailsBannerAd: "",
            //个人中心列表页原生广告位
            personalCenterListCustomAd: "",
            //商城小店原生广告
            pointPlaceGoodsCustomAd: "",
            //我的出袋订单详情原生广告
            myOutBagDetailsCustomAd: "",

            //广告投放页面视频广告
            advertiseVideoAd: "",
            //购买商品 免费领取页面视频广告
            buyProductVideoAd: "",
            /* 购买界面轮播广告 */
            swiperBannerAd: "",

            newSwiperAd: "adunit-bae5eb264a5f4dc2",
            // 订单详情的插屏广告
            orderDetailInsertScreenAd: "adunit-4b35c8c774932a02",
            // 详情底部插入广告
            orderDetailBottomAd: "adunit-79525fb83f2653eb",

        },
        vPageFullPath: '',//当前页面路径参数
        vIsConnected: lifeData.vIsConnected || false,//是否链接蓝牙
        vIsBleDevice: false,//是否是蓝牙设备
        vPayStart: lifeData.vPayStart,
    },
    mutations: {
        $uStore(state, payload) {
            // 判断是否多层级调用，state中为对象存在的情况，诸如user.info.score = 1
            let nameArr = payload.name.split('.');
            let saveKey = '';
            let len = nameArr.length;
            if (nameArr.length >= 2) {
                let obj = state[nameArr[0]];
                for (let i = 1; i < len - 1; i++) {
                    obj = obj[nameArr[i]];
                }
                obj[nameArr[len - 1]] = payload.value;
                saveKey = nameArr[0];
            } else {
                // 单层级变量，在state就是一个普通变量的情况
                state[payload.name] = payload.value;
                saveKey = payload.name;
            }
            // 保存变量到本地，见顶部函数定义
            saveLifeData(saveKey, state[saveKey])
        }
    }
})


export default store