//取URL 域名
function getUrlHttp(url) {
    let urlArry = url.split("/");
    if (urlArry.length > 2) {
        return urlArry[0] + "//" + urlArry[2]
    } else {
        return
    }
}

// 取Url中/的参数
function getUrlDynamicData(url, name) {
    let urlArry = url.split("/");
    for (let i = 0; i < urlArry.length; i++) {
        if (urlArry[i] == name) { return urlArry[i + 1]; }
    }
    return
}

//取URL 中指定参数 
function getUrlParams(url, variable) {
    if (url.split("?").length > 1) {
        let vars = url.split("?")[1].split("&");
        console.log(vars);
        for (let i = 0; i < vars.length; i++) {
            let pair = vars[i].split("=");
            if (pair[0] == variable) { return pair[1]; }
        }
    }
    return false;
}

export default {
    getUrlHttp,
    getUrlParams,
    getUrlDynamicData
}