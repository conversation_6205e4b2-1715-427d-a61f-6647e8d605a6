<template>
  <view class="turn-controls">
    <!-- 左转按钮 -->
    <view
      class="ctrl-btn left-turn"
      :class="{ active: isLeftTurnActive }"
      @touchstart="leftTurnPress"
      @touchend="leftTurnRelease"
      @click="() => {}"
      style="touch-action: manipulation; user-select: none"
    >
      <view class="button-background">
        <image
          src="../../static/右转.svg"
          :class="isLeftTurnActive ? 'imagAc' : 'imag'"
          mode="aspectFit"
          alt="左转"
        />
        <view class="text">左转</view>
      </view>
    </view>
    <!-- 
    <button
      type="primary"
      :class="{ active: isForwardActive }"
      style="margin-bottom: 50rpx; margin-top: 50rpx"
      @touchstart="leftTurnPress"
      @touchend="leftTurnRelease"
    >
      左转按钮
    </button>
    <button
      type="primary"
      :class="{ active: isBackActive }"
      @touchstart="rightTurnPress"
      @touchend="rightTurnRelease"
    >
      右转按钮
    </button> -->

    <!-- 右转按钮 -->
    <view
      class="ctrl-btn right-turn"
      :class="{ active: isRightTurnActive }"
      @touchstart="rightTurnPress"
      @touchend="rightTurnRelease"
      @click="() => {}"
      style="touch-action: manipulation; user-select: none"
    >
      <view class="button-background">
        <image
          src="../../static/左转.svg"
          :class="isRightTurnActive ? 'imagAc' : 'imag'"
          mode="aspectFit"
          alt="右转"
        />
        <view class="text">右转</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "TurnControls",
  props: {
    isLeftTurnActive: Boolean, // 左转按钮状态
    isRightTurnActive: Boolean, // 右转按钮状态
  },
  methods: {
    // 左转按钮事件
    leftTurnPress() {
      this.$emit("left-turn-press")
    },
    leftTurnRelease() {
      this.$emit("left-turn-release")
    },

    // 右转按钮事件
    rightTurnPress() {
      this.$emit("right-turn-press")
    },
    rightTurnRelease() {
      this.$emit("right-turn-release")
    },
  },
}
</script>

<style scoped lang="scss">
.turn-controls {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 40rpx;
  margin-right: 200rpx;
  margin-top: 60rpx;
}

.ctrl-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 180rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  background-color: rgba(128, 128, 128, 0.273);
  position: relative;
  z-index: 5;
  overflow: hidden;
  border-radius: 12rpx;
  pointer-events: auto;
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  /* 3D凸起效果 - 未按下状态 */
  box-shadow:
    /* 外部发光 */ 0 0 20rpx rgba(0, 255, 255, 0.3),
    /* 上方高光 */ 0 -4rpx 8rpx rgba(255, 255, 255, 0.1),
    /* 下方阴影 */ 0 6rpx 12rpx rgba(0, 0, 0, 0.4),
    /* 内部高光 */ inset 0 2rpx 4rpx rgba(255, 255, 255, 0.1),
    /* 内部阴影 */ inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.2);

  border: 1rpx solid rgba(0, 255, 255, 0.6);
  transform: translateY(0rpx);

  /* 快速响应的过渡效果 */
  transition: all 0.1s ease-out;

  &:active,
  &.active {
    transform: translateY(4rpx);
    box-shadow: 0 0 15rpx rgba(0, 255, 255, 0.5),
      0 -2rpx 4rpx rgba(255, 255, 255, 0.1), 0 2rpx 6rpx rgba(0, 0, 0, 0.6),
      inset 0 1rpx 2rpx rgba(255, 255, 255, 0.1),
      inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.3);
  }
}

.button-background {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.text {
  font-size: 30rpx;
  color: white;
  font-weight: bold;
  margin-top: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 2;
}

.imagAc {
  width: 60rpx;
  height: 60rpx;
  /* 激活状态红色高亮效果 */
  filter: brightness(1.5) saturate(2) hue-rotate(-10deg) contrast(1.3);
  transition: all 0.1s ease;
  position: relative;
  z-index: 2;
}
.ctrl-btn.active {
  background-color: rgba(128, 128, 128, 0.8) !important;
  filter: brightness(1.6);
}

.imag {
  width: 60rpx;
  height: 60rpx;
  transition: all 0.1s ease;
  position: relative;
  z-index: 2;
}
</style>
