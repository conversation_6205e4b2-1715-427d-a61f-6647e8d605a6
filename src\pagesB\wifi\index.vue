<template>
    <view>
        <view class="content">
            <view class="tip">
                <view class="title">欢迎体验小汉WiFi一键连</view>
                <view class="ios" @click="go">
                    iOS如何连接,点击这里
                    <BaseIcon name="arrow-right-double" size="22" color="#09a2e3" />
                </view>
            </view>
            <view class="main">
                <image class="main-bg" src="../static/wifi_bg.png" />
                <view class="main-content">
                    <block v-if="wifiConnected">
                        <view class="connected">
                            <view class="connected_correct">
                                <view class="connected_correct_icon">
                                    <BaseIcon name="checkbox-mark" color="#09a2e3" size="36" />
                                </view>
                            </view>
                            <view>Wi-Fi已连接</view>
                        </view>
                        <view class="merchant_1">{{ pointPlaceInfo.hotelName }}</view>
                    </block>
                    <block v-else>
                        <view class="merchant">商户：{{ pointPlaceInfo.hotelName || '暂无' }}</view>
                        <view class="title">
                            <block v-if="wifiInfo.wifi_name">
                                立即为您识别{{ wifiInfo.wifi_name }}
                            </block>
                            <block v-else>
                                该商户暂未设置Wi-Fi
                            </block>

                        </view>
                    </block>
                    <view class="wifi-link">
                        <BaseButton @onClick="linkWifi" :text="wifiConnected ? '重新连接' : '一键识别连接'" width="400rpx"
                            shape="circle" color="#09a2e3" />
                    </view>
                </view>
            </view>

            <!-- <view class="logo-box">
                <image class="logo-img" src="../static/login-ad.png" />
            </view> -->

        </view>
    </view>
</template>
<script>
import BaseIcon from '@/components/base/BaseIcon.vue';
import BaseButton from '@/components/base/BaseButton.vue';
const app = getApp()
import { startWifi } from '@/utils/wifi';
export default {
    components: { BaseIcon, BaseButton, BaseIcon },
    data() {
        return {
            wifiInfo: {
                wifi_name: '',
                wifi_psd: '',
            },
            pointPlaceInfo: {},
            wifiConnected: false
        }
    },
    methods: {
        linkWifi() {
            startWifi()
        },
        go() {
            uni.navigateTo({
                url: './iosTip'
            })
        }
    },
    onLoad() {
        this.wifiInfo = app.globalData.wifiInfo;
        this.pointPlaceInfo = this.vPointInfo
        this.wifiConnected = app.globalData.wifiConnected
        uni.$on('event_wifi_connected', (res) => {
            this.wifiConnected = res.connected
        });

        let platform = uni.getSystemInfoSync().platform
        console.log("🚀 ~  uni.getSystemInfoSync()", uni.getSystemInfoSync())
        if (platform == 'ios') {
            console.log('我是iOS')
            this.go()
        } else if (platform == 'android') {
            console.log('我是安卓')

        }
    }
}
</script>


<style scoped  lang='scss'>
.content {
    padding: 0 54rpx;
}

.tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 120rpx;


    .title {
        color: #050505;
        font-size: 42rpx;
    }

    .ios {
        display: flex;
        align-items: center;
        color: #09a2e3;
        font-size: 26rpx;
        margin-top: 26rpx;
    }
}

.main {
    position: relative;
    width: 640rpx;
    height: 640rpx;
    margin: 106rpx auto 0;

    &-bg {
        width: 100%;
        height: 100%;
    }

    &-content {
        position: absolute;
        bottom: -44rpx;
        left: -35rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 710rpx;
        height: 350rpx;
        color: 333333;
        padding: 0 40rpx;
        background-color: #fff;
        border-radius: 2rpx;
        box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.2);
        z-index: 1;
        box-sizing: border-box;

        .merchant {
            font-size: 26rpx;
            margin-top: 80rpx;
        }

        .title {
            font-size: 30rpx;
            margin-top: 20rpx;
            margin-bottom: 40rpx;
        }



        .merchant_1 {
            font-size: 30rpx;
            color: #09a2e3;
            margin: 20rpx 0 34rpx;
        }

        .connected {
            display: flex;
            align-items: center;
            font-size: 30rpx;
            color: #000000;
            margin-top: 66rpx;

            .connected_correct {
                position: relative;
                width: 26rpx;
                height: 26rpx;
                border: 4rpx solid #333;
                margin-right: 22rpx;

                &_icon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);

                }
            }
        }

    }
}

.logo-box {
    margin: 80rpx auto 0;
    width: 292rpx;

    .logo-img {
        width: 100%;
        height: 75rpx;

    }
}
</style>