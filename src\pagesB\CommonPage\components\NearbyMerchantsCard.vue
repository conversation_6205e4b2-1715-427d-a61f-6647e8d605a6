<template>
    <view class="card">
        <view class="content">
            <view class="list">
                <view class="list-item see">
                    <view class="list-item-box">
                        <view class="title">商户名称：</view>
                        <view class="txt">{{ info.hotelName }}</view>
                    </view>
                    <view class="see-device" @click="go">查看设备</view>
                </view>
                <view class="list-item ">
                    <view class="list-item-box">
                        <view class="title">设备数量：</view>
                        <view class="txt">{{ info.machine_count }}</view>
                    </view>

                </view>
                <view class="list-item ">
                    <view class="list-item-box">
                        <view class="title">商户地址：</view>
                        <view class="txt">{{ info.address || '暂无' }}</view>
                    </view>

                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    name: "NearbyMerchantsCard",
    props: {
        info: { type: Object, default: {} },
    },
    data() {
        return {};
    },
    methods: {
        go() {
            uni.setStorageSync(
                "select_place",
                JSON.stringify(this.info.machine_price)
            );
            uni.navigateTo({
                url: "/pagesB/CommonPage/NearbyMerchantsDevice",
            });
        },
    },
    onLoad() { },
};
</script>

<style scoped lang="scss">
.card {
    padding: 20rpx;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    background-color: #fff;
}

.content {
    .list {
        &-item {
            display: flex;
            font-size: $font-size-base;
            line-height: 1.8;

            .title {
                flex-shrink: 0;
                color: #666;
            }

            .txt {
                color: #333;
            }

            &-box {
                display: flex;
            }
        }

        .see {
            justify-content: space-between;

            &-device {
                flex-shrink: 0;
                color: $themeColor;
            }
        }
    }
}
</style>
