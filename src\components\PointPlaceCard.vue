<template>
    <!--点位信息组件-->
    <view class="comContent">
        <!-- <view class="topDeviceStatus">
            <view class="deviceStatus">
                <image class="icon" src="../../static/icon/public/theme/ic_device_normal.png" />
                <view class="label">机器正常</view>
            </view>
            <view class="surplus">
                <image class="icon" :src="getIconByType(info.deviceType)" :style="isIconStyle" />
                <view class="label">
                    "新设备"
                </view>
            </view>

        </view> -->
        <view class="card">
            <view class="top" @click="gotoConfirmOrderPage">
                <view class="left">
                    <image class="cover" :src="info.img"></image>
                </view>
                <view class="right">
                    <view class="placeName">{{
                            info.hotelName
                    }}</view>
                    <view class="deviceInfo">
                        <view class="line">
                            <view class="icon-box flexRowAllCenter">
                                <BaseIcon name="bookmark" />
                            </view>
                            <view class="label">设备编号：{{ info.device_sn }}</view>
                        </view>
                        <view class="line">
                            <view class="icon-box flexRowAllCenter">
                                <BaseIcon name="clock" />
                            </view>
                            <view class="label textMaxOneLine">营业时间：09:00-23:00</view>
                        </view>
                        <view class="line">
                            <view class="icon-box flexRowAllCenter">
                                <BaseIcon name="map" />
                            </view>

                            <view class="label textMaxOneLine">{{
                                    info.addressDetail
                            }}</view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="btn flexRowAllCenter">
                <view class="btnDetails btnCommon flexRowAllCenter" @click="gotoConfirmOrderPage">查看详情</view>
                <view class="btnNav btnCommon flexRowAllCenter" @click="onClickBtnNav">
                    <BaseIcon name="map" sizi="26" color="#fff" />
                    <view class="btnText">导航{{ distance }}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>

import BaseIcon from './base/BaseIcon.vue';
export default {
    components: { BaseIcon },
    name: "PointPlaceCard",

    props: {
        info: Object,
        close: {
            type: Boolean,
            default() {
                return true;
            },
        },
    },
    computed: {
        distance() {
            let kilo = this.distanceLength(this.vCurLocation.latitude, this.vCurLocation.longitude, this.info.lat, this.info.lon) ?? ''
            return kilo ? kilo + 'km' : ''
        }
    },
    data() {
        return {};
    },
    methods: {
        onClickBtnNav() {
            uni.openLocation({
                latitude: parseFloat(this.info.lat),
                longitude: parseFloat(this.info.lon),
                name: this.info.adr_title,
                address: this.info.addressDetail,
                success: function (res) {
                    console.log("导航返回值：", res);
                },
            });
        },

        gotoConfirmOrderPage() {

            this.$u.vuex('vPointInfo', this.info)
            console.log("🚀 ~ this.info", this.info)
            uni.navigateTo({
                url: `/pagesB/placeDetails/index`,
            });
        },
        onClickClose() {
            //关闭窗口
            this.$emit("onClickClose");
        },
        distanceLength(la1, lo1, la2, lo2) {
            let La1 = (la1 * Math.PI) / 180.0;
            let La2 = (la2 * Math.PI) / 180.0;
            let La3 = La1 - La2;
            let Lb3 = (lo1 * Math.PI) / 180.0 - (lo2 * Math.PI) / 180.0;
            let s =
                2 *
                Math.asin(
                    Math.sqrt(
                        Math.pow(Math.sin(La3 / 2), 2) +
                        Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)
                    )
                );
            s = s * 6378.137;
            s = Math.round(s * 10000) / 10000;
            s = s.toFixed(2);
            return s;
        },
    },

    mounted() {
        //测试用
        //this.getCurrentLocation()
        //获取目的点距离,列表会频繁调用
        // this.getDistance(this.info.lat, this.info.lon);
    },
};
</script>

<style scoped lang="scss">
.comContent {
    background: white;
    box-shadow: 0px 3rpx 7rpx 0px rgba(32, 32, 32, 0.15);
    border-radius: 10rpx;
    box-sizing: border-box;


    .topDeviceStatus {
        display: flex;
        align-items: center;
        height: 65rpx;
        border-bottom: 1rpx solid #e5e5e5;
        box-sizing: border-box;
        padding: 0 20rpx;

        .deviceStatus {
            display: flex;
            align-items: center;

            .icon {
                width: 28rpx;
                height: 32rpx;
            }

            .label {
                font-size: $font-size-xsmall;
                color: $themeColor;
                margin-left: 8rpx;
            }
        }

        .surplus {
            display: flex;
            align-items: center;
            margin-left: 100rpx;

            .icon {
                width: 30rpx;
                height: 30rpx;
            }

            .label {
                font-size: $font-size-xsmall;
                color: $themeColor;
                margin-left: 8rpx;
            }
        }

        .close {
            width: 33rpx;
            height: 33rpx;
            margin-left: auto;
        }
    }

    .card {

        padding: 20rpx;
        box-sizing: border-box;
    }

    .top {
        display: flex;

        .left {
            height: 190rpx;

            .cover {
                width: 190rpx;
                height: 190rpx;
                border-radius: 10rpx;
            }
        }

        .right {
            margin-left: 20rpx;
            height: 190rpx;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .placeName {
                font-size: $font-size-xlarge;
                color: $textBlack;
                font-weight: bold;
                line-height: 1;
                @include textMaxOneLine();
            }

            .deviceInfo {
                margin-top: auto;

                .line {

                    display: flex;
                    align-items: center;
                    overflow: hidden;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .label {
                        margin-left: 22rpx;
                        font-size: $font-size-xsmall;
                        color: #666666;
                    }
                }
            }
        }
    }

    .type {
        margin-left: 200rpx;
        margin-top: 40rpx;

        .imgType {
            width: 20rpx;
            height: 26rpx;
        }

        .typeName {
            margin-left: 10rpx;
            font-size: $font-size-xsmall;
            color: $themeColor;
        }
    }

    .btn {
        display: flex;
        justify-content: space-between;
        margin-top: 30rpx;

        .btnCommon {
            width: 300rpx;
            height: 80rpx;
            border-radius: 40rpx;
            box-sizing: border-box;
            font-size: $font-size-large;
            text-align: center;
        }

        .btnDetails {
            border: 1px solid $themeColor;
            background-color: #fff;
            color: #333;
        }

        .btnNav {
            color: #fff;
            background: linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2);

            .nav {
                width: 27rpx;
                height: 27rpx;
            }

            .btnText {
                margin-left: 6rpx;
            }
        }
    }
}
</style>