<template>
    <view>
        <view class="content">
            <view class="content-box">
                <view class="title">
                    <view>设备编号</view>
                    <view class="necessary">*</view>
                </view>
                <input v-model="device_sn" :disabled="true" placeholder-style="" />
            </view>
            <view class="content-box">
                <view class="title">
                    <view>收货人</view>
                    <view class="necessary">*</view>
                </view>
                <input v-model="receiver.name" placeholder="请输入收货人" />
            </view>
            <view class="content-box">
                <view class="title">
                    <view>手机号</view>
                    <view class="necessary">*</view>
                </view>

                <input v-model="receiver.phone" placeholder="请输入手机号" />
            </view>
            <view class="content-box">
                <view class="title">
                    <view>收货地址</view>
                    <view class="necessary">*</view>
                </view>
                <input v-model="receiver.address" placeholder="请输入收货地址" />
            </view>
        </view>
        <view class="goods-card">

            <PlaceGoodsCard :isShow="false" v-for="item in goodsList" :key="item.goods_id" :info="item" />
        </view>
        <SafeBlock height="120" />
        <view class="fixed-btn flexRowBetween" :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }">
            <view class="total">
                <view class="total-box">
                    <view>合计：</view>
                    <view class="total-box-mark">￥</view>
                    <view class="total-box-price">{{ goodsTotalPrice.toFixed(2) }}</view>
                </view>
                <view class="total-num"> 共计{{ goodsTotalNum }}件 </view>
            </view>
            <view class="btn" @click="confirmOrder"> 确认提交 </view>
        </view>
    </view>
</template>

<script>

import { createGoodsOrderAndPay } from "@/common/http/api";
import PlaceGoodsCard from '../components/PlaceGoodsCard.vue';
import SafeBlock from '../../components/list/SafeBlock.vue';
import { payOrder } from "@/utils/pay"
export default {
    components: { PlaceGoodsCard, SafeBlock },
    data() {
        return {
            receiver: {}, //选择的点位信息
            goodsList: [],
            goodsTotalPrice: 0,
            goodsTotalNum: 0,
            device_sn: "",
        };
    },
    methods: {
        confirmOrder() {
            if (
                !this.receiver.name ||
                !this.receiver.phone ||
                !this.receiver.address
            ) {
                return uni.showToast({ title: "请填写完整收货信息~", icon: "none" });
            }

            let reg =
                /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-7|9])|(?:5[0-3|5-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1|8|9]))\d{8}$/;
            if (!reg.test(this.receiver.phone))
                return uni.showToast({ title: "请输入正确手机号~", icon: "none" });
            let goodsInfo = [];

            this.goodsList.forEach((item) => {
                goodsInfo.push({
                    goods_id: item.goods_id,
                    num: item.isAmount,
                });
            });

            let data = {
                goods_info: encodeURIComponent(JSON.stringify(goodsInfo)),
                receiver_phone: this.receiver.phone,
                receiver_name: this.receiver.name,
                receiver_address: this.receiver.address,
                number: this.goodsTotalNum,
                device_sn: this.device_sn,
            };
            let that = this
            createGoodsOrderAndPay(data).then((res) => {
                console.log("🚀 ~ res", res);
                let payInfo = res.data;
                console.log("🚀 ~ payInfo", payInfo);
                payOrder(this, payInfo, `/pagesB/marketOrder/index`);

            });
        },
    },
    onLoad(opt) {
        let goodsData = uni.getStorageSync("select_purchase_goods");
        this.goodsList = goodsData?.list || [];
        this.goodsTotalPrice = goodsData?.totalPrice || 0;
        this.goodsTotalNum = goodsData?.totalNum || 0;
        this.device_sn = goodsData?.device_sn;
    },
};
</script>
<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.content {
    background-color: $uni-bg-color;
    padding: 20rpx 30rpx;

    &-box {
        margin-bottom: 20rpx;

        .title {
            display: flex;
            align-items: center;
            color: $textBlack;
            font-size: $font-size-middle;
            font-weight: bold;
            margin-bottom: 20rpx;

            .necessary {
                color: red;
                margin-left: 4rpx;
            }
        }
    }
}

.goods-card {
    padding: 20rpx 30rpx;
}

.fixed-btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    background-color: $uni-bg-color;
    z-index: 999;

    .total {
        &-box {
            display: flex;
            align-items: flex-end;
            color: $textBlack;
            font-size: $font-size-base;

            &-mark {
                color: red;
                font-size: $font-size-xsmall;
            }

            &-price {
                color: red;
                font-size: $font-size-middle;
            }
        }

        &-num {
            color: $textDarkGray;
            font-size: $font-size-small;
        }
    }

    .btn {
        padding: 20rpx 50rpx;
        color: #fff;
        font-size: $font-size-xlarge;
        background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
        border-radius: 20rpx;
    }
}
</style>
