<template>
  <u-sticky bgColor="#fff" v-if="isSticky">
    <u-tabs :scrollable="isScroll" :list="list" @change="change" />
  </u-sticky>
  <u-tabs v-else :scrollable="isScroll" :list="list" @change="change" />
</template>
<script>
export default {
  name: "BaseTabs",
  props: {
    isSticky: { type: Boolean, default: true },
    isScroll: { type: Boolean, default: false },
    list: {
      type: Array,
      default: [],
    },
    isShowBar: { type: Boolean, default: false },
  },
  data() {
    return {};
  },

  methods: {
    change(e) {
      this.$emit("change", e);

    },
  },
};
</script>

<style scoped lang="scss">
.com {
  justify-content: flex-start;
}
</style>
