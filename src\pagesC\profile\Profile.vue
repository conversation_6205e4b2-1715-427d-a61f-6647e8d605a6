<template>
    <view class="content">

        <view class="top">
            <image class="topBg" src="../static/icon/ic_profile_top_bg.png" />
            <view class="titleBar">
                <BaseNavbar title="个人中心" bgColor="rgba(255, 255, 255,0)" color="#fff" :isShowSlot="isShowSlot">
                </BaseNavbar>
                <view class="profileInfo " :style="{ marginTop: '26rpx' }">
                    <!-- <image class="avatar" :src="vMemberInfo.headimgurl || '/pagesC/static/icon/ic_def_avatar.png'"
                        @click="getUserProfile"></image> -->
                    <view class="user-box-info-img">
                        <!-- #ifndef MP-WEIXIN -->
                        <image class="avatar" :src="vMemberInfo.headimgurl || '/pagesC/static/icon/ic_def_avatar.png'
                    " @click="getUserProfile" />
                        <!-- #endif -->
                        <!-- #ifdef MP-WEIXIN -->
                        <button class="avatar_btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                            <image class="avatar" :src="avatarUrl || '/pagesC/static/icon/ic_def_avatar.png'"></image>
                        </button>
                        <!-- #endif -->
                        <!-- <view class="bind-mobile">绑定手机号</view> -->

                    </view>
                    <view class="rightInfo">
                        <!-- <view class="nickName" @click="getUserProfile">
                            {{
                    username || "点击授权登录"
                }}
                        </view> -->
                        <view class="user-box-info-name">
                            <!-- #ifndef MP-WEIXIN -->
                            <view class="nickName" @click="getUserProfile">
                                {{ vMemberInfo.user_nickname || "点击授权登录" }}</view>
                            <!-- #endif -->
                            <!-- #ifdef MP-WEIXIN -->
                            <view class="vip_box">
                                <input type="nickname" class="nickName" @blur="blurname" v-model="username"
                                    placeholder="请输入昵称">
                            </view>

                            <!-- #endif -->
                            <!-- <view class="bind-mobile">绑定手机号</view> -->
                        </view>

                    </view>
                </view>
            </view>
        </view>

        <view class="menuList">
            <view class="menuItem " v-for="(item, index) in menuList" :key="index"
                @click="onClickProfileMenuItem(item)">
                <view class="menuItem-left">
                    <view class="iconBox ">
                        <image class="icon" :style="item.style" :src="item.icon"></image>
                    </view>
                    <view class="title">{{ item.title }}</view>
                </view>

                <BaseIcon name="arrow-right" size="18" />
            </view>
        </view>


        <CommonAd :ad="vAd.personalCenterBannerAd" type="banner" />
        <!-- <view :style="{ width: '100%', height: '200rpx', padding: '30rpx', boxSizing: 'border-box' }">
            <image src="/pagesC/static/icon/login-ad.png" :style="{ width: '100%', height: '100%' }" />
        </view> -->
        <LoginPopup />
        <SafeBlock height="20" />
    </view>
</template>
<script>
import BaseIcon from '../../components/base/BaseIcon.vue';
import BaseNavbar from '../../components/base/BaseNavbar.vue';
import SafeBlock from '../../components/list/SafeBlock.vue';
import LoginPopup from '../../components/LoginPopup.vue';
import CommonAd from '../../components/WxAd/CommonAd.vue';
export default {
    components: { BaseNavbar, BaseIcon, SafeBlock, LoginPopup, CommonAd },
    data() {
        return {
            menuList: [
                /* #ifdef MP-WEIXIN */
                // {

                //     icon: require("../static/icon/ic_profile_wallet.png"),
                //     title: "我的钱包",
                //     url: `/profilePages/myWallet/index`,
                //     id: 1,
                //     style: 'width:50rpx;height:46rpx;'
                // },
                /* #endif */
                {
                    icon: require("../static/icon/ic_profile_order.png"),
                    title: "我的订单",
                    id: 2,
                    url: `/pagesB/order/Order`,
                    style: 'width:42rpx;height:48rpx;'
                },
                {
                    icon: require("../static/icon/ic_profile_help.png"),
                    title: "帮助中心",
                    id: 3,
                    url: `/pagesC/help/index`,
                    style: 'width:49rpx;height:44rpx;'
                },
                /* #ifdef MP-WEIXIN */
                // {

                //     icon: require("../static/icon/ic_profile_companion.png"),
                //     title: "成为合伙人",
                //     url: `/pagesB/partner/index`,
                //     style: 'width:55rpx;height:46rpx;'
                // },
                /* #endif */
                {
                    icon: require("../static/icon/ic_profile_about_us.png"),
                    title: "关于我们",
                    id: 5,
                    url: `/pagesC/about/index`,
                    style: 'width:48rpx;height:48rpx;'
                },
                // {
                //     icon: require("../static/icon/ic_profile_score.png"),
                //     title: "积分任务",
                //     id: 6,
                //     url: `/pagesB/scoreTask/index`,
                //     style: 'width:48rpx;height:48rpx;'
                // },
                // {
                //     icon: require("../static/icon/ic_profile_shop.png"),
                //     title: "商城订单",
                //     id: 7,
                //     url: `/pagesB/marketOrder/index`,
                //     style: 'width:48rpx;height:48rpx;'
                // },
                // {
                //     icon: require("../static/icon/ic_profile_ad.png"),
                //     title: "广告订单",
                //     id: 8,
                //     url: `/pagesB/advertiseOrder/index`,
                //     style: 'width:48rpx;height:48rpx;'
                // },
                // {
                //     icon: require("../static/icon/invest_order.png"),
                //     title: "投资订单",
                //     id: 8,
                //     url: `/pagesB/investOrder/index`,
                //     style: 'width:48rpx;height:48rpx;'
                // },
            ],
            isLogin: false,
            username: '',
            avatarUrl: '',

        };
    },

    methods: {
        onChooseAvatar(e) {

            if (e.detail.avatarUrl && e.detail.avatarUrl != this.vMemberInfo.headimgurl) {
                let userinfo = {
                    avatarUrl: e.detail.avatarUrl,
                    user_nickname: this.username
                }
                // console.log('微信头像', e)
                uni.$emit('updataUser', userinfo)
                this.avatarUrl = e.detail.avatarUrl
            }
        },
        blurname(e) {
            // console.log('微信昵称', e)
            if (e.detail.value && e.detail.value != this.vMemberInfo.user_nickname) {
                let userinfo = {
                    user_nickname: e.detail.value,
                    avatarUrl: this.vMemberInfo.headimgurl
                }
                uni.$emit('updataUser', userinfo)
                this.username = e.detail.value
            }
        },
        go(url, boolear) {
            if (boolear) {
                return this.showTips();
            }
            uni.navigateTo({ url });
            // this.showTips();
        },
        showTips() {
            uni.showToast({
                title: "即将上线,敬请期待~",
                icon: "none",
            });
        },
        getUserProfile() {
            // console.log("--->用户信息", this.vMemberInfo.headimgurl);
            //登录过我就返回
            /* #ifndef  MP-WEIXIN*/
            if (this.vMemberInfo.headimgurl) return;

            this.isLogin = true;
            /* #endif */
            /* #ifdef H5*/
            return uni.showToast({
                title: "请登录小程序~",
                icon: "none",
            });
            /* #endif */
        },
        onClickProfileMenuItem(item) {
            const { url } = item;
            uni.navigateTo({ url });
        },
    },
    watch: {
        vMemberInfo: {
            handler(newValue, oldValue) {
                // 在这里执行当 vMemberInfo 变化时的逻辑
                console.log('vMemberInfo 发生变化', newValue?.user_nickname, oldValue?.user_nickname);
                if (newValue?.user_nickname != oldValue?.user_nickname) {
                    this.username = newValue?.user_nickname || '';
                }
                if (newValue?.headimgurl != oldValue?.headimgurl) {
                    this.avatarUrl = newValue?.headimgurl
                }

            },
            deep: true, // 深度监听对象内部属性的变化
            immediate: true // 立即执行监听函数，确保在组件加载时能够正确初始化值
        }
    },
    onLoad() { },
}
</script>


<style scoped lang='scss'>
.content {
    position: relative;
    overflow: hidden;

    .top {
        position: relative;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        height: 500rpx;

        .topBg {
            position: absolute;
            left: 0;
            top: 0;
            width: 750rpx;
            height: 500rpx;
            z-index: -1;
        }

        .titleBar {
            .title {
                margin-left: 60rpx;
                font-size: $font-size-xxlarge;
                line-height: 1;
            }

            .user-box-info-img {
                width: 140rpx;
                height: 140rpx;
                border-radius: 50%;
                overflow: hidden;
                padding: 0;
            }

            .avatar_btn {
                padding: 0 !important;
            }

            .user-box {
                display: flex;
                justify-content: space-between;

                &-info {
                    display: flex;
                    justify-content: center;
                    align-content: center;

                    .avatar_btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        overflow: hidden;

                        border-radius: 50%;
                    }

                    .avatar {
                        width: 120rpx;
                        height: 120rpx;
                        border-radius: 50%;
                    }

                    &-name {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        margin-left: 20rpx;

                        .nickName {
                            text-align: center;
                            font-size: $font-size-large !important;
                            color: white;
                        }

                        .bind-mobile {
                            text-align: center;
                            border-radius: 18rpx;
                            font-size: $font-size-base;
                            margin-top: 20rpx;
                            color: white;
                            background-color: #cbcbcc;
                        }
                    }
                }

                &-set {
                    display: flex;
                    justify-content: center;
                    margin-top: 16rpx;

                    .setting,
                    .bell {
                        margin-right: 24rpx;
                    }
                }
            }
        }

        .profileInfo {
            @include flexColumnAllCenter();

            .avatar {
                width: 140rpx;
                height: 140rpx;
                border-radius: 50%;
                overflow: hidden;
            }

            .rightInfo {
                margin-top: 20rpx;

                .nickName {
                    font-size: $font-size-xlarge !important;
                    font-weight: bold;
                    color: white;
                }

                .a-button {
                    margin-top: -20rpx !important;
                }

                .phone {
                    font-size: $font-size-small;
                    color: white;
                    margin-top: 15rpx;
                }

                button {
                    background-color: transparent;
                    color: white;
                    font-size: $font-size-small;
                    padding: 0;
                    margin: 0;
                    margin-top: 15rpx;
                    border: none;

                    &:after {
                        border: none;
                    }
                }
            }
        }
    }

    .menuList {
        position: relative;
        margin: 0 30rpx;
        margin-top: -60rpx;
        padding: 30rpx;
        border-radius: 10rpx;
        background: white;
        z-index: 999;
        box-shadow: 0px 3px 7px 0px rgba(32, 32, 32, 0.15);

        .menuItem {
            @include flexRowBetween();
            margin-bottom: 50rpx;

            &-left {
                @include flexAllcenter();
            }

            &:last-child {
                margin-bottom: 0;
            }

            .iconBox {
                @include flexAllcenter();

                width: 55rpx;
                height: 55rpx;
            }

            .title {
                font-size: $font-size-middle;
                color: $textBlack;
                margin-left: 22rpx;
            }

            .arrow {
                width: 14rpx;
                height: 24rpx;
                margin-left: auto;
            }
        }
    }

    .adContainer {
        margin-top: 30rpx;
    }
}
</style>