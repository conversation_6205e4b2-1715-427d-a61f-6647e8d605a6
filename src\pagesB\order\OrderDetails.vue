<template>
  <view class="content">
    <!-- #ifndef H5 -->
    <!-- <BaseNavbar title="" /> -->
    <!-- #endif -->
    <!-- <CommonAd :ad="vAd.myOutBagDetailsCustomAd || ''" type="custom" /> -->

    <view class="statusBar">
      <u-line-progress
        v-if="!isFromOrderList"
        :percentage="percentage"
        :showText="true"
        activeColor="#0DACE0"
        height="30rpx"
      ></u-line-progress>
      <view class="statusBar_title">
        {{
          (isFromOrderList
            ? fromOrderListTxt[orderInfo.this_status]
            : statusText) || ""
        }}
      </view>
    </view>
    <view class="pointPlaceInfo">
      <view class="topInfo">
        <view class="left">
          <image class="cover" :src="orderInfo.original_img"></image>
        </view>
        <view class="rightInfo">
          <view class="name">{{ orderInfo.goods_name || "" }}</view>
          <view class="count">
            x{{ orderInfo.realoutnum || ""
            }}{{ orderInfo.unit === 3 ? "分钟" : "" }}
          </view>
          <view class="count">{{ orderType[orderInfo.prom_type] || "" }}</view>
          <view class="realPay">
            <view class="label">{{
              itemInfo.this_status == 0
                ? "未付款："
                : itemInfo.prom_type == 8 && !itemInfo.end_time
                ? "预付："
                : "实付："
            }}</view>

            <view class="payValue">
              <view class="value">
                {{ orderInfo.prom_type == 1 ? orderInfo.order_amount : "0.00" }}
              </view>
              <view class="unit">元</view>
            </view>
          </view>
        </view>
      </view>

      <view class="statistics">
        <view class="line">设备编号：{{ orderInfo.device_sn || "" }}</view>
        <view class="line">订单编号：{{ orderInfo.order_sn || "" }}</view>
        <view class="line">
          创建时间：{{
            $u.timeFormat(orderInfo.add_time * 1000, "yyyy-mm-dd hh:MM:ss")
          }}
        </view>
        <view class="line" v-if="orderInfo.pay_name">
          支付方式：{{ orderInfo.pay_name }}
        </view>
        <view class="line" v-if="orderInfo.pay_time">
          支付时间：{{
            $u.timeFormat(orderInfo.pay_time * 1000, "yyyy-mm-dd hh:MM:ss")
          }}
        </view>
      </view>
    </view>

    <!-- 订单详情页面的banner -->
    <!-- <CommonAd :ad="vAd.myOutBagBanner || ''" type="banner" /> -->

    <!-- 进入订单详情页面就显示的插屏广告 -->
    <CommonAd :ad="vAd.orderDetailInsertScreenAd || ''" type="inter" />

    <!-- 底部占位空间，避免内容被固定广告遮挡 -->
    <view class="bottom-placeholder"></view>

    <!-- 固定在底部的广告位 -->
    <view class="fixed-bottom-ad">
      <CommonAd :ad="vAd.orderDetailBottomAd || ''" type="banner" />
    </view>
  </view>
</template>

<script>
import { getOrderDetail, openDoorByOrderSN } from "@/common/http/api"
import { globalCodes } from "@/global/globalCodes"
import CommonAd from "../../components/WxAd/CommonAd.vue"
import BaseNavbar from "../../components/base/BaseNavbar.vue"
import { adMixin } from "@/mixins/adMixin"
export default {
  components: { CommonAd, BaseNavbar },
  mixins: [adMixin],
  name: "index",
  data() {
    return {
      orderInfo: {},
      //订单编号
      order_sn: "",
      time_id: null, // 定时器
      time_count: 0, // 定时器执行次数
      status: "", // 订单状态
      isFromOrderList: false,
      fromOrderListTxt: {
        1: "已完成",
        2: "订单异常",
        3: "订单已取消",
        4: "订单异常",
        50: "退款中",
        0: "未付款",
        60: "已出货",
        70: "已退款",
        60: "出货失败",
      },
      orderType: {
        1: "普通订单",
        2: "免费订单",
        3: "免费订单",
        5: "积分订单",
      },
      progressTimer: null, //进度条定时器
      percentage: 0, //进度
      isRedirectTo: false, // 是否跳转了,防止多次跳转
      rebootNum: 0, //重启次数
    }
  },

  methods: {
    clearGetStatus() {
      if (this.time_id) {
        clearInterval(this.time_id)
        this.time_id = null
      }
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
      this.percentage = 100
      if (
        this.orderInfo.unit === globalCodes.orderUnitType.UNIT_GAME &&
        this.isRedirectTo == false &&
        this.status == 1
      ) {
        // 游戏订单
        this.isRedirectTo = true
        // console.log('跳转到游戏界面 ', this.order_sn)
        // uni.redirectTo({
        //   url: `/pagesB/chargeDetails/gaming?order_sn=${this.order_sn}`,
        // })
        if (this.orderInfo.prom_type == 8) {
          uni.redirectTo({
            url: `/pagesB/product/perGame?order_sn=${this.order_sn}`,
          })
        } else {
          uni.redirectTo({
            url: `/pagesB/chargeDetails/gaming?order_sn=${this.order_sn}`,
          })
        }
      } else {
        /* 启动失败 弹出弹出，进度条变x */
        this.percentage = 0
        this.time_count = 0
      }
    },

    //API 获取订单详情
    doGetOrderDetailInfo(orderSn) {
      // console.log('time_count',this.time_count)
      if (this.time_count >= 5) {
        this.clearGetStatus()
        this.status = 5

        // console.log('reootNum', this.rebootNum)
        if (this.rebootNum > 1) {
          if (this.orderInfo.prom_type == 8) {
            uni.redirectTo({
              url: `/pagesB/product/perGame?order_sn=${this.order_sn}`,
            })
          } else if (this.orderInfo.prom_type == 1) {
            uni.redirectTo({
              url: `/pagesB/product/perGame?order_sn=${this.order_sn}`,
            })
          } else {
            uni.redirectTo({
              url: `/pagesB/chargeDetails/gaming?order_sn=${this.order_sn}`,
            })
          }
        } else {
          // this.rebootNum++
          this.reboot()
        }
        return
      }
      let params = {
        order_sn: orderSn,
      }
      // console.log('params', params)
      getOrderDetail(params).then((res) => {
        console.log(
          "🔧 订单详情：轮询结果",
          res.order_status,
          "time_count:",
          this.time_count
        )
        if (this.time_count == 0) {
          // 第一次修改这些值，后续只修改订单状态值
          this.orderInfo = res
          //
          // this.orderInfo.order_status=0
          this.status = this.orderInfo.order_status
        }
        //
        //
        // res.order_status=0
        if (res.order_status == 1) {
          // 订单完成
          this.status = 1
          this.clearGetStatus()
          // console.log('this.status', this.status, this.orderInfo)
          return
        } else if (res.order_status == 2) {
          // 订单异常/失败
          console.log("🔧 订单详情：检测到订单异常状态", res.order_status)
          this.status = 2
          this.clearGetStatus()
          this.percentage = 0 // 进度条归零
          console.log("🔧 订单详情：已停止轮询，进度条归零")
          return
        }
        // console.log('this.status', this.status, this.orderInfo)
        this.time_count++
        this.percentage = 20 * this.time_count
      })
    },
    /* 重新开始 */
    reboot() {
      // console.log('this.status', this.status)
      if (this.status != 5) {
        return
      }
      let data = {}
      let params = {
        order_sn: this.order_sn,
      }
      openDoorByOrderSN(params).then((res) => {})
      this.rebootNum++
      this.rebottStatus(this.order_sn)
    },
    /* 重启 */
    rebottStatus(order_sn) {
      this.doGetOrderDetailInfo(order_sn)
      if (!this.isFromOrderList) {
        this.time_id = setInterval(() => {
          // console.log('this.order_sn', this.order_sn, this.isFromOrderList)
          this.doGetOrderDetailInfo(order_sn)
        }, 3000)
      }
    },
  },
  computed: {
    // 计算属性
    statusText() {
      if (this.status == 4) {
        if (this.orderInfo.unit == 3) {
          return "已支付,等待启动..."
        }
        return "已支付,等待出货..."
      } else if (this.status == 1) {
        if (this.orderInfo.unit == 3) {
          return "设备启动成功,请放心使用"
        }
        return "订单完成,请取走商品"
      } else if (this.status == 3) {
        return "取消订单"
      } else if (this.status == 2) {
        if (this.orderInfo.unit == 3) {
          return "设备异常,请联系厂家"
        }
        return "订单异常"
      } else if (this.status == 5) {
        if (this.orderInfo.unit == 3) {
          return "设备启动超时,正在重新启动"
        }
        return "处理超时,请联系厂家~"
      } else {
        if (this.orderInfo.unit == 3) {
          return "设备启动中..."
        }
        return "设备出货中..."
      }
    },
  },
  onLoad(opt) {
    this.isRedirectTo = false
    this.isFromOrderList = opt?.from === "orderList"
    this.order_sn = opt.order_sn

    //根据订单编号获取订单详情
    this.rebottStatus(this.order_sn)

    // 监听订单状态变化事件
    uni.$on("orderStatusChanged", (data) => {
      console.log("🔧 订单详情：收到状态变化事件", data)
      if (data.order_sn === this.order_sn) {
        console.log("🔧 订单详情：匹配当前订单，更新状态")
        this.status = data.status
        this.percentage = 0
        this.clearGetStatus()
      }
    })
  },
  onUnload() {
    // console.log('页面周期函数 onUnload()')
    this.clearGetStatus()
    // 移除事件监听
    uni.$off("orderStatusChanged")
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}

.content {
  .statusBar {
    margin: 30rpx;
    background: white;
    box-shadow: 0px 3rpx 7rpx 0px rgba(32, 32, 32, 0.15);
    border-radius: 20rpx 20rpx 10rpx 10rpx;
    font-size: $font-size-xlarge;
    color: $textBlack;

    &_title {
      @include flexAllcenter();
      height: 120rpx;
    }
  }

  .pointPlaceInfo {
    margin: 30rpx;
    margin-top: 0;
    box-shadow: 0px 3rpx 7rpx 0px rgba(32, 32, 32, 0.15);
    border-radius: 10rpx;
    padding: 30rpx;
    background-color: white;

    .topInfo {
      display: flex;
      height: 190rpx;

      .left {
        .cover {
          width: 180rpx;
          min-width: 180rpx;
          height: 190rpx;
          border-radius: 10rpx;
        }
      }

      .rightInfo {
        flex: 1;
        height: 190rpx;
        overflow: hidden;
        margin-left: 20rpx;
        display: flex;
        flex-direction: column;

        .name {
          @include textMaxOneLine();
          color: $textBlack;
          font-weight: bold;
          font-size: $font-size-xlarge;
        }

        .count {
          margin-top: 10rpx;
          font-size: $font-size-xsmall;
          color: $textDarkGray;
        }

        .realPay {
          @include flexRowVertCenter();
          margin-top: auto;

          .label {
            font-size: $font-size-xsmall;
            color: $textDarkGray;
          }

          .payValue {
            color: $mainRed;
            font-weight: bold;
            display: flex;
            align-items: flex-end;

            .value {
              font-size: $font-size-xlarge;
            }

            .unit {
              font-size: $font-size-xsmall;
              margin-bottom: 5rpx;
            }
          }
        }
      }
    }

    .statistics {
      margin-top: 40rpx;

      .line {
        font-size: $font-size-xsmall;
        color: $textDarkGray;
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .btnLine {
      margin-top: 30rpx;
      display: flex;
      justify-content: flex-end;

      .btn {
        margin-right: 40rpx;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

/* 底部占位空间，避免内容被固定广告遮挡 */
.bottom-placeholder {
  height: 120rpx; /* 根据广告高度调整 */
}

/* 固定底部广告位样式 */
.fixed-bottom-ad {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 999;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
</style>
