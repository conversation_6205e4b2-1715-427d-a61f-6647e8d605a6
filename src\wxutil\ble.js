import appOptions from './appUtil'
const app = getApp()

const crc = appOptions.requirejs("crc16");
const bin = appOptions.requirejs("binary");
//var utils = require('.././util.js');
// var cryptoService = require('../bleFile/cryptoService.js'); //引入加密文件
// var crc = require('../bleFile/crc.js'); //引入crc校验
import { globalEvents } from "../global/globalEvents";

var serviceUUID = [] //主 service 的 uuid 列表
var writeUUID = ""; //写读 UUID
var notifyUUID = ""; //notify UUID
var delayUUID = ""; //护眼仪控制时长UUID

var readUUID = "";// 无线充


var filterServiceUUID = ""; //过滤获取到的服务uuid(有些会返回多条数据)
var filterDeviceName = ""; //设备名称

var device_sn = ""; //  设备编号
var device_type = ''; //  设备类型
var _discoveryStarted = false;      // 蓝牙是否在搜索
var status = false;                 // 蓝牙是否连接,可写入当前状态
var _init = false; // 是否在初始化
var deviceId = ''; //用于区分设备的 id

var _deviceId = '';
var _serviceId = '';
var _characteristicId = '';
var action_type = ''; //操作类型
var code = -1;
var isnotExist = true //防止多次执行

// 回调
var btOpenCallback = null;          // 接收开锁回调
var btErrorCallback = null;         // 接收错误码处理
var btWriteCallback = null;    // 接收写入处理
var btRechargeCallback = null;        //接收充电回调
var readRechargeCallback = null;
var btGetVoltCallback = null;        //获取设备电压回调

//var tempStr = ''; //拼接设备返回数据
//var rssiVal = ''; //RSSI值
var currentSerialVal = ''; //当前点击格子柜序号
var currentOrderSN = ''; // 当前订单编号
var msg_id = 1;   //消息条数

// var yt_device_type = '0x80';        // 圆筒蓝牙类型
// var yt_recharge_device_type='0x81'  //圆筒蓝牙类型，带充电

// var sh_device_type = '0x66'  // SH的板子

var yt_device_type = '0x80'; // 圆筒蓝牙类型
var sh_device_type = '0x66'  // SH的板子
var yt_recharge_device_type = '0x81'  //圆筒蓝牙类型，带充电
var wxc_device_type = '0x88'; // 无线充
var wxc_device_type_new = '0x89'; // 无线充
var recharge_key = '0x87';  // 充电线 + 秘钥

/**
 * 一些判断变量
 */
var searchBleIndex = 0;//搜索蓝牙次数，如果超过连接不上，停止连接

const searchBleValue = 50;//搜索次数大于设定次数就停止搜索


//当前设备的操作类型
var OptionEnum = {
    None: -1,
    Connection: 0, //连接
    Reconnection: 1, //重连
    CloseLock: 2, //关闭
    VendingOpenLock: 8, //打开售卖机
    ReadStatus: 9, //读售卖机状态
    ReadBattery: 10, //读售卖机电量
    Recharge: 11,     //充电
    GetVolt: 12,     //获取设备电压
    ReadRecharge: 13,//读取充电状态
    ReadRechargeCallback: 14,//读取充电状态，单独回调
};



//这个是变量, 存储数据的变量
var action_type = OptionEnum.None;

function inArray(arr, key, val) {
    for (let i = 0; i < arr.length; i++) {
        if (arr[i][key] === val) {
            return i;
        }
    }
    return -1;
}

//判断是否是圆筒蓝牙类型
function isYTDevice(deviceType) {
    return deviceType == yt_device_type || deviceType == yt_recharge_device_type
}

//判断设备是否带充电功能
function isRechargeDevice(deviceType) {
    //return deviceType==yt_recharge_device_type
    return app.globalData.is_recharge
}

//返回当前蓝牙连接状态
function getBleConnectStatus() {
    return app.globalData.connected
}

function strToHexCharCode(str) {
    if (str === "")
        return "";
    var hexCharCode = [];
    hexCharCode.push("0x");
    for (var i = 0; i < str.length; i++) {
        hexCharCode.push((str.charCodeAt(i)).toString(16));
    }
    return hexCharCode.join("");
}

// ArrayBuffer转16进度字符串示例
function ab2hex(buffer) {
    var hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        function (bit) {
            return ('00' + bit.toString(16)).slice(-2)
        }
    )
    return hexArr.join('');
}

/**
 * 去掉 冒号
 */
function clearSymbol(str) {
    str = str.replace(/:/g, ""); //取消字符串中出现的所有冒号
    return str;
}

/**
 *  匹配规则: 取名称后面的mac
 */
function getNameMac(device_sn, len, name) {
    let clearColonMac = clearSymbol(device_sn); // 清除冒号
    let lastFourMac = clearColonMac.substring(clearColonMac.length - len);
    let strName = name.toUpperCase();
    strName = strName + lastFourMac.toUpperCase(); //转大写
    console.log('拼接后的' + strName);
    return strName
}


/**
 * 区分不同类型的服务相关uuid
 * 1-->充电宝 2-->售卖机
 */
function setServicesUUID() {
    console.log('type======>', app.globalData.device_type);
    if (app.globalData.device_type === sh_device_type) {    // 格子机
        serviceUUID[0] = "6E400002-B5A3-F393-E0A9-E50E24DCCA9E"; //主 service的uuid 列表
        writeUUID = "6E400002-B5A3-F393-E0A9-E50E24DCCA9E"; //写读 UUID
        notifyUUID = "6E400003-B5A3-F393-E0A9-E50E24DCCA9E"; //notify UUID
        //filterServiceUUID = "*E0";
    } else if (isYTDevice(app.globalData.device_type)) { // 圆筒蓝牙类型
        serviceUUID[0] = "0783b03e-8535-b5a0-7140-a304f013c3b7"; //主 service的uuid 列表
        writeUUID = "0783b03e-8535-b5a0-7140-a304f013c3ba"; //写读 UUID
        notifyUUID = "0783b03e-8535-b5a0-7140-a304f013c3b8"; //notify UUID
        //readUUID = "0783b03e-8535-b5a0-7140-a304f013c3ba"; //notify UUID
        //filterServiceUUID = "*E0";

        //这里介绍用name匹配方法
        //filterDeviceName = getNameMac(device_sn, 6, 'abc_'); //设备名称
    } else if (app.globalData.device_type === wxc_device_type) {
        serviceUUID[0] = "0000F040-0000-1000-8000-00805F9B34FB"; //主 service的uuid 列表
        writeUUID = "0000F041-0000-1000-8000-00805F9B34FB"; //写读 UUID
        //notifyUUID 		= "0000F042-0000-1000-8000-00805F9B34FB"; //notify UUID
        /* #ifdef MP-ALIPAY */
        readUUID = "F042";// 读UUID
        /* #endif */
        /* #ifndef MP-ALIPAY */
        readUUID = "0000F042-0000-1000-8000-00805F9B34FB";// 读UUID
        /* #endif */

        filterServiceUUID = "F040";
    } else if (app.globalData.device_type === wxc_device_type_new) {
        serviceUUID[0] = "0000F040-0000-1000-8000-00805F9B34FB"; //主 service的uuid 列表
        writeUUID = "0000F041-0000-1000-8000-00805F9B34FB"; //写读 UUID
        //notifyUUID 		= "0000F042-0000-1000-8000-00805F9B34FB"; //notify UUID

        /* #ifdef MP-ALIPAY */
        readUUID = "F042";// 读UUID
        /* #endif */
        /* #ifndef MP-ALIPAY */
        readUUID = "0000F042-0000-1000-8000-00805F9B34FB";// 读UUID
        /* #endif */

        filterServiceUUID = "F040";
    } else if (app.globalData.device_type === recharge_key){ // 密码充电线

        serviceUUID[0] = "0000FF10-0000-1000-8000-00805F9B34FB";
        writeUUID = "0xFF12"; // 写UUID
        readUUID  = "0xFF11"; // 读UUID
    }
}


/**
 * 初始化蓝牙模块
 *
 */
async function initBle() {
    console.log('initBle connected ', app.globalData.connected);
    if (app.globalData.connected) return;
    if (_init || _discoveryStarted) {
        return;
    }
    _init = true;
    appOptions.showLoading('搜索蓝牙...', 'initBle');

    // #ifdef MP-WEIXIN
    //断开连接【每次初始化先断开连接】
    try {
        await closeBLEConnection();
    } catch (res) {
        console.log('initBle closeBLEConnection catch ', res);
        if (res != 10000) { // 没有初始化蓝牙
            _init = false;
            appOptions.hideLoading('initBle 关闭蓝牙失败 ', res);

            errorCallback(res);
            return;
        }
    }
    // #endif

    //closeBLEAdapter();

    //调用主服务id相关
    setServicesUUID();

    _discoveryStarted = false;

    uni.openBluetoothAdapter({
        success(res) {
            console.log('openBluetoothAdapter 初始化蓝牙模块是否成功:', res)
            appOptions.hideLoading('initBle');
            startDiscovery();
        },
        fail(err) {
            appOptions.hideLoading('initBle 异常');
            console.log("初始化蓝牙失败", 123);
            console.log('初始化蓝牙失败', err);
            errorCallback(err.errCode, err.errMsg);
            console.log("🚀 ~ err.error", err.error)
            if (err.error == 12) {

                uni.$emit(globalEvents.EVENT_BLE_NOT_OPEN, {})
                uni.showModal({
                    title: '提示',
                    content: '请检查手机蓝牙是否打开',
                    showCancel: false
                })
                // uni.showToast({
                //     title: '请检查手机蓝牙是否打开',
                //     icon: 'none'
                // })
            }

        }
    })
    _init = false;

}

/**
 * 监听蓝牙状态变化
 */
function onBluetoothAdapterStateChange() {
    //监听蓝牙适配器状态变化事件【根据需求是否执行】
    uni.onBluetoothAdapterStateChange(function (res) {
        console.log('onBluetoothAdapterStateChange() :  ', res)
        if (res.available) {
            console.log('蓝牙可用，搜索设备:--》 ', _discoveryStarted)
            if (!_discoveryStarted) {
                startDiscovery();
            }
        } else {

        }
    });
}

/**
 * 开锁搜索设备
 */
function startDiscovery() {
    onBluetoothDeviceFound();
    startBluetoothDevicesDiscovery();
}

/**
 * 监听寻找新设备事件
 * 注意咯： 这里有展示三种不同方式来连接设备，请看备注【mac, name, 根据系统的】 各取所需吧。
 */
function onBluetoothDeviceFound() {
    console.log('onBluetoothDeviceFound（） 方法', new Date());
    appOptions.showLoading('等待连接...', 'onBluetoothDeviceFound');
    uni.onBluetoothDeviceFound((res) => {
        console.log('onBluetoothDeviceFound（） success ', new Date());
        console.log('onBluetoothDeviceFound（） success ', res);
        // 遍历检索的设备
        if (res && res.devices) {
            searchBleIndex++;
            console.log("🚀 ~ searchBleIndex", searchBleIndex)
            //超过20次，未搜索到指定设备，关闭蓝牙连接。（可能是未找到设备，或者蓝牙已处于连接状态）
            if (searchBleIndex >= searchBleValue) {
                searchBleIndex = 0
                setTimeout(() => {
                    if (!app.globalData.connected) {
                        appOptions.hideLoading("未搜索到设备,请联系管理员处理或尝试重新连接~")
                        uni.showModal({
                            title: "蓝牙连接失败了哦",
                            content: `1、请查看周围是否有连接此设备的蓝牙
2、请先关闭手机蓝牙在打开一次
3、请检查位置信息是否打开
4、退出小程序重新扫码进入`,
                            showCancel: false,
                            confirmText: "我知道了",
                        });
                        closeBLEConnection();//关闭蓝牙
                        return
                    }
                }, 3000)
            }
            res.devices.forEach(device => {
                if (!device.name && !device.localName) {
                    return
                }
                console.log('onBluetoothDeviceFound type----->' + app.globalData.device_type);

                if (app.globalData.device_type === sh_device_type) {
                    if (device.advertisData) {
                        device.advertisData = buf2hex(device.advertisData);
                    } else {
                        device.advertisData = '';
                    }
                    if (app.globalData.device_sn && app.globalData.device_sn == device.advertisData && isnotExist) {
                        isnotExist = false;
                        deviceId = device.deviceId;
                        //连接设备
                        console.log('onBluetoothDeviceFound（） success 1 ', new Date());
                        appOptions.hideLoading('onBluetoothDeviceFound');

                        createBLEConnection();
                    }
                } else if (isYTDevice(app.globalData.device_type)) {
                    console.log('广播数据结果 device.name----->' + device.name);
                    console.log('广播数据结果 app.globalData.device_sn.substr(0,12)----->' + app.globalData.device_sn.substr(0, 12));
                    if (device.name === app.globalData.device_sn.substr(0, 12)) {
                        isnotExist = false; // 不存在
                        deviceId = device.deviceId;
                        //连接设备
                        console.log('onBluetoothDeviceFound（） success 2 ', new Date());
                        appOptions.hideLoading('onBluetoothDeviceFound');

                        createBLEConnection();
                    }
                } else {
                    console.log('广播数据结果 device.deviceName----->', device.deviceName);
                    console.log('广播数据结果 device.name----->', device.name);
                    console.log('广播数据结果 device_sn----->', app.globalData.device_sn);
                    console.log('广播数据结果 deviceId----->', device.deviceId);
                    if (device.name === app.globalData.device_sn) {
                        isnotExist = false; // 不存在
                        deviceId = device.deviceId;
                        appOptions.hideLoading('onBluetoothDeviceFound');

                        //连接设备
                        createBLEConnection();
                    }
                }
            });
        }
    });
}

/**
 * 执行连接蓝牙设备
 */
function startBluetoothDevicesDiscovery() {
    console.log('startBluetoothDevicesDiscovery res _discoveryStarted----->', _discoveryStarted);
    if (_discoveryStarted) {
        return;
    }
    console.log('startBluetoothDevicesDiscovery res app.globalData.device_type----->', app.globalData.device_type);
    _discoveryStarted = true;


    //showLoading('搜索蓝牙...','startBluetoothDevicesDiscovery');
    if (app.globalData.device_type === sh_device_type) { // 格子机 带ServicesUUID
        uni.startBluetoothDevicesDiscovery({
            services: ['FEE7'],
            allowDuplicatesKey: false,
            success(discovery) {
                console.log('startBluetoothDevicesDiscovery success ', discovery);
            },
            fail(err) {
                console.log('startBluetoothDevicesDiscovery error ', err);
                //btErrorCallback(res.errCode, "");
                errorCallback(err.errCode, err.errMsg);
            }
        });

    } else {
        uni.startBluetoothDevicesDiscovery({
            allowDuplicatesKey: false,
            success: (res) => {
                console.log('startBluetoothDevicesDiscovery success ', res);
            },
            fail: (res) => {
                errorCallback(res.errCode, err.errMsg);
                console.log('startBluetoothDevicesDiscovery error ', res);
            },
            complete: (res) => {

            }
        });
    }

}


//停止搜寻附近的蓝牙外围设备。
function stopBluetoothDevicesDiscovery(fromFunction) {
    console.log('stopBluetoothDevicesDiscovery() : fromFunction ' + fromFunction);
    return new Promise(function (resolve, reject) {
        uni.stopBluetoothDevicesDiscovery({
            success: (res) => {
                console.log('stopBluetoothDevicesDiscovery, success  :', res)
                resolve();
            },
            fail: (res) => {
                console.log('stopBluetoothDevicesDiscovery, fail  :', res);
                reject(res.errCode);
            }
        });
    });
    /**

     try{
        let res = await wxp.stopBluetoothDevicesDiscovery();
        console.log('stopBluetoothDevicesDiscovery, success  :', res)
    }catch(res) {
        console.log('stopBluetoothDevicesDiscovery, fail  :', res);
        //errorCallback(res.errCode,res.errMsg);
        return res.errCode;
    }
     return 0;
     */
}

// 获取蓝牙状态
function getBluetoothAdapterState() {
    uni.getBluetoothAdapterState({
        success: (res) => {
            console.log('getBluetoothAdapterState', res)
            if (res.discovering) {  // 正在搜索
                onBluetoothDeviceFound();
            } else if (res.available) { // 有效
                startBluetoothDevicesDiscovery()
            }
        },
        fail(res) {
            console.log('getBluetoothAdapterState fail', res);
            errorCallback(res.errCode, res.errMsg);
        }
    })
}


/**
 * 连接蓝牙设备
 */
function createBLEConnection() {
    searchBleIndex = 0;//一定要置0，避免二次连接继续计数
    console.log('createBLEConnection connect() 方法 deviceId ',deviceId);
    stopBluetoothDevicesDiscovery('createBLEConnection'); //停止搜索

    appOptions.showLoading('正在连接设备...', 'createBLEConnection');

    uni.createBLEConnection({
        // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
        deviceId: deviceId,
        success(res) {
            //获取蓝牙所有服务
            console.log('准备调用getBLEDeviceServices方法，deviceId=', deviceId)
            getBLEDeviceServices(deviceId);
        },
        complete() {
            appOptions.hideLoading('createBLEConnection catch');

        }
    })
}


/**
 * 关闭蓝牙适配器
 */
function closeBLEAdapter() {
    uni.closeBluetoothAdapter({
        complete() {
            initParam()
        }
    })

    /*try{
        let res = await wxp.closeBluetoothAdapter();
        console.log("closeBLEAdapter ==>res:", res);
    } catch(res) {
        console.log("closeBLEAdapter ==>error:", res);
    }
    initParam();*/
}

/**
 * 断开蓝牙连接
 * 首先断开搜索，如果搜索
 */
function closeBLEConnection() {
    console.log('closeBLEConnection  1 ');
    return new Promise(function (resolve, reject) {
        stopBluetoothDevicesDiscovery('closeBLEConnection').then(function (res) {
            console.log('closeBLEConnection  2 ', res);
            //tempStr = ''; //清空
            if (deviceId) {
                uni.closeBLEConnection({
                    deviceId: deviceId,
                    success(res) {
                        status = false;
                        console.log("closeBLEConnection ==>res:", res);
                    }
                })

                /*try{
                    let res = await wxp.closeBLEConnection({
                        deviceId: deviceId,
                    });
                    status = false;
                    console.log("closeBLEConnection ==>res:", res);
                } catch(res) {
                    console.log("closeBLEConnection ==>error:", res);
                }*/
            }

            /*try{
                let res = await wxp.closeBluetoothAdapter();
                console.log("closeBLEAdapter ==>res:", res);
            } catch(res) {
                console.log("closeBLEAdapter ==>error:", res);
            }*/
            uni.closeBluetoothAdapter({
                complete() {
                    initParam();
                    resolve(0);
                },
                fail(err) {
                    reject(err.code);
                }
            })

            /*console.log('closeBLEConnection 3 ',res);
            initParam();
            resolve(0);*/
        }).catch(function (code) {
            reject(code);
        })
    });
}

/**
 * 初始化参数
 */
function initParam() {
    app.globalData.connected = false;
    _discoveryStarted = false;
    isnotExist = true;
    _deviceId = '';
    deviceId = '';
    msg_id = 1;

    uni.$emit(globalEvents.EVENT_BLE_CONNECT_CHANGED, {
        connected: false
    })
}


/**
 * 获取蓝牙所有服务
 */
async function getBLEDeviceServices(deviceId) {

    // 连接状态监听
    uni.onBLEConnectionStateChange(function (res) {
        console.log("onBLEConnectionStateChange:", res);
        // 该方法回调中可以用于处理连接意外断开等异常情况
        console.log(`device ${res.deviceId} state has changed, connected: ${res.connected}`)
        if (res.connected == false) {
            console.log("连接意外断开等****", _deviceId);
            initParam();
            //initParam();
            /**
             if (device_type == 1 && device_type == 2) {
                btErrorCallback(1010, ""); //?
            }*/
            uni.showToast({
                title: '与设备连接已断开,请查看蓝牙设备是否打开',
                icon: 'none',
                fail: (res) => {
                    console.log('getBLEDeviceServices showToastLoading 获取服务值 fail  ', res);
                },
                success: (res) => {
                    console.log('getBLEDeviceServices showToastLoading 获取服务值 success', res);
                }
            });
        }
    });
    console.log("🚀 ~getBLEDeviceServices deviceId", deviceId)

    uni.getBLEDeviceServices({
        deviceId: deviceId,
        success: (res) => {
            console.log("获取蓝牙设备所有服务(service)", res);
            console.log("res.services", res.services, "res.services.length", res.services.length);
            for (let i = 0; i < res.services.length; i++) {
                let tmpUuid = res.services[i].uuid;
                console.log("tmpUuid", tmpUuid);
                //实际测试发现，微信小程序可以正常连接，但是支付宝小程序无法正常连接，这里改一下逻辑
                /* #ifdef MP-ALIPAY */
                if ((res.services[i].isPrimary) && ((tmpUuid.indexOf(filterServiceUUID) != -1) || (tmpUuid.indexOf(filterServiceUUID.toLowerCase()) != -1))) {
                    console.log("我已经抵达", deviceId, res.services[i].uuid);
                    getBLEDeviceCharacteristics(deviceId, res.services[i].uuid)
                    return
                }
                /* #endif */
                /* #ifdef MP-WEIXIN */
                console.log("🚀 ~ res.services[i].isPrimary", res.services[i].isPrimary)
                console.log("🚀 ~ res.services[i].uuid", res.services[i].uuid)
                console.log("🚀 ~ serviceUUID[0]", serviceUUID[0])
                console.log("🚀 ~ deviceId", deviceId)
                if ((res.services[i].isPrimary) && (res.services[i].uuid.toLowerCase() == serviceUUID[0]) || res.services[i].uuid.toUpperCase() == serviceUUID[0]) {

                    console.log("我已经抵达", deviceId, res.services[i].uuid);

                    getBLEDeviceCharacteristics(deviceId, res.services[i].uuid)
                    return
                }
                /* #endif */


            }
        },
        fail: (res) => {
            console.log('getBLEDeviceServices fail', res);
            //btErrorCallback(res.errCode, "");
            errorCallback(res.errCode, res.errMsg);
        }
    })

}


/**
 * 获取蓝牙特征值
 */
function getBLEDeviceCharacteristics(deviceId, serviceId) {
    uni.getBLEDeviceCharacteristics({
        deviceId: deviceId,
        serviceId: serviceId,
        success: (res) => {
            console.log('蓝牙设备特征值信息:', res);
            app.globalData.connected = true; // 链接上了


            uni.showToast({
                title: '设备连接成功',
            })

            uni.$emit(globalEvents.EVENT_BLE_CONNECT_CHANGED, {
                connected: true
            })

            msg_id = 1;
            //setServicesUUID();
            for (let i = 0; i < res.characteristics.length; i++) {
                let item = res.characteristics[i]

                // #ifdef MP-WEIXIN
                var itemUUID = item.uuid; //转小写
                var oriItemUUID = item.uuid
                // #endif

                // #ifdef MP-ALIPAY
                var itemUUID = item.characteristicId; //转小写
                var oriItemUUID = item.characteristicId
                // #endif


                if (isYTDevice(app.globalData.device_type)) { //售卖机
                    itemUUID = itemUUID.toLowerCase(); //转小写
                } else { 
                    itemUUID = itemUUID.toUpperCase(); // 转大写
                }

                console.log(i + ' notifyUUID ', notifyUUID);
                console.log(i + ' itemUUID ', itemUUID);
                console.log(i + ' writeUUID ', writeUUID);
                console.log(i + ' item.properties.notify ', item.properties.notify);
                console.log(i + ' item.properties.indicate ', item.properties.indicate);
                console.log(i + ' item.properties.write  ', item.properties.write);
                console.log(i + ' item.properties.read  ', item.properties.read);

                if (notifyUUID == itemUUID && (item.properties.notify || item.properties.indicate)) { // 服务，监听数据变化
                    console.log('调用notifyBLECharacteristicValueChange前', oriItemUUID);
                    uni.notifyBLECharacteristicValueChange({
                        deviceId: deviceId,
                        serviceId: serviceId,
                        characteristicId: oriItemUUID,
                        state: true,
                        success(res) {
                            console.log('notification通知数据', res);
                            status = true;
                        },
                        fail(res) {
                            console.log('notifyBLECharacteristicValueChange fali', res);
                        }
                    })
                }

                if (item.properties.write) {
                    /* #ifdef MP-WEIXIN */
                    if (itemUUID != writeUUID && itemUUID.toLowerCase() != writeUUID.toLowerCase()) return
                    /* #endif */
                    /* #ifdef MP-ALIPAY */
                    if (writeUUID.indexOf(itemUUID) == -1 && writeUUID.toLowerCase().indexOf(itemUUID.toLowerCase()) == -1) return
                    /* #endif */
                    console.log('f蓝牙设备 访问 1：');
                    _deviceId = deviceId
                    console.log("🚀 ~ f蓝牙设备 访问 1 _deviceId", _deviceId)
                    _serviceId = serviceId
                    _characteristicId = oriItemUUID

                    //发送指令【说明：如需连接设备后发相关指令可以在这里调用】
                    if (app.globalData.device_type == sh_device_type) { //格子机
                        //powerBank.send(); //充电开机指令
                    } else if (isYTDevice(app.globalData.device_type)) { //售卖机
                        console.log('蓝牙设备 访问 2 ：');
                        vendingObj.checkConnect(); //握手指令
                    } else if (app.globalData.device_type == wxc_device_type
                        || app.globalData.device_type == wxc_device_type_new) { // 无线充						
                        uni.readBLECharacteristicValue({
                            // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
                            deviceId,
                            // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
                            serviceId,
                            // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
                            characteristicId: item.uuid,
                            success(res) {
                                console.log('readBLECharacteristicValue ', res);
                                console.log('readBLECharacteristicValue:', res.errCode)
                            }
                        })

                    } else {

                    }
                }
                if (item.properties.read) {
                    /* #ifdef MP-WEIXIN */
                    if (readUUID !== itemUUID || itemUUID.toLowerCase() !== readUUID.toLowerCase()) return
                    /* #endif */
                    /* #ifdef MP-ALIPAY */
                    if (readUUID.indexOf(itemUUID) == -1 || readUUID.toLowerCase().indexOf(itemUUID.toLowerCase()) == -1) return
                    /* #endif */
                    console.log('蓝牙设备 访问 2：');
                    //_deviceId = deviceId
                    // _serviceId = serviceId
                    //_characteristicId = item.uuid
                    //发送指令【说明：如需连接设备后发相关指令可以在这里调用】
                    if (app.globalData.device_type == sh_device_type) { //格子机
                        //powerBank.send(); //充电开机指令
                    } else if (isYTDevice(app.globalData.device_type)) { //售卖机
                        console.log('蓝牙设备 访问 2 ：');
                        //vendingObj.checkConnect(); //握手指令
                    } else if (app.globalData.device_type == wxc_device_type) { // 无线充						
                        

                    } else {
                        
                    }
                }
            }
        },
        fail: (res) => {
            console.log('getBLEDeviceCharacteristics fail', res)
            //btErrorCallback(res.errCode, "");
            errorCallback(res.errCode, res.errMsg);
        }
    })


    // 操作之前先监听，保证第一时间获取数据
    uni.onBLECharacteristicValueChange(function (res) {
        console.log(`characteristic ${res.characteristicId} has changed, now is ${res.value}`)
        console.log("操作类型:" + action_type);

        // #ifdef MP-WEIXIN
        var resData = ab2hex(res.value);
        // #endif

        // #ifdef MP-ALIPAY
        var resData = res.value
        // #endif

        console.log("设备返回数据--->", resData); //5d0000000001be304d
        // ff550e02010000000000000100a6
        // 判断不同类型处理数据
        if (isYTDevice(app.globalData.device_type)) {
            msg_id++;
            console.log('开始调用 自动售卖机====> 处理返回的数据');
            vendingObj.handleResData(resData); //处理返回数据
        } else if (app.globalData.device_type == sh_device_type) {
            vendingObjOne.handleResData(resData); //处理返回数据
        } else if (app.globalData.device_type == wxc_device_type) {
            vendingObjTwo.handleResData(resData); //处理返回数据
        } else if (app.globalData.device_type == wxc_device_type_new) {
            vendingObjThree.handleResData(resData); //处理返回数据
        }
    })
}

/**
 * 检查是否打开蓝牙
 * 未连接设备前检测
 */
function checkIsOpenBluetooth(isEXec) {
    uni.openBluetoothAdapter({
        success: (res) => {
            isEXec(true);
        },
        fail: (res) => {
            uni.showModal({
                title: '提示',
                content: '请检查手机蓝牙是否打开',
                showCancel: false
            })
            isEXec(false);
        }
    })
}


/**
 * 蓝牙连接过程中错误码
 * 10000 / 10006
 */
function bluetoothStatus(errorType, msg = '') {
    console.log('bluetoothStatus', errorType);
    if (errorType) {
        switch (errorType) {
            //case 10000:
            case 10001:
                let cnt = '请检查手机蓝牙是否打开';
                if (appOptions.getPlatform() == 'android') {
                    cnt = '请检查手机蓝牙和定位是否打开'
                }
                uni.$emit(globalEvents.EVENT_BLE_NOT_OPEN, {})
                // uni.showToast({
                //     title: '请检查手机蓝牙是否打开',
                //     icon: 'none'
                // })
                uni.showModal({
                    title: '提示',
                    content: '请检查手机蓝牙是否打开',
                    showCancel: false
                })
                break;
            case 10002:
                uni.showToast({
                    title: '没有找到指定设备',
                    icon: 'none'
                })
                break;
            case 10003:
                uni.showToast({
                    title: '连接失败',
                    icon: 'none'
                })
                closeBLEConnection();
                break;
            case 10004:
                uni.showToast({
                    title: '没有找到指定服务',
                    icon: 'none'
                })
                closeBLEConnection();
                break;
            case 10005:
                uni.showToast({
                    title: '没有找到指定特征值',
                    icon: 'none'
                })
                closeBLEConnection();
                break;
            case 10007:
            case 10008:
                uni.showToast({
                    title: '设备蓝牙连接太频繁，请稍后重试',
                    icon: 'none'
                })
                break;
            case 10013:
                uni.showToast({
                    title: '设备启动失败，请重试',
                    icon: 'none'
                })
                break;
            case 10009:
                uni.showModal({
                    title: '提示',
                    content: '当前系统版本过低，请更新版本体验',
                    showCancel: false
                })
                break;
            case 10012:
                uni.showToast({
                    title: '连接超时',
                    icon: 'none'
                })
                break;
        }
    } else {
        if (msg) {
            uni.showToast({
                title: msg,
                icon: 'none'
            })
        }

    }
}

/**
 * 写入数据
 */
function writeData(hex, title = '', action = '') {
    if (!status) {

        return;
    }
    console.log('writeData : _deviceId ', _deviceId);
    if (!_deviceId) {
        btWriteCallback('w'); // 写入的时候，设备编号为空
        return;
    }

    if (title) {
        appOptions.showLoading(title, 'writeData');
    }

    setTimeout(() => {
        //这里使用`TypedArray视图`中`Uint8Array（无符号 8 位整数）`操作
        var enDataBuf = new Uint8Array(hex);
        console.log('enDataBuf', enDataBuf);
        var buffer1 = enDataBuf.buffer;
        console.log("🚀 ~ _deviceId", _deviceId)
        console.log("🚀 ~ _serviceId", _serviceId)
        console.log("🚀 ~ _characteristicId", _characteristicId)
        uni.writeBLECharacteristicValue({
            deviceId: _deviceId,
            serviceId: _serviceId,
            characteristicId: _characteristicId,
            value: buffer1,
            success: (res) => {
                console.log("🚀 ~ res", res)
                if (action_type != OptionEnum.Recharge) {
                    btWriteCallback('success', currentOrderSN);
                }
                if (app.globalData.device_type == wxc_device_type) {
                    appOptions.showLoading('正在写入指令', 'write');
                    setTimeout(() => {
                        appOptions.hideLoading('正在写入指令 end', 'write');
                        vendingObjTwo.readChargeStatus()
                    }, 1000)

                } else if (app.globalData.device_type == wxc_device_type_new) {
                    appOptions.showLoading('正在写入指令', 'write');
                    setTimeout(() => {
                        appOptions.hideLoading('正在写入指令 end', 'write');
                        vendingObjThree.readChargeStatus()
                    }, 1000)

                }
                console.log("写数据返回结果", res.errMsg);
            },
            fail(res) {
                console.log("写数据失败..", res);
                //btErrorCallback(res.errCode, "");
                errorCallback(res.errCode, res.errMsg);
            },
            complete: (res) => {
                if (title) {
                    appOptions.hideLoading('writeData');

                }
            }
        })

    }, 1000)
}

/**
 * 售卖机：圆筒机  数据类型：0x80
 */
var vendingObj = {
    /**
     * 链接握手
     */
    checkConnect: function () {
        // 0x5C,0x5F,0x57,0x59,0x5E
        console.log('checkConnect 访问：');
        status = true;
        let buff_array = [];
        buff_array.push(0xFF); // 1
        buff_array.push(0x55); // 2
        buff_array.push(0x12); // 3
        buff_array.push(0x01); // 4
        buff_array.push(parseInt(msg_id, 16));
        buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
        buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
        buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
        buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
        buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
        buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
        buff_array.push(0x01);// 指令
        buff_array.push(0x5C);// 固定数据
        buff_array.push(0x5F);// 固定数据
        buff_array.push(0x57);// 固定数据
        buff_array.push(0x59);// 固定数据
        buff_array.push(0x5E);// 固定数据
        buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位
        console.log('握手的指令：', buff_array);
        //var cryptoKey = new Uint8Array(key);
        //enKEY = cryptoKey;
        //得出加密后的指令, 十六进制的数据
        //var enHEX = cryptoService.updateEncrypt(strKey, enKEY);
        writeData(buff_array);
    },
    /**
     * 查询设备信息
     */
    queryDeviceInfo: function () {
        action_type = OptionEnum.ReadBattery; //改变操作类型
        let hex = [0x69, 0xf2, 0x00, 0x89];
        writeData(hex); //写入数据
    },

    /**
     * 开锁指令
     */
    openVendingLock: function (callBack, channel, order_sn = '') {
        currentSerialVal = channel;
        currentOrderSN = order_sn;
        status = true;
        action_type = OptionEnum.VendingOpenLock; // 开锁
        btOpenCallback = callBack;
        //获取当前开锁的编号及转换
        //let getCurrentVal = Number(currentSerialVal);
        //getCurrentVal = getCurrentVal.toString(16);
        // let tempVal = '0x0' + getCurrentVal;
        let tempVal = parseInt(channel, 16);
        console.log('====开锁编号===》', tempVal);
        let buff_array = [];
        buff_array.push(0xFF); // 1
        buff_array.push(0x55); // 2
        buff_array.push(0x12); // 3
        buff_array.push(0x01); // 4 ： 上位机 --> 设备
        buff_array.push(parseInt(msg_id, 16));       // 消息序列号
        buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
        buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
        buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
        buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
        buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
        buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
        buff_array.push(0x02);// 指令
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(tempVal);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位
        writeData(buff_array, '正在开门...');
    },
    /**
     * 全开锁指令
     */
    openAllLock: function (callBack) {
        status = true;
        action_type = OptionEnum.VendingOpenLock; // 开锁
        btOpenCallback = callBack;
        //获取当前开锁的编号及转换
        //let getCurrentVal = Number(currentSerialVal);
        //getCurrentVal = getCurrentVal.toString(16);
        // let tempVal = '0x0' + getCurrentVal;0xB1,0xB2,0xB3,0xB4,0xB5
        let buff_array = [];
        buff_array.push(0xFF); // 1
        buff_array.push(0x55); // 2
        buff_array.push(0x12); // 3
        buff_array.push(0x01); // 4 ： 上位机 --> 设备
        buff_array.push(parseInt(msg_id, 16));       // 消息序列号
        buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
        buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
        buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
        buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
        buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
        buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
        buff_array.push(0x04);// 指令
        buff_array.push(0xB1);// 固定数据
        buff_array.push(0xB2);// 固定数据
        buff_array.push(0xB3);// 固定数据
        buff_array.push(0xB4);// 固定数据
        buff_array.push(0xB5);// 固定数据
        buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位
        console.log('全开锁指令：', buff_array);
        console.log('全开始指令：', buf2hex(buff_array));
        writeData(buff_array);
    },

    /**
     * 处理格子机查询信息电量回调
     * 目的：获取到相关数据，发送给后端（查看设备电量）
     */
    readBatteryCallBack: function (battery) {
        console.log("=======>>>电量:", battery);
        action_type = OptionEnum.None;

        //这里获取到电量, 返回给index.js页面
        if (btOpenCallback != null) {
            btOpenCallback(battery, OptionEnum.ReadBattery);
        } else {
            console.log("是否为空=======标签 2");
        }
    },

    /**
     * 处理开锁成功回调
     */
    openLockCallback: function (resData) {
        console.log('openLockCallback开锁回调 数据 ', resData);
        var isOpenLock = false;
        if (resData) {
            var length = resData.length;
            var cmd = resData.substr(length - 6, 2);
            var status = resData.substr(length - 4, 2);

            if ((cmd == '02' || cmd == '04') && status == '03') {
                isOpenLock = true;
            }
            action_type = OptionEnum.None;
        }
        if (btOpenCallback != null) {
            btOpenCallback(isOpenLock, OptionEnum.VendingOpenLock, currentSerialVal, currentOrderSN);
        }
    },

    //充电指令
    rechargeDevice: function (time, callback) {
        console.log('准备写入充电指令，time=', time)
        //转16进制
        let hexTime = '0x' + parseInt(time).toString(16)
        console.log('充电时间转16进制：', hexTime)

        status = true;
        action_type = OptionEnum.Recharge; // 充电
        btRechargeCallback = callback;
        console.log("🚀 ~ btRechargeCallback", btRechargeCallback)

        let buff_array = [];
        buff_array.push(0xFF); // 1
        buff_array.push(0x55); // 2
        buff_array.push(0x12); // 3
        buff_array.push(0x01); // 4 ： 上位机 --> 设备
        buff_array.push(parseInt(msg_id, 16));       // 消息序列号
        buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
        buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
        buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
        buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
        buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
        buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
        buff_array.push(0x05);// 指令
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(hexTime);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位
        console.log('充电指令：', buff_array);
        console.log('充电指令：', buf2hex(buff_array));
        writeData(buff_array);
    },

    //获取设备电压
    getMachineVolt(callback) {
        console.log('准备写入获取电压指令')

        status = true;
        action_type = OptionEnum.GetVolt;
        btGetVoltCallback = callback;

        let buff_array = [];
        buff_array.push(0xFF); // 1
        buff_array.push(0x55); // 2
        buff_array.push(0x12); // 3
        buff_array.push(0x01); // 4 ： 上位机 --> 设备
        buff_array.push(parseInt(msg_id, 16));       // 消息序列号
        buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
        buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
        buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
        buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
        buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
        buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
        buff_array.push(0x06);// 指令
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(0x00);// 固定数据
        buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位
        console.log('获取设备电压指令：', buff_array);
        console.log('获取设备电压指令：', buf2hex(buff_array));
        writeData(buff_array);
    },

    /**
     * 处理充电回调
     */
    rechargeCallback: function (resData) {
        console.log('充电指令回调，res=', resData)

        if (resData) {
            var length = resData.length;
            var cmd = resData.substr(length - 6, 2);
            var status = resData.substr(length - 4, 2);

            console.log('充电指令回调，cmd=', cmd, ' status=', status)
            if (cmd == '05' && status == '06') {
                //充电成功
                if (btRechargeCallback != null) {
                    btRechargeCallback()
                }
            }

            action_type = OptionEnum.None;
        }
    },

    /**
     * 获取充电电压回调
     */
    getVoltCallback: function (resData) {
        console.log('获取充电电压回调，res=', resData)

        if (resData) {
            var length = resData.length;
            var cmd = resData.substr(length - 6, 2);
            var status = resData.substr(length - 4, 2);

            console.log('获取充电电压回调，cmd=', cmd, ' status=', status)
            status = status.toString()
            status = status[0] + '.' + status[1]

            if (cmd == '06') {
                if (btGetVoltCallback) {
                    btGetVoltCallback({ volt: status })
                }
            }


            action_type = OptionEnum.None;
        }
    },


    /**
     * 处理返回数据
     * 例如： 00f05d09000001be304d
     * ff550e02010000000000000100a6
     */
    handleResData: function (resData) {
        let checkStatus = resData.substring(2, 4);

        if (checkStatus.toUpperCase() == 'F0' && action_type == OptionEnum.Connection) { //校验状态
            vendingObj.queryDeviceInfo(); //查询设备信息

        } else if (action_type == OptionEnum.ReadBattery) { //操作的是获取设备电量

            let batteryVal = resData.substring(6, 8);
            batteryVal = parseInt(batteryVal, 16);
            vendingObj.readBatteryCallBack(batteryVal);

        } else if (action_type == OptionEnum.VendingOpenLock) { //操作的是 开锁
            vendingObj.openLockCallback(resData);
        } else if (action_type == OptionEnum.Recharge) {
            vendingObj.rechargeCallback(resData)
        } else if (action_type == OptionEnum.GetVolt) {
            vendingObj.getVoltCallback(resData)
        }
    }
}

/**
 * 售货机：口罩机， 设备类型： 0x65,64
 */
var vendingObjOne = {
    /**
     * 查询设备信息
     */
    queryDeviceInfo: function () {
        //action_type = OptionEnum.ReadBattery; //改变操作类型
        //let hex = [0x69, 0xf2, 0x00, 0x89];
        //writeData(hex); //写入数据
    },

    /**
     * 开锁指令
     */
    openVendingLock: function (callBack, channel, order_sn = '', led = 1, led_time = 10) {
        currentSerialVal = channel;
        currentOrderSN = order_sn ? order_sn : getOrdersn(18, 100);
        status = true;
        action_type = OptionEnum.VendingOpenLock; // 开锁
        btOpenCallback = callBack;
        //获取当前开锁的编号及转换
        let tempVal = parseInt(channel, 16);
        let buff_array = [];
        buff_array.push(0x7E); // 1 帧头
        buff_array.push(0x01); // 2 上位机 --> 设备
        buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 3 设备号1
        buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 4 设备号2
        buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 5 设备号3
        buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 6 设备号4
        buff_array.push(parseInt(app.globalData.device_sn.substr(12, 2), 16));// 7 设备号5
        buff_array.push(parseInt(app.globalData.device_sn.substr(14, 2), 16));// 8 设备号6
        buff_array.push(0x00);  // 9  固定数据
        buff_array.push(0x00);  // 10 固定数据
        buff_array.push(0xA0);  // 11 开锁指令
        buff_array.push(tempVal);// 12 格子编号
        buff_array.push(parseInt(led, 16));   //  13 灯是否亮
        buff_array.push(parseInt(led_time, 10));   //  14 亮灯时长
        buff_array.push(parseInt(currentOrderSN.substr(0, 2), 10));// 15 订单编号1
        buff_array.push(parseInt(currentOrderSN.substr(2, 2), 10));// 16 订单编号2
        buff_array.push(parseInt(currentOrderSN.substr(4, 2), 10));// 17 订单编号3
        buff_array.push(parseInt(currentOrderSN.substr(6, 2), 10));// 18 订单编号4
        buff_array.push(0x00);  // 19 保留位
        let check = crc.veri(new Uint8Array(buff_array), app.globalData.key);
        buff_array.push(parseInt(check.substr(2, 2), 16)); // 校验位
        writeData(buff_array, '正在开门...');
        
    },
    /**
     * 处理格子机查询信息电量回调
     * 目的：获取到相关数据，发送给后端（查看设备电量）
     */
    readBatteryCallBack: function (battery) {
        console.log("=======>>>电量:", battery);
        action_type = OptionEnum.None;

        //这里获取到电量, 返回给index.js页面
        if (btOpenCallback != null) {
            btOpenCallback(battery, OptionEnum.ReadBattery);
        } else {
            console.log("是否为空=======标签 2");
        }
    },

    /**
     * 处理开锁成功回调
     */
    openLockCallback: function (resData) { //  7e02feef3c0819849b01 b0 00 01 14 15 04 11 00 00 ea
        //7e02feef3c0819849b01b0 00 01 2a 2f 1c 26 00 00 82
        console.log('openLockCallback开锁回调 数据 ', resData);
        var isOpenLock = false;
        if (resData) {
            var length = resData.length;

            var cmd = resData.substr(length - 20, 2).toLowerCase();
            var status = resData.substr(length - 18, 2);
            console.log('开锁命令 ', cmd + " , 开锁状态 ： " + status);
            if ((cmd == 'b0'.toLowerCase()) && status == '00') {
                isOpenLock = true;
            }
            action_type = OptionEnum.None;
        }
        if (btOpenCallback != null) {
            btOpenCallback(isOpenLock, OptionEnum.VendingOpenLock, currentSerialVal, currentOrderSN);
        }
    },

    /**
     * 处理返回数据
     * 例如： 00f05d09000001be304d
     * ff550e02010000000000000100a6
     *
     */
    handleResData: function (resData) { // 7e02feef3c0819849b01b000012a2f0317000017
        let checkStatus = resData.substring(2, 4);

        if (checkStatus.toUpperCase() == 'F0' && action_type == OptionEnum.Connection) { //校验状态
            vendingObjOne.queryDeviceInfo(); //查询设备信息

        } else if (action_type == OptionEnum.ReadBattery) { //操作的是获取设备电量

            let batteryVal = resData.substring(6, 8);
            batteryVal = parseInt(batteryVal, 16);
            vendingObjOne.readBatteryCallBack(batteryVal);

        } else if (action_type == OptionEnum.VendingOpenLock) { //操作的是 开锁

            vendingObjOne.openLockCallback(resData);
        }
    }
}


/**
 * 无线充， 设备类型： 0x88
 */
var vendingObjTwo = {
    //充电指令
    rechargeDevice: function (hour, min, callback) {
        console.log('准备写入充电指令，min=', min)
        console.log('准备写入充电指令，hour=', hour)
        //转16进制
        hour = parseInt(min / 60);//取整数
        min = Math.round(min % 60);//取余数
        console.log('准备写入充电指令，转换后的：', `${hour} 小时,${min} 分钟`)
        let hexHour = 0x00;
        if (hour > 0) {
            hexHour = '0x' + parseInt(hour).toString(16);
        }
        let hexMin = 0x00;
        if (min > 0) {
            hexMin = '0x' + parseInt(min).toString(16);
        }
        console.log('充电时间转16进制：', hexHour, hexMin)

        status = true;
        action_type = OptionEnum.Recharge; // 充电
        btRechargeCallback = callback;

        let buff_array = [];
        buff_array.push(0xFE); // 1
        buff_array.push(0xD5); // 2
        buff_array.push(hexHour); // 3  小时 0x00~0x30  0~48小时 最大支持48小时
        buff_array.push(hexMin); // 4  分钟 0x00~0x3c  0~60分钟
        buff_array.push(0x00);  // 5 固定数据
        buff_array.push(0x00);	// 6 固定数据
        buff_array.push(0x00);	// 7 固定数据
        buff_array.push(0x00);	// 8 固定数据
        buff_array.push(0x00);  // 9 固定数据
        buff_array.push(0x00);	// 10 固定数据
        buff_array.push(0x00);  // 11 固定数据
        buff_array.push(0x00);	// 12 固定数据
        buff_array.push(0x00);	// 13 固定数据
        buff_array.push(0x00);	// 14 固定数据
        buff_array.push(0x00);  // 15 固定数据
        buff_array.push(0x00);	// 16 固定数据
        buff_array.push(0x00);	// 17 固定数据
        buff_array.push(0x00);  // 18 固定数据
        buff_array.push(0x00);	// 19 固定数据
        buff_array.push(0X45); // 校验位
        console.log('充电指令：', buff_array);
        console.log('充电指令：', buf2hex(buff_array));
        writeData(buff_array);
    },

    /**
     * 处理充电回调
     */
    rechargeCallback: function (resData) {
        console.log('充电指令回调，res=', resData)
        console.log("🚀 ~ action_type", action_type)
        console.log("🚀 ~  OptionEnum.ReadRecharge", OptionEnum.ReadRecharge)
        console.log("🚀 ~ OptionEnum.ReadRechargeCallback", OptionEnum.ReadRechargeCallback)
        console.log("🚀 ~ (action_type == OptionEnum.ReadRecharge || action_type == OptionEnum.ReadRechargeCallback)", (action_type == OptionEnum.ReadRecharge || action_type == OptionEnum.ReadRechargeCallback))
        // || action_type == OptionEnum.Recharge
        if (resData && (action_type == OptionEnum.ReadRecharge || action_type == OptionEnum.ReadRechargeCallback)) {




            var length = resData.length;
            //var cmd = resData.substr(length - 6, 2);
            var status = resData.substr(length - 2, 2);

            console.log('充电指令回调 status=', status)
            if (status == '46') {
                //充电成功
                if (action_type == OptionEnum.ReadRecharge) {
                    if (btRechargeCallback != null) {
                        btRechargeCallback(true);
                    }
                } else if (action_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(true);
                    }
                }


            }
            if (status == '00') {
                //充电失败
                if (action_type == OptionEnum.ReadRecharge) {
                    if (btRechargeCallback != null) {
                        btRechargeCallback(false);
                    }
                } else if (action_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }

            }

            action_type = OptionEnum.None;
        }
    },


    /**
     * 处理返回数据
充电状态中：
FE D5 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 46
没有充电中：
FE D5 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00

     * 
     */
    handleResData: function (resData) { // 7e02feef3c0819849b01b000012a2f0317000017 
        console.log("🚀 ~ resData", resData)
        vendingObjTwo.rechargeCallback(resData);
    },


    readChargeStatus: function (flag) {
        // 无线充						
        console.log("🚀 ~ 无线充   开始读状态",)

        uni.readBLECharacteristicValue({
            // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
            deviceId: _deviceId,
            // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
            serviceId: _serviceId,
            // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
            characteristicId: readUUID,
            success(res) {
                action_type = flag ? OptionEnum.ReadRechargeCallback : OptionEnum.ReadRecharge;
                console.log('readBLECharacteristicValue ', res);
                console.log('readBLECharacteristicValue:', res.errCode)
                if (res.errno === 1509000) {//未充电，直接回调
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }
            }, fail: (err) => {
                console.log("🚀 ~ err readBLECharacteristicValue", err)

            }
        })



    }
}
/**
 * 无线充， 设备类型： 0x89
 */
var vendingObjThree = {
    //充电指令
    rechargeDevice: function (hour, min, callback) {
        console.log('准备写入充电指令，min=', min)
        console.log('准备写入充电指令，hour=', hour)
        //转16进制
        hour = parseInt(min / 60);//取整数
        min = Math.round(min % 60);//取余数
        console.log('准备写入充电指令，转换后的：', `${hour} 小时,${min} 分钟`)
        let hexHour = 0x00;
        if (hour > 0) {
            hexHour = '0x' + parseInt(hour).toString(16);
        }
        let hexMin = 0x00;
        if (min > 0) {
            hexMin = '0x' + parseInt(min).toString(16);
        }
        console.log('充电时间转16进制：', hexHour, hexMin)

        status = true;
        action_type = OptionEnum.Recharge; // 充电
        btRechargeCallback = callback;

        let buff_array = [];
        buff_array.push(0xA0); // 1
        buff_array.push(0xD5); // 2
        buff_array.push(hexHour); // 3  小时 0x00~0x30  0~48小时 最大支持48小时
        buff_array.push(hexMin); // 4  分钟 0x00~0x3c  0~60分钟
        buff_array.push(0x00);  // 5 固定数据
        buff_array.push(0x00);	// 6 固定数据
        buff_array.push(0x00);	// 7 固定数据
        buff_array.push(0x00);	// 8 固定数据
        buff_array.push(0x00);  // 9 固定数据
        buff_array.push(0x00);	// 10 固定数据
        buff_array.push(0x00);  // 11 固定数据
        buff_array.push(0x00);	// 12 固定数据
        buff_array.push(0x00);	// 13 固定数据
        buff_array.push(0x00);	// 14 固定数据
        buff_array.push(0x00);  // 15 固定数据
        buff_array.push(0x00);	// 16 固定数据
        buff_array.push(0x00);	// 17 固定数据
        buff_array.push(0x00);  // 18 固定数据
        buff_array.push(0x00);	// 19 固定数据
        buff_array.push(0X45); // 校验位
        console.log('充电指令：', buff_array);
        console.log('充电指令：', buf2hex(buff_array));
        writeData(buff_array);
    },

    /**
     * 处理充电回调
     */
    rechargeCallback: function (resData) {
        console.log('充电指令回调，res=', resData)
        if (resData && (action_type == OptionEnum.ReadRecharge || action_type == OptionEnum.ReadRechargeCallback || action_type == OptionEnum.Recharge)) {
            var length = resData.length;
            //var cmd = resData.substr(length - 6, 2);
            var status = resData.substr(length - 2, 2);

            console.log('充电指令回调 status=', status)
            if (status == '46') {
                //充电成功
                if (action_type == OptionEnum.ReadRecharge) {
                    if (btRechargeCallback != null) {
                        btRechargeCallback(true);
                    }
                } else if (action_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(true);
                    }
                }


            }
            if (status == '00') {
                //充电失败
                if (action_type == OptionEnum.ReadRecharge) {
                    if (btRechargeCallback != null) {
                        btRechargeCallback(false);
                    }
                } else if (action_type == OptionEnum.ReadRechargeCallback) {
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }

            }

            action_type = OptionEnum.None;
        }
    },


    /**
     * 处理返回数据
充电状态中：
A0 D5 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 46
没有充电中：
A0 D5 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00

     * 
     */
    handleResData: function (resData) { // 7e02feef3c0819849b01b000012a2f0317000017 
        console.log("🚀 ~ resData", resData)
        vendingObjThree.rechargeCallback(resData);
    },


    readChargeStatus: function (flag) {
        // 无线充						
        console.log("🚀 ~ 无线充   开始读状态",)
        uni.readBLECharacteristicValue({
            // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
            deviceId: _deviceId,
            // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
            serviceId: _serviceId,
            // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
            characteristicId: readUUID,
            success(res) {
                action_type = flag ? OptionEnum.ReadRechargeCallback : OptionEnum.ReadRecharge;
                console.log('readBLECharacteristicValue ', res);
                console.log('readBLECharacteristicValue:', res.errCode)
                if (res.errno === 1509000) {//未充电，直接回调
                    if (readRechargeCallback != null) {
                        readRechargeCallback(false);
                    }
                }
            }
        })


    }
}


function readChargeStatus(callback) {

    if (app.globalData.device_type === wxc_device_type) {
        readRechargeCallback = callback
        vendingObjTwo.readChargeStatus(true);
    } else if (app.globalData.device_type === wxc_device_type_new) {
        readRechargeCallback = callback
        vendingObjThree.readChargeStatus(true);
    }
}


function strCheckXor(str) { // 异或运算
    var list = [];
    var ar2 = 0;
    for (let i = 0; i < str.length - 1; i += 2) {
        list.push(parseInt((str.substring(i, i + 2)), 16)) //将字符串切割为两位字符的数组并转为16进制的整型。
    }
    for (let j = 0; j < list.length; j++) {
        ar2 ^= list[j] //前两位的数组的异或结果在和第三位数组异或，以此类推得到最终结果
    }
    return parseInt(ar2).toString(16) //将得到结果进行整型，转为16进制得到最终结果
}
function bufCheckXor(buff_array) {
    var ar2 = 0;
    for (let j = 0; j < buff_array.length; j++) {
        ar2 ^= buff_array[j] //前两位的数组的异或结果在和第三位数组异或，以此类推得到最终结果
    }
    return parseInt(ar2).toString(16) //将得到结果进行整型，转为16进制得到最终结果
}
// 二进制处理方法
function buf2hex(buffer) { // buf转16进制
    return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('')
}

//取16位 订单号
function getOrdersn(minNum, maxNum) {
    var today = new Date();
    var day = today.getDate(); //获取当前日(1-31)
    var month = today.getMonth() + 1; //显示月份比实际月份小1,所以要加1
    var year = today.getYear();  //获取完整的年份(4位,1970-????)  getFullYear()
    var years = today.getFullYear();
    years = years < 99 ? "20" + years : years;
    month = month < 10 ? "0" + month : month;  //数字<10，实际显示为，如5，要改成05
    day = day < 10 ? "0" + day : day;
    var hh = today.getHours();
    hh = hh < 10 ? "0" + hh : hh;
    var ii = today.getMinutes();
    ii = ii < 10 ? "0" + ii : ii;
    var ss = today.getSeconds();
    ss = ss < 10 ? "0" + ss : ss;
    var dada = years.toString() + month.toString() + day.toString() + hh.toString() + ii.toString() + ss.toString();
    switch (arguments.length) {
        case 1:
            return dada + parseInt(Math.random() * minNum + 1, 10);
            break;
        case 2:
            return dada + parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
            break;
        default:
            return 0;
            break;
    }
}

/***
 * 设备开锁接口回调
 */
function openOneLock(callBack, channel, order_sn = '', led = 1, led_time = 10) {
    if (app.globalData.device_type === sh_device_type) {
        vendingObjOne.openVendingLock(callBack, channel, order_sn, led, led_time);
    } else if (isYTDevice(app.globalData.device_type)) {
        vendingObj.openVendingLock(callBack, channel, order_sn);
    }
}

/***
 * 设备开锁接口回调
 */
function openAllLock(callBack) {
    console.log('openAllLock');
    if (app.globalData.device_type === sh_device_type) {
        //vendingObjOne.openVendingLock(callBack,channel,order_sn ,led, led_time);
    } else if (isYTDevice(app.globalData.device_type)) {
        vendingObj.openAllLock(callBack);
    }
}

/***
 * 设备充电回调
 */
function rechargeDevice(time, callback, hour = 0) {
    console.log('rechargeDevice');
    if (app.globalData.device_type === sh_device_type) {
        //vendingObjOne.openVendingLock(callBack,channel,order_sn ,led, led_time);
    } else if (isYTDevice(app.globalData.device_type)) {
        console.log('准备调用vendingObj的rechargeDevice方法')
        vendingObj.rechargeDevice(time, callback);
    } else if (app.globalData.device_type === wxc_device_type) {
        console.log('准备调用vendingObjTwo的rechargeDevice方法')
        vendingObjTwo.rechargeDevice(hour, time, callback);
    } else if (app.globalData.device_type === wxc_device_type_new) {
        console.log('准备调用vendingObjTwo的rechargeDevice方法')
        vendingObjThree.rechargeDevice(hour, time, callback);
    }
}

//获取售货机电压（电量）
function getMachineVolt(callback) {
    console.log('获取售货机电压（电量）...')

    if (app.globalData.device_type === sh_device_type) {
        //vendingObjOne.openVendingLock(callBack,channel,order_sn ,led, led_time);
    } else if (isYTDevice(app.globalData.device_type)) {
        console.log('准备调用vendingObj的getMachineVolt方法')
        vendingObj.getMachineVolt(callback);
    }
}

//设置连接
function setConnectionActionType(callBack) {
    action_type = OptionEnum.Connection;
    btOpenCallback = callBack;
}

//设置重连
function setReconnectionActionType() {
    action_type = OptionEnum.Reconnection;
}

// 设置错误
function setbtErrorCallback(callBack) {
    btErrorCallback = callBack;
}

// 报错回调
function errorCallback(errCode, msg) {
    console.log('errorCallback', errCode);
    if (btErrorCallback && errCode) {
        console.log('errorCallback 1');
        btErrorCallback(errCode, "");
    } else {
        console.log('errorCallback 2');
        bluetoothStatus(errCode, msg);
    }
}

//设置关闭
function setCloseActionType(callBack) {
    action_type = OptionEnum.CloseLock;
    btOpenCallback = callBack;
}

//设置写入错误
function setWriteCallback(callBack) {
    btWriteCallback = callBack;
}


//清除
function clearCallBack() {
    btOpenCallback = null;
}

/**
 *         // console.log('准备写入充电指令，min=', min)
        // //转16进制
        // console.log('准备写入充电指令，转换后的：', `${min} 分钟`)
        // let minutes16 = 0;
        // if (min > 0) {
        //     minutes16 = min.toString(16).padStart(4, '0');
        // }
        // if (minutes16.length > 5) {
		// 	minutes16 = 'ffff'
		// }
        // console.log('充电时间转16进制：', minutes16)

        // let random1 = Math.round(Math.random()*255);
        // let random2 = Math.round(Math.random()*255);

        // console.log('BluetoothDetail.mac地址：', BluetoothDetail.mac);
        // let mac = clearSymbol(BluetoothDetail.mac);
        // console.log('mac地址：', mac);

        // let sn = "360260644211797"; // 98260644211797360260644211797
        // // sn = strToHexCharCode(sn);
        // console.log('sn地址：', sn);
        // sn = strToHexCharCode(sn);
        // console.log('sn地址16进制：', sn);

        // let buff_array = [];
        // buff_array.push(0x71); // 1
        // buff_array.push(0x0a); // 2
        // buff_array.push(parseInt(random1,16)); // 随机数1
        // buff_array.push(parseInt(random2,16)); // 随机数2
        // // mac地址 6个字节
        // buff_array.push(parseInt(mac.substr(0, 2),16)); // 
        // buff_array.push(parseInt(mac.substr(2, 2),16));
        // buff_array.push(parseInt(mac.substr(4, 2),16));
        // buff_array.push(parseInt(mac.substr(6, 2),16));
        // buff_array.push(parseInt(mac.substr(8, 2),16));
        // buff_array.push(parseInt(mac.substr(10, 2),16));

        // // 时长
        // buff_array.push(parseInt(to10(minutes16.substr(0, 2)),16));
        // buff_array.push(parseInt(to10(minutes16.substr(2, 2)),16));

        // // CRC校验
        // let crc32Str16 = random1.toString(16) + random2.toString(16) + mac + sn;
        // console.log("BLE crc32Str16 : ",crc32Str16);
		// let crc32Str16Arr = getStrArr(crc32Str16);
        // console.log("BLE crc32Str16Arr : ",crc32Str16Arr);
        // let crc32StrAscii = ''
		// crc32Str16Arr.forEach((item, index) => {
		// 	crc32StrAscii += hexCharCodeToStr(item)
		// });
        // console.log("BLE crc32StrAscii : ",crc32StrAscii);
        // let crc32 = CRC32.bstr(crc32StrAscii).toString(16);
        // console.log("BLE crc32 : ",crc32);
        // crc32 = crc32.padStart(8, '0');
        // console.log("BLE crc32 2 : ",crc32);
        // buff_array.push(parseInt(to10(crc32.substr(0, 2)),16));
        // buff_array.push(parseInt(to10(crc32.substr(2, 2)),16));
        // buff_array.push(parseInt(to10(crc32.substr(4, 2)),16));
        // buff_array.push(parseInt(to10(crc32.substr(6, 2)),16));
        
        // console.log('充电指令：', buff_array);
        // console.log('充电指令：', buf2hex(buff_array));
 */

export default {
    initBle: initBle,
    clearCallBack: clearCallBack,
    closeBLEConnection: closeBLEConnection,
    setConnectionActionType: setConnectionActionType,
    setReconnectionActionType: setReconnectionActionType,
    setBtErrorCallback: setbtErrorCallback,
    setCloseActionType: setCloseActionType,
    setWriteCallback: setWriteCallback,
    checkIsOpenBluetooth: checkIsOpenBluetooth,
    bluetoothStatus: bluetoothStatus,
    openVendingLock: openOneLock,
    openAllLock: openAllLock,
    rechargeDevice: rechargeDevice,
    getMachineVolt: getMachineVolt,
    isRechargeDevice: isRechargeDevice,
    getBleConnectStatus: getBleConnectStatus,
    readChargeStatus: readChargeStatus,
}