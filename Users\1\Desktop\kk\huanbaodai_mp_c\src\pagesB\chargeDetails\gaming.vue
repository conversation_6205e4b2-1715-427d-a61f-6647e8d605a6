    doGetUserOrderByNo(order_sn) {
      let params = {
        order_sn,
      }
      getUserOrderByNo(params).then((res) => {
        // console.log("--订单返回--", res);
        this.orderInfo = res.data
        // console.log("--订单信息--", this.orderInfo);
        res.data?.device_sn &&
          (getApp().globalData.device_sn = this.orderInfo?.device_sn)
        res.data?.machine_type &&
          (getApp().globalData.device_type = this.orderInfo?.machine_type)
        this.user_recharge = this.orderInfo?.user_recharge
        this.order_time = utils.parseTime(this.orderInfo?.add_time)
        // console.log("-----订单时间：", this.order_time);

        this.oldTime = this.orderInfo.start_time * 1000
        let endTime = this.orderInfo.end_time * 1000
        this.gameStatus = this.orderInfo.game_status
        // if (this.gameStatus > globalCodes.gameOrderStatus.STATUS_CHARGE_ING) {
        //   this.oldGameStatus = this.gameStatus;
        // }

        let nowTime = new Date().getTime() //现在时间
        //套餐时间 - （ 现在时间 - 开始时间） = 剩余充电时间
        this.remainTime = ((endTime - nowTime) / 1000 / 60).toFixed(2)
        let start_count_time = this.remainTime * 60 * 1000

        // 添加自动跳转逻辑
        if (Number(this.remainTime) > 0 && this.orderInfo.bind_ble_device && this.orderInfo.bind_ble_mac) {
          setTimeout(() => {
            const orderSn = this.orderInfo.order_sn
            console.log("满足条件，自动跳转到遥控页面，订单号:", orderSn)
            uni.navigateTo({
              url: `/pagesD/remote/index?order_sn=${orderSn}`,
            })
          }, 2000)
        }

        // console.log(
        //   "--时间--",
        //   this.orderInfo.start_time,
        //   this.orderInfo.end_time,
        //   endTime,
        //   nowTime,
        //   this.remainTime,
        //   start_count_time
        // );
      })
    },