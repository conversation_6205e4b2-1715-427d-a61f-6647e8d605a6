<template>
    <!--我的订单页面-->
    <view class="content">

        <BaseTabs :list="navArrCharge" @change="changeTab" />
        <CommonAd :ad="vAd.personalCenterListCustomAd" type="custom" />
        <ComList :loadingType="loadingType">
            <OrderCard v-for="item in listData" :key="item.id" :itemInfo="item" />
        </ComList>
        <LoginPopup />
        <CommonAd :ad="vAd.personalCenterInsertScreenAd" type="inter" />

    </view>
</template>
<script>

import BaseTabs from '../../components/base/BaseTabs.vue';
import myPull from "@/mixins/myPull"
import OrderCard from '../components/OrderCard.vue';
import LoginPopup from '../../components/LoginPopup.vue';
import { getOrderList } from "@/common/http/api"
import ComList from '../../components/list/ComList.vue';
import CommonAd from '../../components/WxAd/CommonAd.vue';
export default {
    components: { BaseTabs, OrderCard, LoginPopup, ComList, CommonAd },
    mixins: [myPull()],
    data() {
        return {
            navArrCharge: [
                {
                    name: "全部",
                    status: 0,
                },
                {
                    name: "待支付",
                    status: 3,
                },
                {
                    name: "已完成",
                    status: 1,
                },
                {
                    name: "订单异常",
                    status: 2,
                },
            ],
            selectNav: {

            }
        };
    },

    methods: {
        changeTab(item) {
            this.selectNav = item
            this.refresh()
        },
        getList(page, done) {
            let data = {
                page,
                status: this.selectNav.status,
            };
            getOrderList(data).then((res) => {
                done(res.data);
            });

        },
    },
    onLoad() {
        this.refresh()
    },
}
</script>

<style   lang='scss'>
page {
    background-color: $pageBgColor;
}
</style>

<style scoped  lang='scss'>
::v-deep .u-tabs__wrapper__nav__item__text {
  font-size: 30rpx !important;
}
::v-deep .u-tabs__wrapper__nav {
  width: 100%;
}
</style>