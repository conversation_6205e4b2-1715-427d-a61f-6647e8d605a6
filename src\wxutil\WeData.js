// 劫持 methods 内的方法  主要是  tap点击事件 和ui组件封装的 onClick事件
// 通过微信小程序We分析  上报埋点信息

(function () {
    const isObject = function (obj) {
        if (obj === undefined || obj === null) {
            return false;
        } else {
            return toString.call(obj) == "[object Object]";
        }
    };
    // 劫持Component
    const _componentProto_ = Component;
    Component = function (options) {
        //options.methods内有uniapp注入的事件处理器__e及mpHook
        Object.keys(options.methods).forEach(methodName => {
            if (methodName == "__e") {
                //劫持事件处理器
                eventProxy(options.methods, methodName)
            }
        })
        _componentProto_.apply(this, arguments);
    }

    function eventProxy(methodList, methodName) {
        //事件处理器的劫持
        const _funcProto_ = methodList[methodName];
        methodList[methodName] = function (e, i) {
            _funcProto_.apply(this, arguments);
            let prop = {};
            if (isObject(arguments[0])) {
                if (Object.keys(arguments[0]).length > 0) {
                    //记录触发页面信息
                    const pages = getCurrentPages();
                    const currentPage = pages[pages.length - 1];
                    prop["$page_path"] = currentPage.route; //页面路径
                    prop["$page_query"] = currentPage.options || {}; //页面携带的query参数
                    const type = arguments[0]["type"];
                    const current_target = arguments[0].currentTarget || {};
                    const dataset = current_target.dataset || {};
                    prop["$event_type"] = type;
                    prop["$event_timestamp"] = uni.$u.timeFormat(Date.now(), "yyyy-mm-dd hh:MM:ss");
                    prop["$element_id"] = current_target.id;
                    const eventDetail = arguments[0].detail;
                    prop["$event_detail"] = eventDetail;
                    prop["$event_user_openid"] = uni.getStorageSync('openid')
                    if (!!dataset.eventOpts && type) {
                        if (type == "tap" || type == 'onClick') { //只记录点击事件
                            const event_opts = dataset.eventOpts;
                            if (Array.isArray(event_opts) && event_opts[0].length === 2) {
                                let eventFunc = [];
                                event_opts[0][1].forEach(event => {
                                    eventFunc.push({
                                        name: event[0],
                                        params: event[1] || ''
                                    })
                                })
                                prop["$event_function"] = eventFunc;
                            }
                            postWeData(prop); //在此处上传记录的事件数据
                        }


                    }
                }
            }
        };

    }

    const postWeData = function (data) {
        // console.log("🚀 ~ data 埋点上传器", data)
        /* #ifdef MP-WEIXIN */
        wx.reportEvent("event_click", data)//微信We分析上报事件
        /* #endif */

    }
})()
