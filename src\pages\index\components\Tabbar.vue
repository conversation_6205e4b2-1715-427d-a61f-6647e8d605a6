<template>
    <view class="tabbar" :style="{ bottom: vIphoneXBottomHeight + 40 + 'rpx' }">

        <!-- #ifdef MP-WEIXIN||MP-TOUTIAO -->
        <button open-type="contact" class="serverMenu flexColumnHorzCenter">
            <image class="imgServer" src="@/static/icon/ic_server.png"></image>
            <view class="label">在线客服</view>
        </button>
        <!-- #endif -->

        <!-- #ifdef MP-ALIPAY -->
        <view class="serverMenu flexColumnHorzCenter">
            <contact-button tnt-inst-id="D4u_nXVF" scene="SCE00151022" size="56rpx" color=""
                icon="../../static/icon/ic_server.png" />
            <view class="label">在线客服</view>
        </view>

        <!-- #endif -->
        <!-- #ifndef MP-TOUTIAO -->
        <view class="btnScan flexRowAllCenter" @click="onClickBtnScan">
            <view class="btnScan-box" hover-class="bind_btn">
                <view class="imgScan">
                    <BaseIcon name="scan" :size="30" color="#fff" />
                </view>
                <view class="btnText">扫一扫</view>
            </view>
        </view>
        <!-- #endif -->


        <view class="profileMenu flexColumnHorzCenter" @click="onClickProfileMenu">
            <image class="imgProfile" src="@/static/icon/ic_profile.png"></image>
            <view class="label">个人中心</view>
        </view>

    </view>
</template>
<script>
import BaseIcon from '@/components/base/BaseIcon.vue';
import utils from "@/utils/utils"
export default {
    components: { BaseIcon },
    data() {
        return {

        };
    },

    methods: {
        onClickProfileMenu() {
            uni.navigateTo({ url: '/pagesC/profile/Profile' })
        },
        onClickBtnScan() {
            uni.scanCode({
                onlyFromCamera: true,
                success: (res) => {
                    console.log("条码类型：" + res.scanType);
                    console.log("条码内容：" + res.result);
                    // 条码内容：https://test.51xhkj.com/mini/index?vscode=253364733236916
                    let result = decodeURIComponent(res.result);
                    let dataVsCode = utils.getUrlParams(result, "vscode");
                    let dataMid = utils.getUrlDynamicData(result, "mid");
                    let dataDeviceSn = utils.getUrlDynamicData(result, "device_sn");
                    let freeType =
                        utils.getUrlParams(result, "ft") ||
                        utils.getUrlDynamicData(result, "ft") ||
                        "";
                    if (dataVsCode) {
                        console.log("扫码进入获取设备虚拟码vscode", dataVsCode);
                        uni.navigateTo({
                            url: `/pagesB/product/index?vscode=${dataVsCode}&ft=${freeType}&url=${res.result}`,
                        });
                    } else if (dataMid) {
                        console.log("扫码进入获取设备mid", dataMid);
                        uni.navigateTo({
                            url: `/pagesB/product/index?mid=${dataMid}&ft=${freeType}&url=${res.result}`,
                        });
                    } else if (dataDeviceSn) {
                        console.log("扫码进入获取设备device", dataDeviceSn);
                        uni.navigateTo({
                            url: `/pagesB/product/index?device_sn=${dataDeviceSn}&ft=${freeType}&url=${res.result}`,
                        });
                    } else {
                        uni.showToast({
                            title: "请扫描正确二维码",
                            icon: "error",
                        });
                    }
                },
                fail: (err) => {
                    console.log("🚀 ~ err 调用扫码失败", err)

                }
            });
        },
    },

}
</script>
<style lang="scss">
/*当button里面包含图文时，图文之间会有间距，消除间距的方式是首先对button采用flex竖向布局，然后将子元素的margin和padding设置为0*/
button {
    &:after {
        border: none;
    }

    .button[plain] {
        border: none;
    }

    &:first-child {
        margin-top: 0;
    }

    &:active {
        background-color: white;
    }

    background-color: white;
    border-radius: 0;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
</style>

<style scoped  lang='scss'>
.flexColumnHorzCenter {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.tabbar {
    position: fixed;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20rpx;
    padding: 30rpx 45rpx;
    margin: 0 30rpx;
    box-shadow: 0px 3rpx 7rpx 0px rgba(32, 32, 32, 0.25);
    background-color: #fff;
    box-sizing: border-box;

    .serverMenu {
        border: none;

        .imgServer {
            width: 56rpx;
            height: 56rpx;
            padding: 0 !important;
            margin: 0 !important;
        }

        .label {
            font-size: $font-size-xsmall;
            color: $textBlack;
            margin-top: 14rpx;
            padding: 0 !important;
            line-height: 1;
        }
    }

    .btnScan {
        position: absolute;
        top: 50%;
        left: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 300rpx;
        height: 80rpx;
        border-radius: 40rpx;
        transform: translate(-50%, -50%);
        background: linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2);

        .btnScan-box {
            transition: all 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .btnText {
            margin-left: 16rpx;
            font-size: $font-size-large;
            color: white;
        }

        .bind_btn {
            opacity: 0.4;
            scale: 1.1;
        }
    }

    .profileMenu {
        .imgProfile {
            width: 50rpx;
            height: 56rpx;
        }

        .label {
            font-size: $font-size-xsmall;
            color: $textBlack;
            margin-top: 15rpx;
            line-height: 1;
        }
    }
}
</style>