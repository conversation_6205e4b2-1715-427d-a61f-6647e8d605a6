<template>
  <view class="common" v-if="ad">
    <!-- #ifdef MP-WEIXIN -->

    <!--banner广告位-->
    <ad v-if="type == 'banner'" :unit-id="ad" />
    <!--原生广告位-->
    <ad-custom v-else-if="type == 'custom'" :unit-id="ad" />
    <!--视频广告位-->
    <ad
      v-else-if="type == 'video'"
      :unit-id="ad"
      ad-type="video"
      ad-theme="white"
    ></ad>

    <!-- #endif -->
  </view>
</template>
<script>
export default {
  props: {
    type: { type: String, default: "" },
    ad: { type: String, default: "" },
  },
  data() {
    return {
      interstitialAd: null,
      hasShown: false, // 标记是否已经显示过
      adLoaded: false, // 标记广告是否已加载
    }
  },

  methods: {
    showInterstitialAd() {
      /* #ifdef MP-WEIXIN */
      if (this.interstitialAd && this.type == "inter") {
        console.log("尝试显示插屏广告")
        this.interstitialAd
          .show()
          .then(() => {
            console.log("插屏广告显示成功")
          })
          .catch((err) => {
            console.error("插屏广告显示失败:", err)
            console.error("显示失败详情:", JSON.stringify(err))

            // 如果显示失败，尝试重新加载
            if (err.errCode === 2001) {
              console.log("广告未加载完成，等待加载...")
            } else if (err.errCode === 2002) {
              console.log("广告加载失败，尝试重新加载")
              setTimeout(() => {
                this.interstitialAd.load().then(() => {
                  console.log("重新加载成功，再次尝试显示")
                  this.interstitialAd.show()
                })
              }, 2000)
            } else if (err.errCode === 2003) {
              console.log("广告显示频次限制")
            }
          })
      }
      /* #endif */
    },
    showInterstitialAdWithRetry(retryCount = 0) {
      /* #ifdef MP-WEIXIN */
      if (this.interstitialAd && this.type == "inter") {
        // 检查是否已经显示过
        if (this.hasShown) {
          return
        }

        try {
          const showPromise = this.interstitialAd.show()

          showPromise
            .then(() => {
              this.hasShown = true
            })
            .catch((err) => {
              if (err.errCode === 2003 && retryCount < 3) {
                // 如果是广告冲突，等待5秒后重试
                setTimeout(() => {
                  this.showInterstitialAdWithRetry(retryCount + 1)
                }, 5000)
              } else if (err.errCode === 2002) {
                // 广告加载失败，尝试重新加载
                setTimeout(() => {
                  this.interstitialAd.load().then(() => {
                    this.interstitialAd.show()
                  })
                }, 2000)
              }
            })
        } catch (error) {
          console.error("插屏广告显示异常:", error)
        }
      }
      /* #endif */
    },
  },
  created() {
    /* #ifdef MP-WEIXIN */
    if (wx.createInterstitialAd && this.ad && this.type == "inter") {
      this.interstitialAd = wx.createInterstitialAd({
        adUnitId: this.ad,
      })

      this.interstitialAd.onLoad(() => {
        this.adLoaded = true
      })

      this.interstitialAd.onError((err) => {
        console.error("插屏广告加载失败:", err)
      })

      this.interstitialAd.onClose(() => {
        // 广告关闭回调
      })
    }
    /* #endif */
  },
  mounted() {
    /* #ifdef MP-WEIXIN */
    if (this.interstitialAd && this.type == "inter") {
      // 检查广告是否已加载且未显示
      if (this.adLoaded && !this.hasShown) {
        this.showInterstitialAdWithRetry()
      } else if (!this.adLoaded) {
        // 等待广告加载完成
        const checkLoaded = () => {
          if (this.adLoaded && !this.hasShown) {
            this.showInterstitialAdWithRetry()
          } else if (!this.adLoaded) {
            setTimeout(checkLoaded, 100)
          }
        }
        setTimeout(checkLoaded, 100)
      }
    }
    /* #endif */
  },
}
</script>

<style scoped lang="scss">
.common {
  padding: 30rpx 0;
}
</style>
