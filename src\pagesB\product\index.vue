<template>
  <view>
    <!-- #ifndef MP-ALIPAY -->
    <!-- <BaseNavbar :isShowSlot="true" bgColor="rgba(255, 255, 255,0)" :placeholder="false" @leftClick="onClickHome">
      <u-icon :name="isMorePage ? 'arrow-left' : 'home'" size="30" color="#fff" />
    </BaseNavbar> -->

    <!-- #endif -->
    <!-- #ifndef H5 -->
    <BaseNavbar
      :isShowSlot="true"
      :title="brandInfo.brandName"
      @leftClick="onClickHome"
    >
      <view class="nav-left">
        <view class="nav-left-title">{{ pointPlaceInfo.hotelName }}</view>
      </view>
    </BaseNavbar>
    <!-- #endif -->
    <view class="swiper">
      <swiper
        class="swiper_box"
        :interval="5000"
        autoplay
        :duration="1000"
        :circular="true"
      >
        <!-- #ifdef MP-WEIXIN  -->
        <swiper-item class="swiper-item" v-if="vAd.swiperBannerAd">
          <!-- 插入广告组件 -->
          <CommonAd :ad="vAd.swiperBannerAd || ''" type="custom" />
        </swiper-item>
        <!-- 新增广告位 - 广告位ID: adunit-bae5eb264a5f4dc2 -->
        <swiper-item class="swiper-item">
          <!-- 插入新的广告组件 -->
          <CommonAd :ad="vAd.newSwiperAd || ''" type="custom" />
        </swiper-item>
        <!--  #endif -->
        <!-- 广告轮播项 -->
        <swiper-item v-for="(item, index) in adInfoList" :key="index">
          <!-- 插入其他内容 -->
          <!-- 这里可以放置您的其他内容，如图片、文字等 -->
          <image :src="item.url" class="image"></image>
        </swiper-item>
      </swiper>
    </view>
    <view class="place-info">
      <view class="place-info-item">
        <u-icon name="bookmark" size="26" />
        <view class="label">{{ pointPlaceInfo.device_sn || "暂无设备" }}</view>
      </view>

      <view class="place-info-item">
        <u-icon name="map" size="26" />
        <view class="label">
          {{ pointPlaceInfo.addressDetail || "暂无具体地址" }}</view
        >
      </view>
      <view class="place-info-item" v-if="vIsBleDevice" @click="bleConnect()">
        <image
          class="icon bleIcon"
          style="width: 22px; height: 22px"
          :src="
            isConnected
              ? '../../static/icon/ic_ble_connected.png'
              : '../../static/icon/ic_ble_not_connected.png'
          "
        ></image>
        <view class="label" :style="!isConnected ? 'color:#666' : ''">
          {{ isConnected ? "蓝牙已连接" : "点击链接" }}
        </view>
      </view>
    </view>
    <MoreMenu
      v-if="pointPlaceInfo.device_sn"
      :deviceSn="deviceInfo.device_sn"
      :miniFoodInfo="miniFoodInfo"
    />
    <view class="goods-area">
      <view class="goods-item" v-for="item in goodList" :key="item.goodid">
        <view class="goods-info">
          <image class="goods-info-img" :src="item.goods_img" />
          <view class="goods-info-main">
            <view class="coloum">
              <view class="goods-info-mani-top">
                <view class="goods-info-main-title">{{ item.goods_name }}</view>
                <!-- <view class="goods-info-main-price">
                            <view>售价:￥{{ item.good_price }}元</view>
                            <view>库存:{{ item.amount }}</view>
                        </view> -->
                <view class="goods-info-main-price">
                  <view>库存：{{ item.amount }}</view>
                </view>
                <view class="goods-info-main-price">
                  <view>已售：{{ goodsInfo.order_total }}</view>
                </view>
              </view>
              <view class="goods-info-mani-but">
                <view class="goods-but-title">请选择购买包数</view>
                <!-- <view class="goods-info-main-price">
                            <view>售价:￥{{ item.good_price }}元</view>
                            <view>库存:{{ item.amount }}</view>
                        </view> -->
                <view class="goods-input">
                  {{ number }}
                  <!-- <u-number-box :min="1" :max="3" minus="1" v-model="number" button-size="40"></u-number-box> -->
                </view>
              </view>
            </view>

            <!-- <view class="goods-info-main-desc">{{ item.goods_remark }}</view> -->
            <view class="goods-info-main-img">
              <view
                class="goods-info-main-img-box"
                v-for="items in item.images"
                :key="items"
              >
                <image :src="items" />
              </view>
            </view>
          </view>
        </view>
        <!-- <view class="meal-area">
          <RechargeMeal @selectRechargeItem="onSelectRechargeItem" :isShowKey.sync="isShowKey" />
        </view> -->
        <view class="goods-btn">
          <view
            class="free"
            v-if="resInfo.isFreeBuy != false"
            @click="onClickVideoAd(item)"
          >
            <image src="@/pagesB/static/goods/free_get.png" />
            <view class="free-text">观看完整视频，免费领取一片</view>
          </view>

          <!-- <view>
            <BaseButton text="看视频免费领取" shape="circle" color="linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2)"
              @onClick="onClickVideoAd(item)" />
          </view> -->
          <view class="buy_box">
            <view
              class="bubble"
              v-if="vMemberInfo.is_have_special_price && item.second_price > 0"
              >{{ updataTime }}后到期</view
            >
            <BaseButton
              class="buy"
              :text="`立即购买,${
                vMemberInfo.is_have_special_price && item.second_price > 0
                  ? '第二包特惠'
                  : ''
              } (${
                vMemberInfo.is_have_special_price && item.second_price > 0
                  ? (item.second_price * number).toFixed(2)
                  : (item.good_price * number).toFixed(2)
              })元`"
              color="linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2)"
              height="82rpx"
              @onClick="onClickBtnBuyNow(item)"
            />
          </view>
          <!-- <view>
                        <BaseButton :text="`${item.exchange_integral || 0}积分兑换`" :plain="true" shape="circle"
                            @onClick="onClickBtnPointExchange(item)" />
                    </view> -->
        </view>
        <view class="article">
          <ServiceProtocolAgree
            @agreeChange="(val) => (isAgreeProtocol = val)"
          />
        </view>
      </view>
    </view>
    <!-- <view class="shop-box">
      <view class="goods-shop">
        <view class="goods-title">
          <view class="goods-h1">好物商城</view>
          <view class="goods-text">更多 》</view>
        </view>
        <view class="goods-item" v-for="item in 10" :key="item">

        </view>
      </view>

    </view> -->
    <!-- <view class="logo-box">
      <image class="logo-img" src="../static/login-ad.png" />
    </view> -->
    <CommonAd :ad="vAd.buyProductBannerAd" type="banner" />
    <CommonAd :ad="vAd.homeInsertScreenAd" type="inter" />
    <!-- 进入页面就显示的插屏广告 -->
    <!-- <CommonAd :ad="vAd.orderDetailInsertScreenAd || ''" type="inter" /> -->
    <view class="local-video" v-if="isShowLocalVideo">
      <video
        class="video"
        :src="local_video_url"
        play-btn-position="center"
        show-fullscreen-btn="false"
        show-center-play-btn="true"
        autoplay="false"
        @ended="endCocalVideo"
        @error="onVideoError"
        @play="onVideoPlay"
        @pause="onVideoPause"
      />
      <view class="close-video" @click="adAlterClose">关闭</view>
    </view>
    <BasePopup :show.sync="isShowPopupAdAlter" mode="center" :safeArea="false">
      <view class="ad_alter" @click="handleAdInfo(ad_index_alter)">
        <image class="img" :src="ad_index_alter.img_url" />
        <view class="close_img" @click.stop="closeAdAlter(ad_index_alter)">
          <BaseIcon name="close" />
        </view>
      </view>
    </BasePopup>
    <!-- <view class="shop-box">
      <view class="goods-shop">
        <view class="goods-title">
          <view class="goods-h1">好物商城</view>
          <button v-if="isPhones" open-type="getPhoneNumber" class="goods-text"
            @getphonenumber="getrealtimephonenumber">
            更多 》
          </button>
          <view v-else class="goods-text" @click="other()">更多 》</view>
        </view>
        <view class="shopList-box" v-if="isPhones">
          <button open-type="getPhoneNumber" v-for="(items, i) in shopList" :key="i"
            class="shop-item shop-phone-btn" @getphonenumber="getrealtimephonenumber">
            <ShopListCar :info="items" @showShoping="other" @goDateil="other"></ShopListCar>
          </button>
        </view>
        <view class="shopList-box" v-else>
          <ShopListCar class="shop-item" v-for="(items, i) in shopList" :info="items" :key="i" @showShoping="other"
            @goDateil="other"></ShopListCar>
        </view>


      </view>

    </view>
    <view class="shop-code" v-if="isShopCode" @click="isShopCode = false">
      <view class="shop-code-imge" @click.stop="goUrl()">
        <image :show-menu-by-longpress="true" class="image" src="../static/shop/code.jpg" />
      </view>
      <view class="shop-code-text">长按图片保存到本地</view>
    </view> -->
    <tabbar />
    <SafeBlock height="120" />

    <LoginPopup @bindDeviceSn="bindDeviceSn" />
  </view>
</template>
<script>
import {
  bindFirstDevice,
  createFreeOrderAndOut,
  createOrder,
  getDeviceInfoByMid,
  getUserInfo,
  getDeviceAllInfoByVscode,
  getDeviceAllInfo,
  getShopdAndAd,
  setAdLog,
  getRemainCountMini,
  createPointOrder,
  updateUMEle,
  getPhoneNumber,
  updateUserPhone,
} from "@/common/http/api"
import { locationMixin } from "@/mixins/locationMixin"
import BaseNavbar from "../../components/base/BaseNavbar.vue"
import MoreMenu from "./components/MoreMenu.vue"
import BaseButton from "../../components/base/BaseButton.vue"
import utils from "@/utils/utils"
import SafeBlock from "../../components/list/SafeBlock.vue"
import BasePopup from "../../components/base/BasePopup.vue"
import BaseIcon from "../../components/base/BaseIcon.vue"
import CommonAd from "../../components/WxAd/CommonAd.vue"
import { adMixin } from "@/mixins/adMixin"
import LoginPopup from "../../components/LoginPopup.vue"
import Tabbar from "./components/Tabbar.vue"
import ServiceProtocolAgree from "./components/ServiceProtocolAgree.vue"
import {
  globalCodes,
  isMaskMachine,
  isBagMachine,
  isRechargeMachine,
  isWirelessMachine,
  isWet_Tissu,
  isBleDevice,
} from "@/global/globalCodes"
import { bleMixin } from "@/mixins/bleMixin"
import { globalEvents } from "@/global/globalEvents"
import ble from "@/wxutil/newBle"
import { globalKeys } from "@/global/globalKeys"
import { getWxPayInfo, getOrderPayStatus } from "@/common/http/api"
// import { payOrder } from "@/utils/pay"
import RechargeMeal from "./components/RechargeMeal.vue"
import ShopListCar from "../components/ShopListCar.vue"

const app = getApp()
export default {
  name: "index",
  components: {
    BaseNavbar,
    MoreMenu,
    BaseButton,
    SafeBlock,
    BasePopup,
    BaseIcon,
    CommonAd,
    LoginPopup,
    Tabbar,
    ServiceProtocolAgree,
    RechargeMeal,
    ShopListCar,
  },
  mixins: [locationMixin, adMixin, bleMixin],
  data() {
    return {
      //最后一次点击按钮的时间
      lastOrderTime: 0,
      //是否同意用户协议
      bAgreeProtocol: true,
      //加载框显示的图片和文字
      subOrderTimer: null, //这里类似防抖伐,未连接蓝牙时,免费兑换会创建多次订单,
      isVsCode: false,
      isMid: false,
      isScene: false,
      dataDeviceSn: "",
      dataVsCode: "",
      dataMid: "",
      freeType: "", //显示领取按钮类
      adInfoList: [], //轮播广告
      isMorePage: false, //是否是多页面
      pointPlaceInfo: {}, //点位信息
      deviceInfo: {}, //设备信息
      resInfo: {}, //设备所有信息
      brandInfo: {}, //程序信息

      goodList: [], //商品套餐
      isShowPopupAdAlter: false, //显示自定义插屏广告
      local_video: {}, //本地视频信息
      local_video_url: "", //本地视频地址
      ad_index_alter: {}, //自定义弹窗广告
      isShowLocalVideo: false, //显示本地视频
      selectGoodsInfo: {}, //选择的商品套餐
      goodsInfo: {}, //商品信息
      isAgreeProtocol: true, //同意协议
      //蓝牙连接成功后要进行的操作
      //0-不做任何操作（当页面进入时主动连接蓝牙）
      //1-创建免费订单（点击了免费试用5分钟按钮）
      //2-创建普通订单（点击了立即购买按钮）
      //3-创建积分订单（点击了积分兑换按钮）
      enumOptTypeAfterBleConnected: {
        NONE: 0,
        CREATE_FREE_ORDER: 1,
        CREATE_COMMON_ORDER: 2,
        CREATE_POINT_ORDER: 3,
      },

      optTypeAfterBleConnected: 0,
      //蓝牙是否连接
      bBleConnected: false,
      //设备类型
      deviceType: -1,
      canUrl: null,
      curSelMeal: {}, //选择的套餐
      number: 1,
      isPay: false,
      mtu: "",
      shopList: [
        {
          price: 94,
          title:
            "【国庆秒杀】卫生湿巾10包 便携10片装 杀菌＞99% 出差旅游 厕用擦手",
          asle: 100000,
          src: require("../static/shop/shop1.jpg"),
        },
        {
          price: 4.9,
          title: "【马应龙护理】彩虹湿厕纸 99%杀菌 可冲马桶 实力护菊",
          asle: 2334,
          src: require("../static/shop/shop2.jpg"),
        },
      ],
      isShowShoping: false,
      isPhone: false,
      isShopCode: false,
      is_have_special_price: false,
      updataTime: "00:00:00",
      showUpTime: null,
    }
  },
  computed: {
    isPhones() {
      // console.log('phone', this.vMemberInfo?.phone)
      return !this.vMemberInfo?.phone
    },
    isConnected() {
      return this.bBleConnected
    },
    swiperLoading() {
      return (this.adInfoList?.length ?? 0) === 0
    },
  },
  methods: {
    updateCountdown() {
      // 获取当前时间
      const now = new Date()

      // 计算今天晚上12点（次日0点）的时间
      const midnight = new Date(now)
      midnight.setHours(0, 0, 0, 0)
      midnight.setDate(midnight.getDate() + 1) // 确保是次日的0点

      // 计算时间差（毫秒）
      const timeDifference = midnight - now

      // 转换为秒、分钟、小时
      const seconds = String(
        Math.floor((timeDifference % (1000 * 60)) / 1000)
      ).padStart(2, "0")
      const minutes = String(
        Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60))
      ).padStart(2, "0")
      const hours = String(
        Math.floor((timeDifference / (1000 * 60 * 60)) % 24)
      ).padStart(2, "0")

      // 更新页面上的倒计时
      this.updataTime = `${hours}:${minutes}:${seconds}` // 假设属性名是 updateTime 而不是 updataTime
      // console.log('updateTime', this.updataTime)
      // 每秒更新一次倒计时
      if (timeDifference > 0 && this.vMemberInfo.is_have_special_price) {
        this.showUpTimer = setTimeout(() => this.updateCountdown(), 1000) // 使用箭头函数保持 this 上下文
      } else {
        // 可选：倒计时结束后执行的操作
        this.getinfo()
        // console.log('倒计时结束');
        // 清除可能的定时器（虽然在这个例子中，由于 setTimeout 在 timeDifference <= 0 时不会调用，所以这一步是多余的）
        // 但如果是在其他情况下设置的定时器，则应该在这里清除它
      }
    },
    // goUrl() {

    // },
    //获取电话

    async getrealtimephonenumber(event) {
      console.log("电话", event, this.vMemberInfo.phone)
      try {
        if (event.detail.code) {
          let data = {
            code: event.detail.code,
          }
          getPhoneNumber(data).then((res) => {
            console.log("res", res)
            let parame = {
              phone: res,
            }

            updateUserPhone(parame)
              .then((res) => {
                console.log("res", res)
                let obj = { ...this.vMemberInfo, phone: res }
                this.$u.vuex("vMemberInfo", obj)
                // this.$u.vuex("vMemberInf", ...this.vMemberInfo,{phone:'12348'});
              })
              .catch((err) => {
                console.log("err", err)
              })
          })
        }
      } catch (err) {
        console.log("err", err)
      }
    },
    //跳转到商城
    other() {
      this.isShopCode = true
      console.log("拉起弹窗")
      this.isShopCode = true
      // uni.navigateTo({
      //   url: `/pagesB/shop/shoping`,
      // });
    },
    //触发绑定
    bindDeviceSn() {
      if (this.deviceInfo.device_sn) {
        let data = {
          device_sn: this.deviceInfo.device_sn,
        }
        bindFirstDevice(data)
          .then((res) => {
            console.log("绑定成功", res)
          })
          .catch((err) => {
            console.log("绑定失败", err)
          })
      }
    },
    onSelectRechargeItem(item) {
      this.number = item.value
      console.log("出片数", this.number, item)
    },
    //截取域名
    getDomainFromUrl(url) {
      // 使用正则表达式来匹配URL中的域名
      let domainMatch = url.match(/https?:\/\/[^\/]+/)
      return domainMatch ? domainMatch[0] : null
    },

    bleConnect() {
      if (!this.bBleConnected) {
        this.startBleConnect()
      }
    },
    //是否是蓝牙设备
    isBle(type) {
      return isBleDevice(type)
    },
    //本地视频广告播放结束
    endCocalVideo() {
      console.log("本地视频广告播放完成...")
      this.isShowLocalVideo = false
      this.setAdLogHandle(this.local_video, 80, 2)
      this.doCreateFreeOrder(this.selectGoodsInfo, true)
    },
    //视频播放错误处理
    onVideoError(e) {
      console.log("视频播放错误:", e)
      // 不显示错误提示，静默处理
    },
    //视频开始播放
    onVideoPlay(e) {
      console.log("视频开始播放:", e)
    },
    //视频暂停
    onVideoPause(e) {
      console.log("视频暂停:", e)
    },
    //关闭本地视频广告
    adAlterClose() {
      uni.showModal({
        title: "提示",
        content: "暂未获得奖励 是否继续观看视频",
        showCancel: true,
        cancelText: "放弃",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
          } else if (res.cancel) {
            this.isShowLocalVideo = false
          }
        },
      })
    },
    //关闭自定义弹窗广告
    closeAdAlter(item) {
      if (Math.random() < 0.7) this.handleAdInfo(item)
      this.isShowPopupAdAlter = false
    },
    handleAdInfo(item) {
      if (item.file_type == 2) return //视频广告不跳转
      //小程序跳转广告
      if (item.wx_mp_appid && item.wx_mp_path)
        return wx.navigateToMiniProgram({
          appId: item.wx_mp_appid,
          path: item.wx_mp_path,
          success: (result) => {
            console.log("🚀 ~ result", result)
          },
        })
      //链接跳转
      let linkHref = item.img_href
      console.log("🚀 ~ linkHref", linkHref)

      if (!linkHref || linkHref == "#") return

      linkHref && this.goToWebView(linkHref)
    },
    onAgreeChange(agree) {
      this.bAgreeProtocol = agree
      console.log("是否同意服务协议：", this.bAgreeProtocol)
    },
    //激励视频播放完成回调
    onRewardAdEndCallbck() {
      console.log("激励视频广告播放完成...")
      this.$u.vuex("vPayStart", false)
      this.doCreateFreeOrder(this.selectGoodsInfo, true)
    },
    //2s内不能重复下单
    checkRepeatOrder() {
      if (!this.bAgreeProtocol) {
        this.isShowErr("请先同意《服务协议》")
        return false
      }
      let timestamp = new Date().valueOf()
      if (timestamp - this.lastOrderTime < 2000) {
        this.isShowErr("您操作太频繁了")
        this.lastOrderTime = timestamp
        return false
      }
      console.log("🚀 ~ this.vCurLocation", this.vCurLocation)
      if (this.deviceType == globalCodes.machineType.WET_TISSU) {
        return true
      }
      let distances = this.distanceLength(
        this.vCurLocation.latitude,
        this.vCurLocation.longitude,
        this.pointPlaceInfo.lat,
        this.pointPlaceInfo.lon
      )

      console.log(
        "🚀 ~ distances 与设备之间的距离",
        parseFloat(distances),
        "公里"
      )
      console.log(
        "🚀 ~ this.resInfo.max_distance  最大距离（米）",
        parseFloat(this.resInfo.max_distance)
      )
      if (
        parseFloat(distances * 1000) >
        parseFloat(this.resInfo.max_distance || 500)
      ) {
        uni.showModal({
          title: "提示",
          content: "您距离设备太远了，请在设备附近操作~",
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
            } else if (res.cancel) {
              console.log("用户点击取消")
            }
          },
        })
        return false
      }
      this.lastOrderTime = timestamp
      return true
    },
    //立即购买
    onClickBtnBuyNow(item) {
      if (!this.bAgreeProtocol) {
        uni.showToast({
          title: "请先同意《服务协议》",
          icon: "none",
        })
        return
      }
      //判断设备类型
      let type = this.deviceType
      this.deviceInfo.type = type
      console.log("当前type", type, item)
      if (
        type == globalCodes.machineType.BAG ||
        type == globalCodes.machineType.MASK
      ) {
        //取袋机
        //创建订单
        let params = {
          device_sn: item.device_sn,
          channel: item.num || "",
          goods_id: item.goodid || "",
          num: 1,
        }
        createOrder(params).then((res) => {
          this.handlePayOrder(res, item, 1)
        })
      } else if (type == globalCodes.machineType.WET_TISSU) {
        if (!this.bBleConnected) {
          this.optTypeAfterBleConnected = 2
          return this.startBleConnect()
        } else {
          // return console.log('mtu',getApp().globalData.mtu)
          if (getApp().globalData.mtu < 23) {
            uni.showToast({
              title: "请升级蓝牙4.0以上",
              icon: "none",
            })
            return
          }
          // this.onOpen()
          //湿巾机订单
          let params = {
            device_sn: item.device_sn,
            channel: item.num || "",
            goods_id: item.goodid || "",
            num: this.number,
            isRedirectOut: true,
            price_type:
              this.vMemberInfo.is_have_special_price && item.second_price > 0
                ? 1
                : 0,
          }
          console.log("params", params)
          createOrder(params).then((res) => {
            let orderInfo = res.data
            console.log("创建订单：", res)
            //将orderInfo,pointPlaceInfo,curSelMeal三个对象存入当前订单信息中
            getApp().globalData[globalKeys.KEY_CUR_ORDER_INFO] = {
              orderInfo,
              pointPlaceInfo: this.pointPlaceInfo,
              curSelMeal: item,
            }
            this.deviceInfo.type = this.deviceInfo.deviceType
            console.log(
              "this.deviceInfo.deviceType",
              this.deviceInfo.deviceType
            )
            this.deviceInfo.hotelName = this.pointPlaceInfo.hotelName
            // 设备信息
            getApp().globalData[globalKeys.CUR_SELECT_MACHINE_INFO] =
              this.deviceInfo
            //跳转到支付订单页面
            console.log("跳转")
            this.handlePayOrder(res, item, globalCodes.machineType.WET_TISSU)
          })
        }
      }
    },

    //点击了免费领取按钮
    onClickFreeGet(item) {
      if (!this.checkRepeatOrder()) return
      console.log("onClickFreeGet 协议：", this.bAgreeProtocol)
      if (!this.bAgreeProtocol) {
        uni.showToast({
          title: "请先同意《服务协议》",
          icon: "none",
        })
        return
      }

      let type = this.deviceType
      console.log("设备类型", type)
      if (type == globalCodes.machineType.WET_TISSU) {
        if (!this.bBleConnected) {
          this.optTypeAfterBleConnected = 2
          return this.startBleConnect()
        } else {
          this.doCreateFreeOrder(item)
        }
        // this.doCreateFreeOrder(item);
      } else {
        this.doCreateFreeOrder(item)
      }
    },
    //看视频免费领取
    onClickVideoAd(item) {
      this.selectGoodsInfo = item
      if (!this.checkRepeatOrder()) return
      if (!this.bAgreeProtocol) {
        uni.showToast({
          title: "请先同意《服务协议》",
          icon: "none",
        })
        return
      }
      let type = this.deviceType
      if (type == globalCodes.machineType.WET_TISSU) {
        if (!this.bBleConnected) {
          this.optTypeAfterBleConnected = 2
          return this.startBleConnect()
        } else {
          getRemainCountMini({
            isVideo: true,
          })
            .then((res) => {
              if (res) {
                // if (this.local_video_url) {
                //     this.setAdLogHandle(this.local_video, 80, 1)
                //     return this.isShowLocalVideo = true;
                // }
                this.$u.vuex("vPayStart", true)
                this.showRewardAd()
              } else {
                uni.showModal({
                  title: "提示",
                  content: "亲,暂时没有视频资源,请直接购买啦",
                  showCancel: false,
                })
              }
            })
            .catch((err) => {
              uni.showModal({
                title: "提示",
                content: "亲,暂时没有视频资源,请直接购买啦",
                showCancel: false,
              })
            })
        }
        // this.doCreateFreeOrder(item);
      }
    },
    //点击了积分兑换
    onClickBtnPointExchange(item) {
      if (!this.checkRepeatOrder()) return
      let params = {
        device_sn: this.deviceInfo.device_sn,
        channel: item.num,
        goods_id: item.goodid,
        num: 1,
      }
      createPointOrder(params).then((res) => {
        this.handlePayOrder(res, item, 2)
      })
    },
    //处理跳转到确认订单页面
    handlePayOrder(orderInfo, item, getWay) {
      uni.$u.vuex("vCreateOrderInfo", {
        orderInfo,
        pointPlaceInfo: this.pointPlaceInfo,
        curSelMeal: item,
      })
      this.$u.vuex("vPayStart", true)
      this.isPay = false
      console.log("订单信息", orderInfo, item, getWay)
      this.shop(orderInfo?.order_sn, item?.device_sn, orderInfo?.num)
      //跳转到支付订单页面
      // uni.navigateTo({
      //   url: `/pagesB/product/ConfirmOrder?getWay=${getWay}`,
      // });
    },
    async shop(order_sn, device_sn, num = 1) {
      let that = this

      //取袋机支付
      let params = {
        order_sn: order_sn,
        device_sn: device_sn,
      }

      // console.log('params-vPayStart', params, this.vPayStart)
      //更改状态
      const res = await getWxPayInfo(params)

      let payInfo = res.data
      // console.log('this.payOrderInfo.orderInfo', this.payOrderInfo.orderInfo)
      //请求微信支付
      /* #ifdef MP-ALIPAY */
      if (!payInfo.trade_no) {
        return that.isShowErr("支付错误,请重新扫码")
      }
      /* #endif */
      uni.requestPayment({
        /* #ifdef MP-WEIXIN */
        provider: "wxpay",
        timeStamp: payInfo.timeStamp,
        nonceStr: payInfo.nonceStr,
        package: payInfo.package,
        signType: payInfo.signType,
        paySign: payInfo.paySign,
        /* #endif */
        /* #ifdef MP-ALIPAY */
        provider: "alipay",
        orderInfo: payInfo.trade_no,
        /* #endif */
        /* #ifdef MP-TOUTIAO */
        orderInfo: payInfo,
        service: 5, //固定值，拉起小程序收银台
        /* #endif */
        success(res) {
          console.log("取袋机支付成功", res, that.vPayStart)
          /* #ifdef MP-ALIPAY */
          if (res.resultCode != 9000) {
            that.$u.vuex("vPayStart", false)
            clearInterval(time)
            uni.showToast({
              title: "支付失败",
              icon: "none",
            })
            // return uni.navigateBack();
            return
          }

          /* #endif */
          //跳转到订单详情页面前，先关闭当前页面的插屏广告
          console.log("准备跳转到订单详情页面，先关闭插屏广告")
          // 延迟跳转，确保插屏广告有时间关闭
          setTimeout(() => {
            uni.navigateTo({
              url: `/pagesB/order/OrderDetails?order_sn=${order_sn}`,
            })
          }, 1000)
          // console.log('订单号', order_sn)
          if (that.bBleConnected) {
            if (!that.isPay) {
              that.isPay = true
              clearInterval(time)
              if (isWet_Tissu(that.deviceType)) {
                // console.log('调用几次1')
                that.onOpen(num, that.pointPlaceInfo.deviceType)
              } else {
              }
            }
          } else {
            that.startBleConnect()
          }
        },
        fail(res) {
          clearInterval(time)
          console.log("支付失败", res)
          that.$u.vuex("vPayStart", false)
          uni.showToast({
            title: "支付失败",
            icon: "none",
          })
          // return uni.navigateBack();
        },
        complete(res) {
          clearInterval(time)
          console.log("支付complete", res)
          //clearInterval(that.sti);
        },
      })

      const time = setInterval(() => {
        console.log("支付", that.isPay)
        if (!that.isPay) {
          console.log("轮询查询")
          getOrderPayStatus(params)
            .then((res) => {
              console.log("获取用户信息成功", res)
              if (res.pay_status == 1) {
                if (!that.isPay) {
                  that.isPay = true
                  clearInterval(time)
                  if (isWet_Tissu(that.deviceType)) {
                    console.log("调用几次1")
                    that.onOpen(num, that.pointPlaceInfo.deviceType)
                  } else {
                  }
                }
              }
            })
            .catch((err) => {
              console.log("获取用户信息失败", err)
              that.isShowErr("获取用户信息失败")
            })
        }
      }, 1000)
    },

    //API 创建免费订单
    doCreateFreeOrder(item, isVideo = false) {
      console.log("这里开始创建免费订单")
      let params = {
        device_sn: this.deviceInfo.device_sn,
        channel: item.num,
        goods_id: item.goodid,
        num: 1,
        isVideo,
      }
      if (this.isBle(this.deviceType)) {
        params["isRedirectOut"] = false
      }
      createFreeOrderAndOut(params).then((res) => {
        //跳转到订单详情页面
        if (this.deviceType == globalCodes.machineType.WET_TISSU) {
          uni.$u.vuex("vCreateOrderInfo", {
            orderInfo: res,
          })
          this.onOpen(res.num, this.pointPlaceInfo.deviceType)
        }
      })
    },
    getinfo() {
      getUserInfo().then((res) => {
        // this.userInfo = res;
        uni.$u.vuex("vMemberInfo", res)
      })
    },

    onClickHome() {
      if (this.isMorePage) {
        uni.navigateBack()
      } else {
        uni.redirectTo({
          url: "/pages/index/index",
        })
      }
    },
    // //API 根据虚拟码获取设备信息
    doGetDeviceAllInfoByVscode(device_code) {
      let params = {
        device_code,
      }
      getDeviceAllInfoByVscode(params)
        .then((res) => {
          console.log(res)
          this.handleRes(res)
        })
        .catch((err) => {
          console.log("err", err)
        })
    },
    // //API 根据mid获取设备信息
    doGetDeviceInfoByMid(mid, device_sn) {
      let params = {
        mid,
        device_sn,
      }
      //getDeviceInfoByMid
      getDeviceInfoByMid(params).then((res) => {
        this.handleRes(res)
      })
    },
    // //API 根据设备编号获取设备信息
    doGetDeviceAllInfo(device_sn) {
      let params = {
        device_sn,
      }
      getDeviceAllInfo(params).then((res) => {
        this.handleRes(res)
      })
    },
    //设备编号存入全局变量
    setDeviceSn(type) {
      //重新扫码是否是上次的设备
      let isDeviceAgreement =
        app.globalData.device_sn &&
        app.globalData.device_sn !== this.pointPlaceInfo.device_sn
      // app.globalData.mkey !== this.pointPlaceInfo.mkey;

      getApp().globalData.device_sn = this.pointPlaceInfo.device_sn
      getApp().globalData.mkey = this.pointPlaceInfo.mkey

      if (
        isRechargeMachine(type) ||
        isWirelessMachine(type) ||
        isWet_Tissu(type)
      ) {
        this.bBleConnected = getApp().globalData.connected
        //设置需要连接的蓝牙设备的设备编号
        getApp().globalData.device_type = this.pointPlaceInfo.deviceType
        ble.initBleData({
          device_sn: this.pointPlaceInfo.device_sn, //设备编号 也是搜索name
          device_type: this.pointPlaceInfo.deviceType, //设备类型
          mkey: this.pointPlaceInfo.mkey, //设备类型
        })
        if (getApp().globalData.connected == false || isDeviceAgreement) {
          console.log("🚀 ~ startBleConnect  开始连接蓝牙 ")
          //这里暂时不主动连接蓝牙，因为有网络请求，可能会影响连接状态
          if (getApp().globalData.connected) {
            ble.closeBle()
            setTimeout(() => {
              this.startBleConnect()
            }, 500)
          } else {
            this.startBleConnect()
          }
        }
      }
      console.log(
        "🚀 ~ 设备号",
        type,
        "isWireless:",
        isWirelessMachine(type),
        "isWetTissu:",
        isWet_Tissu(type),
        "isRecharge:",
        isRechargeMachine(type)
      )
    },

    //统一处理返回数据
    handleRes(res) {
      if (res.code == 0) {
        return uni.navigateBack()
      }
      this.resInfo = res
      this.deviceInfo = res.device_info
      this.brandInfo = res.brand_info
      console.log("brandInfo", this.brandInfo)
      //获取点位信息
      this.pointPlaceInfo = { ...res.hotel_info, ...res.device_info }
      uni.$u.vuex("vServicePhone", res.brand_info.phone)
      // uni.$u.vuex('vAppName', res.brand_info.brandName)
      uni.$u.vuex("vDeviceSn", res.device_info.device_sn)
      uni.$u.vuex("vDeviceType", res.device_info.deviceType)
      uni.$u.vuex("vPointInfo", this.pointPlaceInfo)
      getApp().globalData.wifiInfo = {
        wifi_name: res.data?.hotel_info?.wifi_name,
        wifi_psd: res.data?.hotel_info?.wifi_psd,
      }
      let type = this.deviceInfo.deviceType
      console.log("🔍 测试设备类型判断:", type)
      console.log("🔍 isWet_Tissu结果:", isWet_Tissu(type))
      console.log("🔍 WET_TISSU数组:", globalCodes.machineCode.WET_TISSU)
      //如果没有定位直接获取定位
      if (!this.vCurLocation?.longitude || !this.vCurLocation?.latitude) {
        this.getLocPermission() //调用获取位置信息
      }
      if (isBagMachine(type)) this.deviceType = globalCodes.machineType.BAG
      else if (isMaskMachine(type))
        this.deviceType = globalCodes.machineType.MASK
      else if (isRechargeMachine(type))
        this.deviceType = globalCodes.machineType.RECHARGE_LINE
      else if (isWirelessMachine(type))
        this.deviceType = globalCodes.machineType.WIRELESS_CHARGING
      else if (isWet_Tissu(type))
        this.deviceType = globalCodes.machineType.WET_TISSU
      let isBleSn = this.isBle(this.deviceType)
      uni.$u.vuex("vIsBleDevice", isBleSn)
      console.log("type", this.deviceType)
      this.handleAd() //获取广告信息
      this.setDeviceSn(this.deviceInfo.deviceType) //让信息存入全局变量
    },
    //处理广告信息
    handleAd() {
      let params = {
        device_sn: this.pointPlaceInfo.device_sn,
      }
      getShopdAndAd(params).then((res) => {
        this.adInfoList = res?.ads || []
        this.goodList = res.goods || []
        this.adInfoList = this.adInfoList?.map((el) => {
          el.url = el.img_url
          return el
        })
        this.goodsInfo = res

        this.local_video = res?.ad_city_video
        this.local_video_url = res?.ad_city_video?.img_url //本地视频地址
        console.log("🚀 ~ this.local_video_url", this.local_video_url)
        this.ad_index_alter = res?.ad_index_alter //自定义弹窗广告
        if (this.ad_index_alter?.img_url) this.isShowPopupAdAlter = true

        let ad_city_spread = res?.ad_city_spread //置剪辑板广告
        if (ad_city_spread?.content_text) {
          uni.setClipboardData({
            data: ad_city_spread?.content_text,
            success: (res) => {
              this.setAdLogHandle(ad_city_spread, 90, 1)
              console.log("🚀 ~ 置剪辑板成功", res)
            },
            fail: (err) => {
              console.log("🚀 ~ 置剪辑板失败", err)
            },
          })
        }
      })
    },
    //添加广告展示记录
    setAdLogHandle(item, ad_type, active) {
      let data = {
        device_sn: this.deviceInfo.device_sn,
        ad_type, //80=》本地视频，90=》同城推广
        active,
        ad_id: item.aid,
      }
      setAdLog(data).then((res) => {
        console.log("🚀 ~ 广告统计 res", res)
      })
    },
    // 计算两点的距离
    distanceLength(la1, lo1, la2, lo2) {
      let La1 = (la1 * Math.PI) / 180.0
      let La2 = (la2 * Math.PI) / 180.0
      let La3 = La1 - La2
      let Lb3 = (lo1 * Math.PI) / 180.0 - (lo2 * Math.PI) / 180.0
      let s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(La3 / 2), 2) +
              Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)
          )
        )
      s = s * 6378.137
      s = Math.round(s * 10000) / 10000
      s = s.toFixed(2)
      return s
    },
    getLocPermission() {
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {})
      })
      //#endif
      // #ifdef MP-ALIPAY
      this.getCurrentLocation(() => {})
      //#endif
    },
    clickSwiper(i) {
      this.handleAdInfo(this.adInfoList[i])
    },
  },
  onLoad(opt) {
    //没有数据就回到首页

    this.isMorePage = getCurrentPages()?.length >= 2
    console.log("🚀 ~ this.isMorePage", this.isMorePage)

    console.log("opt", opt)

    if (opt) {
      //判断领取类型
      this.freeType = opt?.ft || ""
      if (opt.q || getApp().globalData.aliScanCode) {
        //扫描虚拟码进入
        let scanCodeUrl = undefined
        /* #ifdef MP-WEIXIN */
        scanCodeUrl = decodeURIComponent(opt.q)
        /* #endif */
        /* #ifdef MP-ALIPAY */
        //如果是支付宝扫普通二维码
        scanCodeUrl = decodeURIComponent(getApp().globalData.aliScanCode)
        getApp().globalData.aliScanCode = ""
        /* #endif */
        this.canUrl = scanCodeUrl
        //https://xh.xhwxc.com/mini/index?vscode=070816000937171
        // getApp().globalData.baseURL = utils.getUrlHttp(scanCodeUrl);
        // console.log("getApp().globalData.baseURL", getApp().globalData.baseURL);

        this.dataVsCode = utils.getUrlParams(scanCodeUrl, "vscode")
        this.dataMid = utils.getUrlDynamicData(scanCodeUrl, "mid")
        this.dataDeviceSn = utils.getUrlDynamicData(scanCodeUrl, "device_sn")
        this.freeType =
          utils.getUrlParams(scanCodeUrl, "ft") ||
          utils.getUrlDynamicData(scanCodeUrl, "ft") ||
          ""
        if (this.dataVsCode) {
          this.isVsCode = true
          console.log("扫码进入获取设备虚拟码vscode", this.dataVsCode)
        } else if (this.dataMid) {
          this.isMid = true
          console.log("扫码进入获取设备mid", this.dataMid)
        } else if (this.dataDeviceSn) {
          this.isScene = true
        }
      } else if (opt.scene || opt.name) {
        //扫描小程序码进入
        console.log("")
        if (opt.name) {
          this.dataDeviceSn = opt.name
        } else {
          let scanAppletCode = decodeURIComponent(opt.scene)
          this.dataDeviceSn = utils.getUrlParams("?" + scanAppletCode, "name")
          this.freeType = utils.getUrlParams("?" + scanAppletCode, "ft")
          // console.log("🚀 ~ 扫描小程序码获取得ft  freeType", this.freeType);
        }

        this.isScene = this.dataDeviceSn ? true : false
        console.log("扫描小程序码获取得设备编号", this.dataDeviceSn)
      } else if (opt.mid) {
        //首页扫码获取mid进入
        this.dataMid = opt.mid
        this.isMid = this.dataMid ? true : false
      } else if (opt.vscode) {
        //首页扫码获取 vscode虚拟码进入
        this.dataVsCode = opt.vscode
        this.isVsCode = this.dataVsCode ? true : false
      } else if (opt.device_sn) {
        this.dataDeviceSn = opt.device_sn
        this.isScene = this.dataDeviceSn ? true : false
      } else if (opt.store_id && opt.tableid) {
        this.miniFoodInfo.store_id = opt.store_id
        this.miniFoodInfo.tableid = opt.tableid
      }
      //判断目前是从哪里进入页面得
      // if (opt.url) {
      //   this.canUrl = opt.url
      // }
      // if (this.canUrl) {
      //   let domain = this.getDomainFromUrl(this.canUrl);
      //   // 初始化请求配置
      //   uni.$u.http.setConfig((config) => {
      //     /* config 为默认全局配置*/
      //     config.baseURL = domain; /* 根域名 */
      //     // 注：如果局部custom与全局custom有同名属性，则后面的属性会覆盖前面的属性，相当于Object.assign(全局，局部)
      //     config.custom = {
      //       isShowLoading: false,
      //       lodingTitle: '加载中'
      //     } // 全局自定义参数默认值
      //     return config
      //   })
      //   console.log("域名:", domain);
      // }

      if (this.isVsCode) {
        //根据虚拟码获取设备信息
        console.log("根据虚拟码获取设备信息vscode", this.dataVsCode)
        this.doGetDeviceAllInfoByVscode(this.dataVsCode)
      } else if (this.isMid) {
        //根据mid获取设备信息等
        console.log("根据mid获取设备信息mid", this.dataMid)
        this.doGetDeviceInfoByMid(this.dataMid)
      } else if (this.isScene) {
        //根据设备编号获取设备信息
        console.log("根据设备编号获取设备信息deviceSn", this.dataDeviceSn)
        this.doGetDeviceAllInfo(this.dataDeviceSn)
      } else {
        //没有数据就回到首页
        uni.redirectTo({ url: "/pages/index/index?scanCodeStatus=0" })
        return
      }
      // this.doGetAllChargeOrderList();
      //监测蓝牙连接成功事件
    }
  },
  onShow() {
    // console.log('设备号', this.deviceInfo.device_sn, this.vMemberInfo.is_have_special_price)
    if (this.deviceInfo.device_sn) {
      this.handleAd()
      this.getinfo()
    }

    //  let uptime=this.isUpdataTime

    uni.$off(globalEvents.EVENT_BLE_CONNECT_CHANGED)
    uni.$on(globalEvents.EVENT_BLE_CONNECT_CHANGED, (e) => {
      getApp().globalData.connected = e.connected
      if (this.subOrderTimer) clearInterval(this.subOrderTimer)
      this.subOrderTimer = setTimeout(() => {
        this.bBleConnected = e.connected

        // this.optTypeAfterBleConnected = 0;
        if (e.connected) {
          console.log("打印mtu", getApp().globalData.mtu)
          setTimeout(() => {
            this.readBattery((val) => {
              console.log("电量：", val)
              let data = {
                device_sn: this.deviceInfo.device_sn,
                ele: val,
              }
              updateUMEle(data)
                .then((res) => {})
                .catch((err) => {})
            }, this.deviceInfo.deviceType)
          }, 500)
        }
      })
    })
    if (this.vIsBleDevice && !this.bBleConnected) {
      this.startBleConnect()
    }
    setTimeout(() => {
      // console.log('进入计时', this.vMemberInfo.is_have_special_price)
      if (
        this.vMemberInfo.is_have_special_price &&
        this.goodList[0]?.second_price > 0
      ) {
        this.updateCountdown()
      }
    }, 300)
  },
  onHide() {
    if (this.showUpTimer) clearTimeout(this.showUpTimer)
  },
  onUnload() {
    console.log("页面周期函数 onUnload()")
    // 确保视频停止播放
    this.isShowLocalVideo = false
    // 清理定时器
    if (this.showUpTimer) clearTimeout(this.showUpTimer)
  },
  watch: {
    /*  #ifdef MP-ALIPAY */
    "brandInfo.brandName": function (newVal) {
      if (newVal) {
        uni.setNavigationBarTitle({
          title: newVal,
        })
      }
    },
    /* #endif */
  },
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.buy_box {
  position: relative;
}

.bubble {
  right: 8rpx;
  top: -53rpx;
  position: absolute;
  display: inline-block;
  padding: 15rpx 20rpx;
  font-size: 23rpx;
  z-index: 9;
  background-color: #ff1a1a;
  /* 气泡背景颜色 */
  border-radius: 15rpx;
  /* 圆角 */
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  /* 阴影效果 */
  border: 3rpx solid #fce167;
  color: white;
  /* 边框颜色 */
  box-sizing: border-box;
  /* 确保padding不影响元素大小 */
}

.bubble::before {
  content: "";
  position: absolute;
  bottom: -8rpx;
  /* 尖角朝下，距离气泡底部20px */
  left: 20rpx;
  /* 尖角位于气泡左侧，向右偏移20px */
  border-width: 15rpx;
  /* 尖角的高度（上下边框）和宽度（左右边框）的一半 */
  border-style: solid;
  border-color: #ff1a1a transparent transparent transparent;
  // border: 2rpx solid #fce167;
  transform: translateX(-40%) rotate(130deg);
  z-index: 8;
  /* 上边框颜色，其他边框透明形成尖角 */
  z-index: 0;
}

.shop-code {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .shop-code-imge {
    width: 550rpx;
    height: 550rpx;

    .image {
      width: 100%;
      height: 100%;
    }
  }

  .shop-code-text {
    color: #fff;
    font-size: 30rpx;
    text-align: center;
    margin-top: 20rpx;
  }
}

.cart-box {
  background-color: #fff;
  border-top-left-radius: 50rpx;
  border-top-right-radius: 50rpx;
  padding: 35rpx 30rpx;
  padding-bottom: 200rpx;
  position: relative;
}

.shop-btn {
  width: 700rpx;
  position: absolute;
  bottom: 20rpx;
}

::v-deep .u-popup__content {
  background-color: #3787ce00 !important;
}

.cart-top-left {
  display: flex;
  align-items: flex-end;
}

.cart-top {
  display: flex;
  justify-content: space-between;
}

.shopList-box {
  display: flex;
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  // padding: 20rpx;
  flex-wrap: wrap;

  /* 允许子元素换行 */
  .shop-item {
    width: 320rpx;
    margin: 0;
    margin-bottom: 10rpx;
    border: 1rpx solid rgba(192, 191, 191, 0.504);
    border-radius: 15rpx;
    // box-shadow: 0 0 8rpx 3rpx rgba(255, 133, 133, 0.3);
    height: 540rpx;
  }
}

.shop-phone-btn {
  background-color: transparent;
  /* 移除背景色 */
  border: none;
  /* 移除边框 */
  padding: 0;
  /* 移除内边距 */
  font: inherit;
  /* 继承父元素的字体 */
  color: inherit;
  /* 继承父元素的颜色 */
  cursor: pointer;
  /* 添加鼠标指针样式 */
  outline: none;
  /* 移除点击时的轮廓线 */
  /* 你可以添加更多的样式来进一步自定义按钮的外观 */
  // border: 0;
}

.shop-phone-btn::after {
  border: none;
}

.shop-box {
  padding: 0 30rpx;
  margin: 20rpx auto;

  .goods-shop {
    padding: 20rpx;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    // margin: 20rpx auto;

    .goods-title {
      display: flex;
      justify-content: space-between;
      line-height: 60rpx;
      align-items: center;

      .goods-h1 {
        font-weight: bold;
      }

      .goods-text {
        margin: 0;
        font-size: 25rpx;
        color: rgb(103, 218, 244);
      }
    }

    .goods-item {
      padding: 44rpx 70rpx 30rpx;
      border-radius: 20rpx;
    }
  }
}

.many {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-box {
  margin: 30rpx auto 0;
  width: 368rpx;

  .logo-img {
    width: 100%;
    height: 95rpx;
  }
}

.ad_alter {
  position: relative;

  .close_img {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
  }
}

.local-video {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;

  .video {
    width: 100%;
    height: 100%;
  }

  .close-video {
    position: absolute;
    right: 20rpx;
    top: 200rpx;
    padding: 10rpx 30rpx;
    border-radius: 20rpx;
    color: #fff;
    background-color: #00b3ab;
    color: #fff;
    font-size: 24rpx;
    font-weight: 700;
  }
}

.swiper_box {
  /* #ifdef MP-WEIXIN  */
  height: 260rpx;
  /* #endif */
  /* #ifdef MP-ALIPAY */
  height: 260rpx;

  /* #endif */
  .swiper-item {
    // background-color:white;
    // background-color:red;
    height: auto;
  }

  ::v-deep .common {
    padding: 0 !important;
  }

  image {
    width: 100%;
    height: 100%;
  }
}

.meal-area {
  margin: 20rpx 0;
}

::v-deep .u-button__text {
  font-size: 35rpx !important;
}

.nav-center {
  color: #666;
  font-size: $font-size-large;
}

.nav-left {
  color: #666;

  &-title {
    font-size: 28rpx;
  }
}

.nav-left-title {
  width: 220rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;

  display: -webkit-box;
}

.navbar-img {
  width: 88rpx;
  height: 88rpx;
}

.place-info {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 50rpx;
  padding: 24rpx 50rpx;
  background-color: #fdfdfd;
  box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);

  &-item {
    width: 31%;
    display: flex;
    align-items: center;
    overflow: hidden;
    /* 确保超出容器的文本被裁剪 */
    white-space: nowrap;
    /* 确保文本在一行内显示 */
    text-overflow: ellipsis;
    justify-content: center;

    /* 使用省略号表示文本超出 */
    .icon {
      flex-shrink: 0;
    }

    .label {
      font-size: 18rpx;
      margin-left: 10rpx;
      color: #666;
    }

    // &:nth-child(n + 3) {
    //   margin-top: 20rpx;
    // }
  }
}

.goods-area {
  padding: 0 30rpx;
  width: 100%;
  box-sizing: border-box;

  .goods-item {
    padding: 44rpx 70rpx 30rpx;
    border-radius: 20rpx;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
    background-color: #fff;

    &:nth-child(n + 2) {
      margin-top: 30rpx;
    }

    .coloum {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;

      .goods-but-title {
        font-weight: bold;
        font-size: 30rpx;
      }

      .goods-input {
        margin-top: 10rpx;
      }
    }

    .goods-info {
      display: flex;

      &-img {
        flex-shrink: 0;
        // width: 290rpx;
        // height: 290rpx;
        width: 250rpx;
        height: 250rpx;
        border-radius: 20rpx;
      }

      &-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        margin-left: 20rpx;
        box-sizing: border-box;

        &-title {
          flex-shrink: 0;
          font-size: 36rpx;
          font-weight: 700;
          margin-bottom: 10rpx;
          color: #333333;
          @include textMaxOneLine();
        }

        &-desc {
          margin-top: 15rpx;
          font-size: 24rpx;
          color: #333333;
          line-height: 1.5;
          @include textMaxTwoLine();
        }

        &-price {
          font-size: 24rpx;
          color: #666;
          @include flexRowBetween();
        }

        &-img {
          display: flex;
          justify-content: space-between;
          flex-shrink: 0;
          overflow: hidden;

          &-box {
            display: flex;
            width: 30%;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            image {
              width: 96rpx;
              height: 96rpx;
              border-radius: 50%;
            }

            view {
              margin-top: 16rpx;
              font-size: 20rpx;
              color: #333;
            }

            &:nth-child(n + 2) {
              margin-left: auto;
            }
          }
        }
      }
    }

    .goods-btn {
      > view {
        width: 550rpx;
        // height: 100rpx;
        margin: 0 auto;
        margin-top: 30rpx;
        border-radius: 15rpx;
        // overflow: hidden;

        &:nth-child(2) {
          margin-top: 20rpx;
        }
      }

      .free {
        height: 82rpx;
        position: relative;

        .free-text {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-weight: bold;
        }

        image {
          width: 100%;
          height: 100%;
        }
      }
    }

    .article {
      margin-top: 30rpx;
      margin-left: -40rpx;
    }
  }
}

.logo-box {
  margin: 30rpx auto 0;
  width: 368rpx;

  .logo-img {
    width: 100%;
    height: 95rpx;
  }
}

.ad_alter {
  position: relative;

  .close_img {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
  }
}

.local-video {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;

  .video {
    width: 100%;
    height: 100%;
  }

  .close-video {
    position: absolute;
    right: 20rpx;
    top: 200rpx;
    padding: 10rpx 30rpx;
    border-radius: 20rpx;
    color: #fff;
    background-color: #00b3ab;
    color: #fff;
    font-size: 24rpx;
    font-weight: 700;
  }
}
</style>
