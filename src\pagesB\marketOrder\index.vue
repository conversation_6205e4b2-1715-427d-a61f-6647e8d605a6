<template>
    <view>
        <BaseTabs :list="orderTypeNav" @change="onTabChangeType" />
        <CommonAd :ad="vAd.personalCenterListCustomAd" type="custom" />

        <ComList :loadingType="loadingType">
            <MarketOrderCard v-for="item in listData" :key="item.id" :info="item" />
        </ComList>
        <LoginPopup />

    </view>
</template>

<script>
import myPull from "@/mixins/myPull.js";
import { getMySelectOrderList } from "@/common/http/api";
import ComList from "../../components/list/ComList.vue";
import MarketOrderCard from '../components/MarketOrderCard.vue';
import BaseTabs from '../../components/base/BaseTabs.vue';
import CommonAd from '../../components/WxAd/CommonAd.vue';
import LoginPopup from '../../components/LoginPopup.vue';
export default {
    components: { ComList, MarketOrderCard, BaseTabs, CommonAd, LoginPopup },
    mixins: [myPull()],
    data() {
        return {
            orderTypeNav: [
                {
                    name: "全部",
                    status: "",
                },
                {
                    name: "待审核",
                    status: 0,
                },

                {
                    name: "待发货",
                    status: 1,
                },
                {
                    name: "发货完成",
                    status: 2,
                },
            ],
            curTabInfo: {},
        };
    },
    methods: {
        onTabChangeType(e) {
            this.curTabInfo = e;
            this.refresh();
        },
        getList(page, done) {
            let data = {
                page,
                status: this.curTabInfo.status,
            };
            getMySelectOrderList(data).then((res) => {
                done(res.data);
            });
        },
    },
    onLoad(opt) {
        this.refresh();
    },

};
</script>
<style lang="scss">
page {
    background-color: #f4f4f4;
}
</style>
<style lang="scss" scoped>
.orderTypeBar {
    background-color: #fff;
    padding-bottom: 6rpx;
}
</style>