<template>
  <view v-if="initIsLogin && vMemberInfo.avatarUrl == undefined && isNeedAuth && isGetEnd
    " class="authorized_box authorized_box_show">
    <view class="getphonebox">
      <view class="getPhone">温馨提示</view>
      <view class="center_text">
        <view class="img1box">
          <image class="img1" src="/static/public/default_heading.jpg"></image>
        </view>
        <text class="askinfo">申请获取您的登录信息</text>
        <!-- #ifndef MP-ALIPAY -->
        <view class="ask_btn" @click="getUserProfile">立即登录</view>
        <!-- #endif -->

        <!-- #ifdef MP-ALIPAY -->
        <button class="ask_btn" type="primary" open-type="getAuthorize" scope="userInfo" @getAuthorize="getUserProfile"
          @error="onAuthError">
          立即登录
        </button>
        <!-- #endif -->
      </view>
    </view>
  </view>
</template>
<script>
import { login, updateUserInfo, getUserInfo } from '@/common/http/api'
export default {
  props: {
    isNeedAuth: { type: Boolean, default: false }, // 是否需要授权
    isReloadPage: { type: Boolean, default: true }, // 是否重新加载界面
  },
  data() {
    return {
      bClickPermissionBtn: false,
      loginCode: '',
      initIsLogin: false, //适配抖音小程序闪屏问题，初始化为false， 加载完成再改为true;
      isGetEnd: true, // 数据获取是否结束
    }
  },
  methods: {
    getUserProfile() {
      if (this.bClickPermissionBtn) {
        //如果已经点击了授权按钮
        return
      }
      let that = this
      /* #ifdef MP-TOUTIAO */
      this.initCode() //写在这里是因为 头条小程序获取用户信息前必须 获取login的code
      /* #endif */
      if (!this.loginCode) {
        this.initCode()
      }
      this.bClickPermissionBtn = true
      setTimeout(() => {
        that.bClickPermissionBtn = false
      }, 1000)
      uni.getProvider({
        service: 'oauth',
        success: (res) => {
          console.log('🚀 ~ 获取用户信息 uni.getProvider ：', res)
          /* #ifdef MP-WEIXIN||MP-TOUTIAO */
          uni.getUserProfile({
            desc: '申请获取用户信息，提供会员服务',
            force: true, //头条小程序，是否弹窗
            success: (info) => {
              console.log('🚀 ~ 获取用户信息：', info)
              this.updateInfo(info.userInfo)
            },
            fail: (err) => {
              console.log('🚀 ~ 获取用户信息err', err)
              this.isShowErr('登录授权失败~')
            },
          })
          /* #endif */
          /* #ifdef MP-ALIPAY */
          uni.getUserInfo({
            withCredentials: true,
            success: (info) => {
              const userInfo = JSON.parse(info.response).response
              if (userInfo.msg == 'Success') {
                this.updateInfo(userInfo)
              }
            },
            fail: (err) => {
              console.log('🚀 ~ 获取用户信息err', err)
              this.isShowErr('登录授权失败~')
            },
          })
          /* #endif */
          /* #ifdef H5 */
          this.login()
          /* #endif */
        },
      })
    },
    //获取登录Code
    initCode() {
      console.log('进行', this.loginCode)
      // if (this.loginCode) {
      //   return
      // }
      /* #ifndef H5 */
      uni.login({
        // #ifdef MP-WEIXIN
        provider: 'weixin',
        // #endif
        // #ifdef MP-ALIPAY
        provider: 'alipay',
        // #endif
        /* #ifdef MP-TOUTIAO */
        provider: 'toutiao',
        /* #endif */
        success: (res) => {
          console.log('🚀 ~ res2-----------------------------', res)
          this.loginCode = res.code
          if (res.code == 0) {
            console.log('code=0')
          }
          this.login()
        },
        fail: (err) => {
          // this.isShowErr('登录授权失败~')
          console.log('调用login失败,err=', err)
        },
      })
      /* #endif */
      /* #ifdef H5 */
      this.loginCode = this.getCode()
      if (this.loginCode) {
        this.isShowErr('调起登录~')
        this.login()
      }
      /* #endif */
    },
    getCode() {
      let code = ''
      let url = location.search
      if (url.indexOf('?') != -1) {
        var strs = url.substr(1).split('&')
        let ua = window.navigator.userAgent.toLowerCase()
        //判断是不是微信
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          // 微信
          console.log('微信浏览器')
          for (var i in strs) {
            if (strs[i].indexOf('code') == 0) {
              code = strs[i].split('=')[1]
            }
          }
        }
        //判断是不是支付宝
        if (ua.match(/AlipayClient/i) == 'alipayclient') {
          //支付宝
          console.log('支付宝浏览器')
          for (var i in strs) {
            if (strs[i].indexOf('auth_code') == 0) {
              code = strs[i].split('=')[1]
            }
          }
        }
      }
      return code
    },
    login() {
      let data = {
        code: this.loginCode,
      }
      let that = this
      login(data).then((rtnLogin) => {
        let rows = rtnLogin.user_info
        /* #ifdef MP-WEIXIN||MP-TOUTIAO */
        uni.$u.vuex('vSession3rd', rows.session3rd)
        /* #endif */
        /* #ifdef MP-ALIPAY */
        uni.$u.vuex('vSession3rd', rows.ali_id)
        /* #endif */
        /* #ifdef H5 */
        let ua = window.navigator.userAgent.toLowerCase()
        //判断是不是微信
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          // 微信
          console.log('微信浏览器')
          uni.$u.vuex('vSession3rd', rows.session3rd)
        }
        //判断是不是支付宝
        if (ua.match(/AlipayClient/i) == 'alipayclient') {
          //支付宝
          console.log('支付宝浏览器')
          uni.$u.vuex('vSession3rd', rows.ali_id)
        }
        /* #endif */
        uni.$u.vuex('vIsLogin', true)
        // 会员信息
        /* #ifdef MP-WEIXIN */
        if (rtnLogin && rtnLogin.member_info && rtnLogin.member_info.headimgurl) {
          let obj = {
            ...rtnLogin.member_info,
            headimgurl: ''
          }
          console.log('身份信息', obj)
          that.updateInfo(obj)
        } else {
          uni.$u.vuex('vMemberInfo', rtnLogin.member_info,)
        }
        /* #endif */
        /* #ifndef  MP-WEIXIN*/
        uni.$u.vuex('vMemberInfo', rtnLogin.member_info,)
        /* #endif */
        if (!rtnLogin.member_info.device_sn) {
          setTimeout(() => {
            this.$emit('bindDeviceSn')
          },2000)


        }

      })
    },
    async updateInfo(userInfo) {
      if (this.vIsLogin == false) {
        await this.login()
      }
      if (userInfo) {
        //更新用户信息
        /* #ifdef MP-ALIPAY */
        if (userInfo) {
          userInfo.avatarUrl = userInfo.avatar //这是为了同步微信  头像的url字段名
        }
        /* #endif */

        let that = this
        let uParams = {
          city: userInfo.city || '',
          nickName: userInfo.user_nickname || '',
          province: userInfo.province || '',
          gender: userInfo.gender || '',
          /* #ifdef MP-WEIXIN||MP-TOUTIAO */
          avatarUrl: userInfo.avatarUrl || '',
          session3rd: this.vSession3rd || '',
          /* #endif */
          /* #ifdef MP-ALIPAY */
          avatarUrl: userInfo.avatarUrl || '',
          ali_id: this.vSession3rd || '',
          /* #endif */
        }
        updateUserInfo(uParams)
          .then((res) => {
            that.isGetEnd = false
            console.log('更新用户信息 成功 1 -----------------------', userInfo, res)
            //设置已获取用户信息flag
            uni.$u.vuex('vIsLogin', true)
            /* #ifdef  MP-WEIXIN*/
            // uni.$u.vuex('vMemberInfo', {...that.vMemberInfo,...userInfo})
            that.getInfo()
            /* #endif */
            /* #ifndef MP-WEIXIN*/
            this.isShowSuccess('登录成功', 0, () => {
              if (that.isReloadPage) {
                this.vPageFullPath &&
                  uni.redirectTo({ url: this.vPageFullPath })
              }
            })
            /* #endif */

          })
          .catch((err) => {
            this.isShowErr('更新用户信息失败~')
          })
      }
    },
    onAuthError() {
      uni.showToast({
        title: '授权失败,请重新授权',
      })
    },
    getUserInfoHandle() {
      if (!this.vMemberInfo) {
        getUserInfo().then((res) => {
          uni.$u.vuex('vMemberInfo', res)
        })
      }
    },
    getInfo() {
      getUserInfo().then((res) => {
        uni.$u.vuex('vMemberInfo', res)
      })
    }
  },
  mounted() {
    let timer = setTimeout(() => {
      this.initIsLogin = true
      clearTimeout(timer)
    }, 200)
    let pages = getCurrentPages()
    let currPages = pages[pages?.length - 1]
    if (!this.vIsLogin) {
      this.initCode()
    } else {
      this.getUserInfoHandle()
    }

    // console.log('用户信息--->', this.vMemberInfo.avatarUrl)
    // this.initCode();
    uni.$u.vuex(
      'vPageFullPath',
      decodeURIComponent(currPages?.$page?.fullPath) ?? '',
    )
    console.log('vPageFullPath-----------------------', this.vPageFullPath)
    uni.$off('onlogon')
    uni.$off('updataUser')
    uni.$on('onlogon', (res) => {
      console.log('登录')
      this.initCode()
    })
    uni.$on('updataUser', (userInfo) => {
      console.log('登录')
      this.updateInfo(userInfo)
    })
  },

}
</script>

<style scoped lang="scss">
.authorized_box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1110;
  opacity: 0;
  outline: 0;
  text-align: center;
  -ms-transform: scale(1.185);
  transform: scale(1.185);
  backface-visibility: hidden;
  perspective: 2000rpx;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.authorized_box_show {
  opacity: 1;
  transition-duration: 0.3s;
  -ms-transform: scale(1);
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}

.getphonebox {
  width: 550rpx;
  height: 500rpx;
  background: #fff;
  border-radius: 30rpx;
  margin: auto;
}

.getPhone {
  width: 100%;
  height: 105rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666666;
  line-height: 105rpx;
  border-bottom: 1rpx solid #e7e7e7;
}

.center_text {
  width: 100%;
  height: 380rpx;
  display: flex;
  flex-direction: column;

  align-items: center;
}

.center_text image {
  width: 100%;
  height: 100%;
  /* padding: 40rpx 0; */
}

.img1box {
  width: 150rpx;
  height: 150rpx;
  margin: 20rpx 0;
  border-radius: 50%;
  overflow: hidden;
}

.askinfo {
  font-size: 26rpx;
  color: #666666;
}

.ask_btn {
  width: 230rpx;
  height: 60rpx;
  font-size: 26rpx;
  text-align: center;
  line-height: 60rpx;
  border-radius: 30rpx;
  margin-top: 40rpx;
  border: none;
  outline: none;
  color: #fff;
  background: #1cbbb4;

  &::after {
    border: none;
  }
}
</style>
