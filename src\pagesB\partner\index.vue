<template>
    <!--成为合伙人页面-->
    <view class="content">
        <BaseNavbar :isShowSlot="false" bgColor="rgba(255, 255, 255,0)" :placeholder="false" color="#fff" />
        <image class="bg" :src="vBaseInfo.img_public.img_recruit_base" mode="widthFix"></image>

        <view class="formWrap">
            <image class="formBg" :src="vBaseInfo.img_public.img_recruit_background" mode="widthFix"></image>
            <view class="inputLine">
                <image class="imgPhone" src="@/pagesB/static/icon/ic_phone_orange.png"></image>
                <view class="input">
                    <input type="digit" placeholder="点击授权手机号领取最新招商政策" v-model="inputPhone" :disabled="true" />
                    <button v-if="!inputPhone" class="phone-btn" open-type="getPhoneNumber"
                        @getphonenumber="getPhoneNumberFun"></button>
                </view>
            </view>
            <view class="btnGet" @click="leavingMessage">我要投资</view>
        </view>
        <CommonAd :ad="vAd.companionInsertScreenAd" type="inter" />
        <LoginPopup />

    </view>
</template>

<script>

import { getPhoneNumber, getInvestPrice } from "@/common/http/api";
import BaseNavbar from '../../components/base/BaseNavbar.vue';
import CommonAd from '../../components/WxAd/CommonAd.vue';
import LoginPopup from '../../components/LoginPopup.vue';
export default {
    components: { BaseNavbar, CommonAd, LoginPopup },
    name: "index",
    data() {
        return {
            inputPhone: "",
            device_sn: "",

        };
    },
    methods: {
        leavingMessage() {
            if (this.inputPhone.length != 11)
                return uni.showToast({
                    title: "请授权正确手机号~",
                    icon: "none",
                    mask: true,
                });

            if (this.device_sn) {
                let data = {
                    device_sn: [this.device_sn],
                };
                getInvestPrice(data).then((res) => {
                    if (parseFloat(res?.[0]?.price) >= 0) {
                        return uni.navigateTo({
                            url: `./ConfirmPartner?device_sn=${res?.[0]?.device_sn
                                }&price=${res?.[0]?.price || 0}&phone=${this.inputPhone}`,
                        });
                    } else {
                        return uni.navigateTo({
                            url: `./ConfirmPartner?phone=${this.inputPhone}`,
                        });
                    }
                });
            } else {
                return uni.navigateTo({
                    url: `./ConfirmPartner?phone=${this.inputPhone}`,
                });
            }
            return;
        },

        getPhoneNumberFun(e) {
            console.log("🚀 ~ e", e)

            uni.showLoading({
                title: "请等待",
                mask: false,
            });
            const { encryptedData, iv, code } = { ...e.detail };
            if (e.detail.errMsg == "getPhoneNumber:ok") {
                this.login(code, encryptedData, iv);
            } else {
                uni.showToast({
                    title: "获取手机号授权失败，请重新获取~",
                    icon: "none",
                });
            }
        },
        async login(code, encryptedData, iv) {
            uni.hideLoading();
            let data = {
                code,
            };
            let rtn = await getPhoneNumber(data);
            console.log("🚀 ~ rtn", rtn);
            if (rtn) {
                this.inputPhone = rtn;
                this.isShowSuccess('授权手机号成功')
            } else {
                this.isShowErr('授权获取手机号失败~')
            }
        },
    },

    onLoad(opt) {
        this.device_sn = opt?.device_sn || this.vDeviceSn || "";
    },
};
</script>

<style scoped lang="scss">
.content {
    position: relative;

    .back {
        position: fixed;
        left: 30rpx;
        width: 20rpx;
        height: 34rpx;
        z-index: 999;
        padding: 20rpx;
        padding-left: 0;
    }

    .bg {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        z-index: -1;
        width: 100vw;
    }

    .formWrap {
        position: absolute;
        width: 690rpx;
        height: 698rpx;
        left: 30rpx;
        right: 30rpx;
        top: 934rpx;

        .formBg {
            position: absolue;
            left: 0;
            right: 0;
            width: 690rpx;
            top: 0;
            bottom: 0;
            z-index: 9;
        }

        .inputLine {
            position: absolute;
            z-index: 99;
            top: 403rpx;
            left: 31rpx;
            right: 31rpx;
            width: 628rpx;
            height: 112rpx;
            box-sizing: border-box;
            padding: 0 23rpx;
            background: white;
            border: 4rpx solid #ffca28;
            border-radius: 10rpx;
            @include flexRowVertCenter();

            .imgPhone {
                width: 33rpx;
                height: 48rpx;
            }

            input {
                margin-left: 20rpx;
                flex: 1;
                box-sizing: border-box;
                padding: 0 20rpx;
                height: 100rpx;
                color: $textBlack;
                font-size: $font-size-middle;

                &::-webkit-input-placeholder {
                    font-size: $font-size-base;
                    content: $textGray;
                }
            }

            .input {
                width: 100%;
                position: relative;

                .phone-btn {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba($color: #fff, $alpha: 0.1);
                    z-index: 9999;
                    border: none;

                    &::after {
                        border: none;
                    }
                }
            }
        }

        .btnGet {
            height: 80rpx;
            background: linear-gradient(-8deg, #f56830, #f58830, #ffa418);
            border-radius: 40rpx;
            color: white;
            font-weight: bold;
            font-size: $font-size-xxlarge;
            position: absolute;
            left: 57rpx;
            right: 57rpx;
            bottom: 50rpx;
            z-index: 99;
            @include flexAllcenter();
        }
    }
}
</style>
