<template>
    <view>
        <ComList :loadingType="loadingType">
            <NearbyMerchantsCard v-for="item in listData" :key="item.id" :info="item" />
        </ComList>
    </view>
</template>
<script>
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull";
import NearbyMerchantsCard from "./components/NearbyMerchantsCard.vue";
import { getHotelInvestPrice, getMachinAdPrice } from "@/common/http/api";
export default {
    components: { ComList, NearbyMerchantsCard },
    mixins: [myPull()],
    data() {
        return {
            fromData: "",
        };
    },
    methods: {
        async getList(page, done) {
            let data = {
                page,
                limit: 5,
                lng: this.vCurLocation.longitude,
                lat: this.vCurLocation.latitude,
                radius: 30,
            },
                rtn = null;

            if (this.fromData === "investment") {
                rtn = await getHotelInvestPrice(data);
            } else if (this.fromData === "ad") {
                rtn = await getMachinAdPrice(data);
            }

            done(rtn.list);
        },
    },
    onLoad(opt) {
        this.fromData = opt?.from;
        console.log("🚀 ~ this.fromData", this.fromData);
        this.refresh();
    },
};
</script>

<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
</style>
