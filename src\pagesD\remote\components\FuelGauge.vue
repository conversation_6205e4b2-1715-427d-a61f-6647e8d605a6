<template>
  <view class="fuel-gauge">
    <!-- 油表盘图片 -->
    <image src="../../static/仪表盘.png" mode="aspectFit" />

    <!-- <veiw class="fuel-gauge-bottom"> -->
    <!-- 数字按钮 -->
    <!-- <view class="ctrl-btn one" :class="isPlay ? 'active' : ''" @touchend="playPress()">
        <view class="button-background">
          <image src="../../static/摇摇车.svg" :class="isForwardActive ? 'imagAc' : 'imag'" alt="图标" />
          <view class="txt">摇一摇</view>
        </view>
      </view> -->
    <!-- 按钮组容器 -->
    <view class="left-box">
      <!-- 中控开启按钮 -->
      <view
        class="ctrl-btn stop"
        :class="isCentralActive ? 'active' : ''"
        @touchend="centralPressEnd()"
      >
        <view class="button-background">
          <image
            style="width: 100px; height: 100px"
            :src="
              isCentralActive
                ? '../../static/中控active.svg'
                : '../../static/中控end.svg'
            "
            :class="isCentralActive ? 'imagAc' : 'imag'"
            alt="图标"
          />
          <view class="txt">{{
            isCentralActive ? "中控关闭" : "中控开启"
          }}</view>
        </view>
      </view>
      <!-- 摇摆模式按钮 -->
      <view
        class="ctrl-btn stop"
        :class="isSwingActive ? 'active' : ''"
        @touchend="swingPressEnd()"
      >
        <view class="button-background">
          <image
            :src="
              isSwingActive
                ? '../../static/摇摆active.svg'
                : '../../static/摇摆end.svg'
            "
            :class="isSwingActive ? 'imagAc' : 'imag'"
            alt="图标"
          />
          <view class="txt">{{ isSwingActive ? "摇摆开启" : "摇摆关闭" }}</view>
        </view>
      </view>
    </view>
    <view class="button-group">
      <!-- 刹车按钮 -->
      <view
        class="ctrl-btn stop"
        :class="isStop ? 'active' : ''"
        @touchstart="stopPressStart()"
        @touchend="stopPressEnd()"
      >
        <view class="button-background">
          <image
            :src="
              isStop
                ? '../../static/刹车active.svg'
                : '../../static/刹车end.svg'
            "
            :class="isForwardActive ? 'imagAc' : 'imag'"
            alt="图标"
          />
          <view class="txt">刹车</view>
        </view>
      </view>

      <!-- 驻车按钮 -->
      <view
        class="ctrl-btn parking"
        :class="isParkingActive ? 'active' : ''"
        @touchstart="parkingPressStart()"
        @touchend="parkingPressEnd()"
      >
        <view class="button-background">
          <image
            :src="
              isParkingActive
                ? '../../static/驻车active.svg'
                : '../../static/驻车end.svg'
            "
            :class="isParkingActive ? 'imagAc' : 'imag'"
            alt="图标"
          />
          <view class="txt">{{
            isParkingActive ? "驻车开启" : "驻车关闭"
          }}</view>
        </view>
      </view>
    </view>

    <!-- </veiw> -->
  </view>
</template>

<script>
export default {
  props: {
    isStop: {
      type: Boolean,
      default: false,
    },
    isPlay: {
      type: Boolean,
      default: false,
    },
    isParkingActive: {
      type: Boolean,
      default: false,
    },
    isSwingActive: {
      type: Boolean,
      default: false,
    },
    isCentralActive: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    stopPressStart() {
      console.log("🎯 FuelGauge: stopPressStart, isStop:", this.isStop)
      this.$emit("stop-press-start")
    },
    stopPressEnd() {
      console.log("🎯 FuelGauge: stopPressEnd, isStop:", this.isStop)
      this.$emit("stop-press-end")
    },
    // 保留原方法以兼容
    stopPress() {
      this.$emit("stop-press")
    },

    // 驻车按钮方法
    parkingPressStart() {
      console.log(
        "🎯 FuelGauge: parkingPressStart, isParkingActive:",
        this.isParkingActive
      )
      this.$emit("parking-press-start")
    },
    parkingPressEnd() {
      console.log(
        "🎯 FuelGauge: parkingPressEnd, isParkingActive:",
        this.isParkingActive
      )
      this.$emit("parking-press-end")
    },

    // 摇摆模式按钮方法
    swingPressStart() {
      console.log(
        "🎯 FuelGauge: swingPressStart, isSwingActive:",
        this.isSwingActive
      )
      this.$emit("swing-press-start")
    },
    swingPressEnd() {
      console.log(
        "🎯 FuelGauge: swingPressEnd, isSwingActive:",
        this.isSwingActive
      )
      this.$emit("swing-press-end")
    },

    // 中控开启按钮方法
    centralPressStart() {
      console.log(
        "🎯 FuelGauge: centralPressStart, isCentralActive:",
        this.isCentralActive
      )
      this.$emit("central-press-start")
    },
    centralPressEnd() {
      console.log(
        "🎯 FuelGauge: centralPressEnd, isCentralActive:",
        this.isCentralActive
      )
      this.$emit("central-press-end")
    },
  },
}
</script>

<style scoped lang="scss">
.left-box {
  display: flex;
  position: absolute;
  right: 560rpx;
  bottom: -70rpx;
  transform: translateY(-50%);
  gap: 20rpx;
}
.bigbox {
  position: relative;
  background-color: red;
  z-index: 99;
  width: 100%;
  height: 100%;
}

.fuel-gauge {
  z-index: 2;
  width: 600rpx;
  height: 300rpx;
  border-radius: 50%;
  background-color: rgb(34, 34, 34);
  position: absolute;
  left: 50%; /* 将元素左侧边缘对齐父元素中心 */
  transform: translateX(-50%); /* 向左移动自身宽度的50% */
  bottom: 80rpx;
  // background-color: pink;
  // .ctrl-btn stop {
  //   width: 150rpx;
  //   height: 150rpx;
  //   position: relative;
  //   left: 50%;
  //   bottom: 80rpx;

  //   transform: translateX(-50%); /* 向左移动自身宽度的50% */

  //   background-color: red;
  // }
  .imagAc {
    width: 80rpx;
    height: 80rpx;
    top: 0;
  }

  .button-background {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .txt {
    font-size: 30rpx;
  }

  .imag {
    width: 80rpx;
    height: 80rpx;
    top: 0;

    /* 蓝色 */
  }

  // border: 1rpx solid red;
  image {
    position: relative;
    top: -70rpx;
    width: 100%;
    height: 100%;
  }

  /* 左侧按钮组样式 */
  .left-box {
    z-index: 10;

    .ctrl-btn {
      /* 重置左侧按钮的位置 */
      left: 0 !important;
      bottom: 0 !important;
      position: relative !important;

      /* 统一左侧按钮图片尺寸 */
      image {
        width: 80rpx !important;
        height: 80rpx !important;
        top: 0 !important;
        position: relative !important;
      }

      .imag,
      .imagAc {
        width: 80rpx !important;
        height: 80rpx !important;
      }
    }
  }

  /* 按钮组样式 */
  .button-group {
    display: flex;
    flex-direction: row;
    gap: 20rpx;
    position: absolute;
    bottom: -70rpx;
    left: 30%;
    transform: translateX(-50%);
    z-index: 10;
  }

  .ctrl-btn {
    /* 保持原有样式 */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 100rpx;
    font-size: 40rpx;
    font-weight: bold;
    color: white;
    background-color: rgba(128, 128, 128, 0.273);
    position: relative;
    left: 600rpx;
    bottom: 50rpx;
    /* 确保按钮内容在伪元素之上 */
    z-index: 1;
    /* 为伪元素定位 */
    overflow: hidden;
    /* 隐藏溢出效果 */
    border-radius: 12rpx;
    /* 可选圆角 */

    /* 科幻发光边框 */
    box-shadow: 0 0 20rpx rgba(0, 255, 255, 0.3),
      inset 0 0 20rpx rgba(0, 255, 255, 0.3);
    border: 1rpx solid rgba(0, 255, 255, 0.6);

    /* 添加流动光效动画 */
    &::before {
      content: "";
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        45deg,
        transparent 25%,
        rgba(0, 255, 255, 0.2) 50%,
        transparent 75%
      );
      z-index: 0;
    }

    &::after {
      content: "";
      position: absolute;
      inset: 2rpx;
      border: 1rpx solid rgba(0, 255, 255, 0.3);
      border-radius: 10rpx;
      animation: pulse 2s ease-in-out infinite;
    }

    transition: all 0.3s ease;
    /* 添加过渡效果 */
  }

  .active {
    background-color: rgba(0, 255, 255, 0.15);
    /* 增强背景亮度 */
    color: #7df9ff;
    /* 更亮的霓虹青色 */
    text-shadow: 0 0 15rpx rgba(125, 249, 255, 0.8);
    /* 文字发光 */

    /* 增强边框光效 */
    box-shadow: 0 0 30rpx rgba(0, 255, 255, 0.5),
      inset 0 0 30rpx rgba(0, 255, 255, 0.5);
    border: 1rpx solid rgba(0, 255, 255, 0.8);

    /* 增强内部动画效果 */
    &::before {
      background: linear-gradient(
        45deg,
        transparent 25%,
        rgba(0, 255, 255, 0.4) 50%,
        /* 提高流光亮度 */ transparent 75%
      );
      animation-duration: 2s;
      /* 加快流动速度 */
    }

    &::after {
      border: 1rpx solid rgba(0, 255, 255, 0.6);
      animation: pulse 0.8s ease-in-out infinite;
      /* 加快脉冲频率 */
    }
  }

  @keyframes flow {
    0% {
      transform: rotate(45deg) translateX(-50%);
    }

    100% {
      transform: rotate(45deg) translateX(50%);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.6;
      transform: scale(1);
    }

    50% {
      opacity: 1;
      transform: scale(1.02);
    }
  }

  &-bottom {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 20rpx;
    // top: 0;
    // top: 0;
    width: 375rpx;
    height: 100rpx;
    // border: 1rpx solid red;
    display: flex;
    justify-content: space-between;
    // bottom: 40rpx;
  }

  .stop {
    width: 150rpx;
    height: 100rpx;
  }

  .parking {
    width: 150rpx;
    height: 100rpx;
  }

  .button-background {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
