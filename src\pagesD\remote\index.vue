<template>
  <view class="page-wrapper">
    <!-- 🎯 导航栏放在横屏容器外部，保持竖屏显示 -->
    <!-- #ifndef MP-ALIPAY -->
    <BaseNavbar
      v-if="showCustomTitle"
      :isShowSlot="true"
      bgColor="#ffffff"
      color="#333"
      :bold="true"
      :title="title"
    >
      <view @click="handleLeftClick" style="padding: 10rpx">
        <u-icon name="arrow-left" size="24" color="#333" />
      </view>
    </BaseNavbar>
    <!-- #endif -->
    <!-- 背景图 -->
    <image
      class="bg-image"
      style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100vh;
        height: 100vw;
        z-index: 0;
        object-fit: cover;
        transform-origin: left top;
        transform: rotate(90deg) translateY(-100%);
      "
      src="https://img2.baidu.com/it/u=1242887946,1467773963&fm=253&fmt=auto&app=138&f=JPEG?w=698&h=500"
      mode="aspectFill"
    />

    <!-- 🎯 横屏内容容器 -->
    <view
      class="landscape-container"
      :class="{ 'with-navbar': showCustomTitle }"
    >
      <view @click="handleLeftClick" class="back-button">
        <u-icon name="arrow-left" size="18" color="rgba(255, 255, 255, 0.9)" />
        <text class="back-text">返回</text>
      </view>
      <!-- <button
        @click="toggleDevelopmentMode"
        @touchstart.stop="toggleDevelopmentMode"
        class="toggle-dev-mode-button"
      >
        ×
      </button> -->
      <!-- 主体内容容器 -->
      <view class="content-wrapper">
        <!-- 圆弧背景纯黑色 -->
        <view class="ellipse"></view>
        <!-- 顶部调速区域 -->
        <view class="top-speed-control">
          <SteeringWheel
            :isStop="isStop"
            :steering-angle="steeringAngle"
            :totalTime="totalTime"
            :remainingTime="remainingTime"
            @gear-press="addEventLog('档位按下: ' + $event)"
            @gear-release="addEventLog('档位释放')"
            @gear-change="handleGearChange($event)"
            @speed-change="sendCommand('speed', $event)"
            @steer-start="handleTouchStart"
            @steer-change="handleSteerChange"
            @steer-end="handleButtonRelease('steer')"
          />
        </view>

        <!-- 主控制区域 -->
        <view class="main-controls-layout">
          <!-- 左侧前进后退控制 -->
          <view class="left-side">
            <ControlPanel
              :isStop="isStop"
              :bBleConnected="bBleConnected"
              :isPlay="isPlay"
              :is-forward-active="isForwardActive"
              :is-back-active="isBackActive"
              :bluetoothOn="bluetoothOn"
              :wxBluetoothAuth="wxBluetoothAuth"
              :deviceSearchStatus="deviceSearchStatus"
              @forward-press="sendCommand('forward', true)"
              @forward-release="handleButtonRelease('forward')"
              @back-press="sendCommand('backward', true)"
              @back-release="handleButtonRelease('backward')"
              @reconnect-bluetooth="handleReconnectBluetooth"
            />
          </view>

          <!-- 右侧左右转控制 -->
          <view class="right-side">
            <TurnControls
              :is-left-turn-active="isLeftTurnActive"
              :is-right-turn-active="isRightTurnActive"
              @left-turn-press="sendCommand('left', true)"
              @left-turn-release="handleButtonRelease('left')"
              @right-turn-press="sendCommand('right', true)"
              @right-turn-release="handleButtonRelease('right')"
            />
          </view>
        </view>

        <!-- 底部仪表盘区域 -->
        <view class="bottom-gauge-area">
          <!-- 仪表盘图片 -->
          <view class="fuel-gauge-container">
            <FuelGauge
              :isStop="isStop"
              :isPlay="isPlay"
              :isParkingActive="isParkingActive"
              :isSwingActive="isSwingActive"
              :isCentralActive="isCentralActive"
              @stop-press-start="sendCommand('brake', true)"
              @stop-press-end="handleButtonRelease('brake')"
              @parking-press-start="sendCommand('parking', true)"
              @parking-press-end="() => {}"
              @swing-press-end="sendCommand('swing', true)"
              @central-press-end="sendCommand('central', true)"
              @play-press="handlePlayPress"
            />
            <view
              class="steering-wheel"
              @touchstart="onTouchStart"
              @touchmove="onTouchMove"
              @touchend="onTouchEnd"
              :style="{
                transform: `translateX(-50%) rotate(${wheelAngle}deg)`,
                transition: isTouching ? 'none' : 'transform 0.3s ease-out',
              }"
            >
              <image
                src="../static/方向盘.png"
                mode="aspectFit"
                class="wheel-img"
              />
            </view>
          </view>
          <!-- 当前操作状态提示 -->
          <view v-if="currentActionStatus" class="action-status-tip">
            {{ currentActionStatus }}
          </view>
        </view>

        <!-- 🎯 页面参数显示 - 仅开发环境显示 -->
        <!-- <view v-if="isDevelopment" class="page-params-log">
          <view class="params-header">
            <text class="params-title" style="color: white">📋 扫码参数</text>
          </view>
          <view class="params-content">
            <view class="debug-section">
              <text class="debug-label" style="color: white">订单号:</text>
              <text class="params-text" style="color: #00ff00">
                {{ pageParams.order_sn || "未找到" }}
              </text>
            </view>

            <view class="debug-section">
              <text class="debug-label" style="color: #00ff00">手动测试:</text>
              <input
                v-model="testOrderSn"
                placeholder="输入支付宝订单号"
                class="test-input"
              />
              <button @click="testGetOrderData" class="test-button">
                测试获取订单数据
              </button>
              <button @click="scanQRCode" class="test-button scan-button">
                📷 扫描二维码测试
              </button>
            </view>
          </view>
        </view> -->

        <!-- 🎯 蓝牙通信记录列表 - 仅开发环境显示 -->
        <!-- <view v-if="isDevelopment" class="communication-log">
          <view class="log-header">
            <text class="log-title">📡 通信数据</text>
            <view class="log-controls">
              <button
                size="mini"
                @click="clearData"
                type="primary"
                plain="true"
              >
                清空
              </button>

              <text class="log-count"
                >{{ allDataList.length || 0 }}条 (显示{{
                  displayDataList.length
                }}条)</text
              >
            </view>
          </view>
          <view class="viewDivider"></view>
          <view class="scanResultGroup">
            <view
              class="result-content"
              :class="
                item.type === 'send'
                  ? 'send-item'
                  : item.type === 'receive'
                  ? 'receive-item'
                  : 'event-item'
              "
              v-for="(item, index) in displayDataList"
              :key="index"
            >
              <template v-if="item.type === 'event'">
                {{ item.time }}：[事件] {{ item.meaning }}
              </template>
              <template v-else-if="item.type === 'send'">
                {{ item.time }}：{{ item.data }} [发送] {{ item.meaning }}
                <span
                  :class="
                    item.broadcastStatus === 'success'
                      ? 'broadcast-success'
                      : 'broadcast-failed'
                  "
                  class="broadcast-status"
                >
                  [{{
                    item.broadcastStatus === "success" ? "✅成功" : "❌失败"
                  }}]
                </span>
                <span
                  v-if="item.broadcastStatus === 'failed'"
                  class="broadcast-error"
                >
                  {{ item.broadcastMessage }}
                </span>
              </template>
              <template v-else>
                {{ item.time }}：{{ item.data }} [接收] {{ item.meaning }}
              </template>
            </view>
            <view v-if="allDataList.length === 0" class="no-data-tip">
              暂无通信数据，请发送指令等待设备响应
            </view>
          </view>
        </view> -->
      </view>
      <!-- 页面顶部加u-modal -->
      <!-- 删除<u-modal>相关内容，无需在template中保留 -->
    </view>

    <!-- 🎯 自定义二维码弹窗（可选） -->
    <view
      v-if="showCustomQRCodeModal"
      class="qrcode-modal-overlay"
      @click="closeQRCodeModal"
    >
      <view class="qrcode-modal" @click.stop>
        <view class="qrcode-header">
          <text class="qrcode-title">请使用微信小程序</text>
          <view class="qrcode-close" @click="closeQRCodeModal">×</view>
        </view>
        <view class="qrcode-content">
          <!-- 二维码容器：固定高度，loading 和图片在同一位置切换 -->
          <view class="qrcode-container">
            <!-- Loading 转圈 -->
            <view v-if="qrcodeLoading" class="qrcode-loading">
              <view class="loading-spinner"></view>
            </view>

            <!-- 二维码图片 -->
            <image
              v-show="!qrcodeLoading && qrcodeImageUrl"
              :src="qrcodeImageUrl"
              class="qrcode-image"
              mode="aspectFit"
              @load="onQRCodeImageLoad"
              @error="onQRCodeImageError"
            />

            <!-- 加载失败时的重试按钮 -->
            <view v-if="!qrcodeLoading && !qrcodeImageUrl" class="qrcode-error">
              <text class="error-text">二维码加载失败</text>
              <button class="retry-btn" @click="retryQRCodeGeneration">
                重试
              </button>
            </view>
          </view>

          <text class="qrcode-tip"
            >请使用微信扫描上方二维码跳转到微信小程序遥控页面</text
          >
        </view>
        <view class="qrcode-buttons">
          <button class="qrcode-btn cancel" @click="saveQRCodeToAlbum">
            保存
          </button>
          <button class="qrcode-btn confirm" @click="closeQRCodeModal">
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 🎯 使用新架构的蓝牙模块
import toyCarBleUtil from "@/utils/ble/toyCarBleUnifiedNew.js"
// 🎯 导入蓝牙配置更新函数
import { updateConfigFromOrderData } from "@/utils/ble/toyCarBleConfig.js"

// 导入组件
import ControlPannel from "./components/ControlPannel.vue"
import SteeringWheel from "./components/SteeringWheel.vue"
import TurnControls from "./components/TurnControls.vue"
import FuelGauge from "./components/FuelGauge.vue"
import BaseNavbar from "../../components/base/BaseNavbar.vue"
export default {
  name: "RemoteControl",
  components: {
    ControlPanel: ControlPannel, // 🎯 修复组件名称映射
    SteeringWheel,
    TurnControls,
    FuelGauge,
    BaseNavbar,
  },

  data() {
    return {
      title: "遥控器",
      // 🎯 新架构：简化的状态管理
      buttonStates: {}, // 从新架构蓝牙模块获取的按钮状态
      activeBroadcasts: [], // 活跃的广播列表

      // 🎯 其他状态
      isStop: false, // 停止状态
      isPlay: false, // 播放状态
      isParkingActive: false, // 驻车状态
      isSwingActive: false, // 摇摆模式状态
      isCentralActive: false, // 中控开启状态
      steeringAngle: 0, // 方向盘角度

      // 🎯 初始化状态管理
      isInitializing: false, // 是否正在初始化

      // 🎯 方向盘相关数据
      wheelAngle: 0,
      startAngle: 0,
      startX: 0, // 触摸开始 X 坐标
      isTouching: false,

      // 🎯 状态更新定时器
      stateUpdateTimer: null,

      // 🎯 蓝牙连接状态
      bluetoothInitialized: false, // 蓝牙是否已初始化
      bBleConnected: false, // 蓝牙是否已连接

      // 🎯 数据管理
      allDataList: [], // 所有数据列表

      // 🎯 环境判断
      isDevelopment: true, // 是否为开发环境

      // 🎯 蓝牙状态监听（复制 toycar-new）
      bluetoothEnabled: false, // 蓝牙是否开启
      bluetoothStateChangeTimer: null, // 防抖定时器
      permissionCheckTimer: null, // 权限检查定时器
      bluetoothPollingTimer: null, // 蓝牙状态轮询定时器

      // 🎯 平台检测相关
      showCustomQRCodeModal: false, // 是否显示自定义二维码弹窗
      qrcodeImageUrl: "", // 二维码图片URL
      qrcodeLoading: false, // 二维码加载状态
      pageParams: {}, // 页面接收到的参数
      rawPageParams: {}, // 🎯 原始扫码参数（onLoad接收到的原始数据）
      testOrderSn: "", // 测试用的订单号

      // 🎯 时长数据（从服务器获取最新数据）
      totalTime: 0, // 总时长（秒）
      remainingTime: 0, // 剩余时长（秒）
      countdownTimer: null, // 倒计时定时器
      orderInfo: {}, // 订单信息

      // 🎯 当前操作状态
      currentActionStatus: "", // 当前操作状态

      // 🎯 控制状态
      isParkingBrakeEnabled: false, // 驻车制动是否启用
      isSwingEnabled: false, // 摇摆是否启用
      isRemoteOnlyEnabled: false, // 是否只接受遥控（true=只遥控，false=遥控和面板）

      // 🎯 新增：增强蓝牙和授权检测
      bluetoothOn: false, // 手机蓝牙是否打开
      wxBluetoothAuth: false, // 小程序蓝牙授权

      // 🎯 设备搜索状态
      deviceSearchStatus: {
        isSearching: false,
        deviceFound: false,
        isDeviceOnline: false,
        targetDeviceName: "TEMP",
      }, // 设备搜索状态

      // 🎯 自定义标题栏控制
      showCustomTitle: true, // 是否显示自定义标题栏
      isCheckingBluetooth: false,
      bold: true, // 导航栏文字加粗 // 防止多次弹窗
      // showModal: false,        // 删除
      // modalContent: '',        // 删除
    }
  },

  onLoad(options) {
    console.log("📱 页面加载 - 检查蓝牙状态")
    console.log("🔍 接收到的所有参数:", options)
    console.log("🔍 参数类型:", typeof options)
    console.log(
      "🔍 参数是否为空:",
      !options || Object.keys(options).length === 0
    )
    console.log("🔍 参数详细内容:", JSON.stringify(options, null, 2))

    // 🎯 详细分析每个参数
    if (options && Object.keys(options).length > 0) {
      Object.keys(options).forEach((key) => {
        console.log(`🔍 参数 ${key}:`, options[key])
        console.log(`🔍 参数 ${key} 类型:`, typeof options[key])

        // 尝试解码
        try {
          const decoded = decodeURIComponent(options[key])
          console.log(`🔍 参数 ${key} 解码后:`, decoded)
        } catch (e) {
          console.log(`🔍 参数 ${key} 解码失败:`, e.message)
        }
      })
    }

    console.log("🎯 onLoad 开始执行，准备启动轮询")

    // 🎯 保存原始参数
    this.rawPageParams = options || {}

    // 🎯 处理和保存页面参数
    this.pageParams = this.processPageParams(options || {})
    console.log(
      "🎯 处理后的页面参数:",
      JSON.stringify(this.pageParams, null, 2)
    )

    // 🎯 检测平台，如果是支付宝小程序则提示使用微信
    this.checkPlatformAndShowTip()

    // 🎯 先设置蓝牙状态监听器（无论蓝牙是否开启都要设置）
    this.startBluetoothStateListener()

    // 🎯 启动蓝牙状态轮询（用于iOS等不触发onShow的情况）
    this.startBluetoothPolling()

    // 🎯 先检测蓝牙适配器，再检测授权
    this.checkBluetoothEnabled().then((on) => {
      this.bluetoothOn = on
      this.checkWxBluetoothAuth().then((auth) => {
        this.wxBluetoothAuth = auth
      })
    })

    // 🎯 如果有 order_sn，直接调用接口获取订单信息（和 gaming 页面一样）
    if (options && options.order_sn) {
      console.log("🎯 接收到订单号:", options.order_sn)
      this.doGetUserOrderByNo(options.order_sn)
      return
    }

    // 🎯 启动倒计时
    if (this.remainingTime > 0) {
      this.startCountdown()
    }
    // 只在onLoad弹窗
    this.checkBluetoothAndAuth().then((ok) => {
      if (ok) {
        this.initNewArchitecture()
      }
    })
  },

  onReady() {
    // 🎯 立即隐藏系统导航栏，使用自定义 BaseNavbar
    setTimeout(() => {
      try {
        // 隐藏自定义导航栏
        this.showCustomTitle = false
        console.log("🎯 自定义导航栏已隐藏，进入全屏模式")
      } catch (error) {
        console.log("⚠️ 隐藏自定义导航栏失败:", error)
      }
    }, 2000)
  },

  onShow() {
    console.log("👁️ 页面显示 - 检查蓝牙状态并重新初始化")

    // 🎯 如果有订单信息，重新获取最新时间
    if (this.orderInfo && this.orderInfo.order_sn) {
      console.log("🎯 页面显示时重新获取订单信息")
      this.doGetUserOrderByNo(this.orderInfo.order_sn)
    }

    // 启动状态更新
    this.startStateUpdates()

    // 🎯 启动权限检查
    this.startPermissionCheck()

    // 🎯 每次页面显示都检查蓝牙状态并重新初始化
    console.log("🔍 onShow 开始检查蓝牙状态，当前连接状态:", this.bBleConnected)

    // 🎯 直接检查蓝牙授权状态
    wx.getSetting({
      success: (res) => {
        const bluetoothAuth = res.authSetting["scope.bluetooth"] === true
        console.log("🔍 onShow 蓝牙授权检查:", bluetoothAuth)

        if (!bluetoothAuth) {
          // 权限被拒绝
          this.bBleConnected = false
          this.bluetoothEnabled = false
          this.bluetoothOn = true // 假设蓝牙是开着的
          this.wxBluetoothAuth = false
          console.log("❌ 小程序蓝牙权限被拒绝")
          console.log("🎯 设置状态: bluetoothOn=true, wxBluetoothAuth=false")
        } else {
          // 有权限，先打开适配器再检查蓝牙状态
          wx.openBluetoothAdapter({
            success: () => {
              console.log("✅ onShow 蓝牙适配器打开成功")
              // 适配器打开成功，检查蓝牙状态
              wx.getBluetoothAdapterState({
                success: (bluetoothRes) => {
                  console.log("🔍 onShow 蓝牙状态检查:", bluetoothRes)

                  if (bluetoothRes.available) {
                    // 蓝牙正常
                    this.bBleConnected = false // 先设为false，初始化成功后再设为true
                    this.bluetoothEnabled = true
                    this.bluetoothOn = true
                    this.wxBluetoothAuth = true
                    console.log("✅ 蓝牙已开启，重新初始化服务")
                    // 🎯 每次onShow都重新初始化，确保服务可用
                    this.bluetoothInitialized = false // 强制重新初始化
                    this.initNewArchitecture()
                  } else {
                    // 蓝牙未开启
                    this.bBleConnected = false
                    this.bluetoothEnabled = false
                    this.bluetoothOn = false
                    this.wxBluetoothAuth = true // 权限有，但蓝牙没开
                    console.log("❌ 蓝牙未开启，设置为未连接")
                    this.bluetoothInitialized = false
                  }
                },
                fail: (stateError) => {
                  console.log("⚠️ onShow 获取蓝牙状态失败:", stateError)
                  // 获取状态失败，可能蓝牙未开启
                  this.bBleConnected = false
                  this.bluetoothEnabled = false
                  this.bluetoothOn = false
                  this.wxBluetoothAuth = true
                  console.log("❌ 获取蓝牙状态失败，可能蓝牙未开启")
                },
              })
            },
            fail: (error) => {
              console.log("⚠️ onShow 蓝牙适配器打开失败:", error)

              // 🎯 适配器打开失败，但不一定是蓝牙未开启
              // 可能是适配器已经被其他地方打开了，尝试直接检查状态
              wx.getBluetoothAdapterState({
                success: (bluetoothRes) => {
                  console.log(
                    "🔍 onShow 适配器失败后直接检查状态:",
                    bluetoothRes
                  )

                  if (bluetoothRes.available) {
                    // 蓝牙实际上是开启的
                    this.bBleConnected = false
                    this.bluetoothEnabled = true
                    this.bluetoothOn = true
                    this.wxBluetoothAuth = true
                    console.log("✅ 蓝牙实际已开启，重新初始化服务")
                    this.bluetoothInitialized = false
                    this.initNewArchitecture()
                  } else {
                    // 蓝牙确实未开启
                    this.bBleConnected = false
                    this.bluetoothEnabled = false
                    this.bluetoothOn = false
                    this.wxBluetoothAuth = true
                    console.log("❌ 蓝牙确实未开启")
                  }
                },
                fail: () => {
                  // 彻底失败，蓝牙未开启
                  this.bBleConnected = false
                  this.bluetoothEnabled = false
                  this.bluetoothOn = false
                  this.wxBluetoothAuth = true
                  console.log("❌ 蓝牙适配器打开失败，蓝牙未开启")
                },
              })
            },
          })
        }

        console.log("🔍 onShow 检查完成，最终状态:", {
          bluetoothOn: this.bluetoothOn,
          wxBluetoothAuth: this.wxBluetoothAuth,
          bBleConnected: this.bBleConnected,
        })
      },
      fail: () => {
        console.log("❌ 获取设置失败")
        this.bBleConnected = false
        this.wxBluetoothAuth = false
      },
    })
  },

  onHide() {
    // 🚨 页面隐藏时停止所有广播
    console.log("🚨 页面隐藏 - 停止所有广播")
    try {
      toyCarBleUtil.stopAllIndependentBroadcasts()
    } catch (error) {
      console.error("❌ 停止广播失败:", error)
    }
    this.stopStateUpdates()
    this.stopPermissionCheck()
  },

  onUnload() {
    // 🚨 紧急停止所有广播并关闭服务器
    console.log("🚨 页面卸载 - 停止所有广播并关闭服务器")
    try {
      // 使用onNoneLoad方法清理所有蓝牙资源
      toyCarBleUtil.onNoneLoad()

      // 清理蓝牙状态监听器
      this.stopBluetoothStateListener()

      // 停止蓝牙状态轮询
      this.stopBluetoothPolling()

      // 停止权限检查
      this.stopPermissionCheck()

      console.log("✅ 所有广播和服务器已关闭")
    } catch (error) {
      console.error("❌ 停止广播失败:", error)
    }

    // 页面卸载时清理所有资源
    this.stopStateUpdates()
    this.cleanupTimers()
  },

  computed: {
    // 🎯 限制显示的数据条数，提升页面性能
    displayDataList() {
      // 只显示最新的50条数据，减少DOM元素数量
      return this.allDataList.slice(0, 50)
    },

    // 🎯 新架构：计算属性获取按钮状态
    isForwardActive() {
      return this.buttonStates.forward || false
    },
    isBackActive() {
      return this.buttonStates.backward || false
    },
    isLeftTurnActive() {
      return this.buttonStates.left || false
    },
    isRightTurnActive() {
      return this.buttonStates.right || false
    },
  },

  methods: {
    // ===== 蓝牙状态检查 =====

    /**
     * 检查小程序蓝牙授权
     */
    async checkWxBluetoothAuth() {
      return new Promise((resolve) => {
        wx.getSetting({
          success: (res) => {
            const bluetoothAuth = res.authSetting["scope.bluetooth"]
            console.log("🔍 蓝牙授权状态:", bluetoothAuth)

            // 如果是 undefined，说明用户还没有被询问过权限，这种情况下认为是有权限的
            // 如果是 true，说明用户明确授权了
            // 如果是 false，说明用户明确拒绝了
            const hasAuth =
              bluetoothAuth === true || bluetoothAuth === undefined
            console.log("🔍 最终判断有权限:", hasAuth)
            resolve(hasAuth)
          },
          fail: (err) => {
            console.error("🔍 获取授权设置失败:", err)
            resolve(false)
          },
        })
      })
    },

    /**
     * 检查系统蓝牙是否打开
     */
    async checkBluetoothEnabled() {
      return new Promise((resolve) => {
        // 🎯 先尝试打开蓝牙适配器，再检查状态
        wx.openBluetoothAdapter({
          success: () => {
            // 适配器打开成功，检查蓝牙状态
            wx.getBluetoothAdapterState({
              success: (res) => {
                console.log("🔍 蓝牙状态检查成功:", res)
                resolve(res.available)
              },
              fail: (error) => {
                console.log("⚠️ 蓝牙状态检查失败:", error)
                resolve(false)
              },
            })
          },
          fail: (error) => {
            console.log("⚠️ 蓝牙适配器打开失败:", error)

            // 🎯 检查错误类型
            if (error.errno === 103 || error.errMsg.includes("auth deny")) {
              console.log("❌ 小程序蓝牙权限被拒绝")
              // 权限被拒绝，设置权限状态
              this.wxBluetoothAuth = false
              resolve("auth_denied") // 返回特殊值表示权限被拒绝
            } else {
              // 其他错误，延迟重试一次
              setTimeout(() => {
                wx.openBluetoothAdapter({
                  success: () => {
                    wx.getBluetoothAdapterState({
                      success: (res) => {
                        console.log("🔍 蓝牙状态检查成功(重试):", res)
                        resolve(res.available)
                      },
                      fail: () => resolve(false),
                    })
                  },
                  fail: (retryError) => {
                    console.log("⚠️ 蓝牙适配器重试失败:", retryError)
                    if (
                      retryError.errno === 103 ||
                      retryError.errMsg.includes("auth deny")
                    ) {
                      this.wxBluetoothAuth = false
                      resolve("auth_denied")
                    } else {
                      resolve(false)
                    }
                  },
                })
              }, 200)
            }
          },
        })
      })
    },

    /**
     * 总控方法
     */
    async checkBluetoothAndAuth() {
      if (this.isCheckingBluetooth) return false
      this.isCheckingBluetooth = true

      const bluetoothResult = await this.checkBluetoothEnabled()

      // 🎯 根据不同的结果设置状态和显示不同提示
      if (bluetoothResult === "auth_denied") {
        // 权限被拒绝
        this.bluetoothOn = false
        this.wxBluetoothAuth = false
        uni.showModal({
          title: "蓝牙权限提示",
          content: "请在小程序设置中开启蓝牙权限",
          showCancel: false,
          complete: () => {
            this.isCheckingBluetooth = false
          },
        })
        return
      } else if (!bluetoothResult) {
        // 蓝牙未开启
        this.bluetoothOn = false
        this.wxBluetoothAuth = true // 权限是有的，但蓝牙没开
        uni.showModal({
          title: "温馨提示",
          content: "请打开手机蓝牙和定位",
          showCancel: false,
          complete: () => {
            this.isCheckingBluetooth = false
          },
        })
        return false
      } else {
        // 蓝牙正常
        this.bluetoothOn = true
        this.wxBluetoothAuth = true
      }
      const wxBluetoothAuth = await this.checkWxBluetoothAuth()
      this.wxBluetoothAuth = wxBluetoothAuth
      if (!wxBluetoothAuth) {
        uni.showModal({
          title: "温馨提示",
          content: "小程序未授权蓝牙，请去授权",
          showCancel: false,
          complete: () => {
            this.isCheckingBluetooth = false
          },
        })
        return false
      }
      this.isCheckingBluetooth = false
      return true
    },

    // ===== 蓝牙状态监听（完全复制 toycar-new 的逻辑）=====

    /**
     * 监听蓝牙状态变化
     */
    startBluetoothStateListener() {
      // 监听蓝牙适配器状态变化
      try {
        console.log("🎯 启动蓝牙状态监听器")
        uni.onBluetoothAdapterStateChange((res) => {
          console.log("🔄 蓝牙状态变化:", res)

          // 🎯 防抖处理，避免频繁触发
          if (this.bluetoothStateChangeTimer) {
            clearTimeout(this.bluetoothStateChangeTimer)
          }

          this.bluetoothStateChangeTimer = setTimeout(() => {
            this.handleBluetoothStateChange(res)
          }, 500) // 500ms防抖
        })
        console.log("✅ 蓝牙状态监听器启动成功")
      } catch (error) {
        console.log("❌ 蓝牙状态监听启动失败:", error)
      }
    },

    /**
     * 处理蓝牙状态变化
     */
    handleBluetoothStateChange(res) {
      const wasEnabled = this.bluetoothEnabled
      this.bluetoothEnabled = res.available

      // 只在状态真正改变时处理
      if (wasEnabled === res.available) {
        console.log("🔄 蓝牙状态无变化，跳过处理")
        return
      }

      console.log("🔄 蓝牙状态变化，重新检查权限和状态")

      if (res.available) {
        console.log("✅ 蓝牙已打开，重新检查权限并初始化")
        // 🎯 蓝牙开启时，重新检查权限状态
        this.checkBluetoothEnabled().then((result) => {
          if (result === "auth_denied") {
            // 权限被拒绝
            this.bBleConnected = false
            this.bluetoothOn = true // 蓝牙是开着的
            this.wxBluetoothAuth = false // 但权限被拒绝
            console.log("❌ 蓝牙开启但权限被拒绝")
          } else if (result === true) {
            // 蓝牙和权限都正常，立即初始化
            this.bluetoothOn = true
            this.wxBluetoothAuth = true
            console.log("✅ 蓝牙和权限都正常，立即初始化服务")

            // 🎯 立即重新初始化蓝牙服务
            this.reinitializeBluetoothServices()
          }
        })
      } else {
        console.log("❌ 蓝牙已关闭，显示未连接")
        this.bBleConnected = false
        this.bluetoothOn = false
        this.bluetoothInitialized = false
        this.cleanupBluetoothState()
      }
    },

    /**
     * 重新初始化蓝牙服务
     */
    reinitializeBluetoothServices() {
      console.log("🔄 重新初始化蓝牙服务")

      // 重置初始化状态
      this.bluetoothInitialized = false

      // 延迟重新初始化，给蓝牙适配器准备时间
      setTimeout(async () => {
        try {
          await this.initNewArchitecture()
          console.log("✅ 蓝牙服务重新初始化完成")
          // 🎯 确保连接状态正确设置
          this.bBleConnected = true
        } catch (error) {
          console.error("❌ 蓝牙服务重新初始化失败:", error)
          this.bBleConnected = false
        }
      }, 1000)
    },

    /**
     * 清理蓝牙状态
     */
    cleanupBluetoothState() {
      console.log("🧹 清理蓝牙状态")

      this.bluetoothInitialized = false
      this.bBleConnected = false

      // 停止所有广播
      try {
        toyCarBleUtil.stopAllIndependentBroadcasts()
      } catch (error) {
        console.error("❌ 停止广播失败:", error)
      }

      this.addEventLog("蓝牙已断开")
    },

    /**
     * 停止蓝牙状态监听
     */
    stopBluetoothStateListener() {
      try {
        uni.offBluetoothAdapterStateChange()
        console.log("🛑 停止蓝牙状态监听")
      } catch (error) {
        console.log("❌ 停止蓝牙状态监听失败:", error)
      }
    },

    /**
     * 🎯 启动蓝牙状态轮询（用于iOS等不触发onShow的情况）
     */
    startBluetoothPolling() {
      console.log("🎯 启动蓝牙状态轮询检查")

      // 清除之前的定时器
      if (this.bluetoothPollingTimer) {
        clearInterval(this.bluetoothPollingTimer)
        console.log("🎯 清除之前的轮询定时器")
      }

      // 每10秒检查一次蓝牙状态（减少频率避免误判）
      this.bluetoothPollingTimer = setInterval(() => {
        console.log("🔄 执行蓝牙状态轮询检查")
        this.checkBluetoothStatusQuietly()
      }, 10000)

      console.log("✅ 蓝牙状态轮询定时器已启动，间隔5秒")
    },

    /**
     * 🎯 静默检查蓝牙状态（不打印太多日志）
     */
    async checkBluetoothStatusQuietly() {
      try {
        console.log("🔍 轮询检查蓝牙状态，当前连接状态:", this.bBleConnected)
        const result = await this.checkBluetoothEnabled()
        const wasConnected = this.bBleConnected

        console.log("🔍 轮询检查结果:", result, "之前连接状态:", wasConnected)

        if (result === true && !wasConnected) {
          // 蓝牙从未连接变为可用
          console.log("🔄 轮询检测到蓝牙已开启，重新初始化")
          this.bluetoothOn = true
          this.wxBluetoothAuth = true
          this.bluetoothInitialized = false
          this.initNewArchitecture()
        } else if (result !== true && wasConnected) {
          // 蓝牙从连接变为不可用
          console.log("🔄 轮询检测到蓝牙已关闭，更新状态")
          this.bBleConnected = false
          this.bluetoothOn = false
          this.bluetoothInitialized = false
        } else {
          console.log("🔍 轮询检查：蓝牙状态无变化")
        }
      } catch (error) {
        console.log("❌ 轮询检查蓝牙状态失败:", error)
      }
    },

    /**
     * 🎯 停止蓝牙状态轮询
     */
    stopBluetoothPolling() {
      if (this.bluetoothPollingTimer) {
        clearInterval(this.bluetoothPollingTimer)
        this.bluetoothPollingTimer = null
        console.log("🛑 停止蓝牙状态轮询")
      }
    },

    /**
     * 🎯 定期检查权限状态（用于检测用户在设置中修改权限）
     */
    startPermissionCheck() {
      // 每5秒检查一次权限状态
      this.permissionCheckTimer = setInterval(async () => {
        if (this.bluetoothOn) {
          // 只在蓝牙开启时检查权限
          const result = await this.checkBluetoothEnabled()

          if (result === "auth_denied" && this.wxBluetoothAuth) {
            // 权限从有变成无
            console.log("🔄 检测到权限被关闭")
            this.wxBluetoothAuth = false
            this.bBleConnected = false
          } else if (result === true && !this.wxBluetoothAuth) {
            // 权限从无变成有
            console.log("🔄 检测到权限被开启")
            this.wxBluetoothAuth = true
            this.bluetoothOn = true
            if (!this.bluetoothInitialized) {
              this.initNewArchitecture()
            }
          }
        }
      }, 5000)
    },

    /**
     * 停止权限检查
     */
    stopPermissionCheck() {
      if (this.permissionCheckTimer) {
        clearInterval(this.permissionCheckTimer)
        this.permissionCheckTimer = null
        console.log("🛑 停止权限检查")
      }
    },

    // ===== 新架构：蓝牙初始化和管理 =====

    // 🎯 完整的蓝牙初始化检查流程
    async initBluetoothWithFullCheck() {
      console.log("🔧 开始完整的蓝牙初始化检查流程")

      try {
        // 步骤1: 检查蓝牙是否开启
        const bluetoothState = await this.checkSystemBluetoothState()
        if (!bluetoothState.available) {
          console.log("❌ 系统蓝牙未开启")
          this.showBluetoothDisabledTip()
          return
        }

        // 步骤2: 开始正常的蓝牙初始化
        console.log("✅ 蓝牙状态检查通过，开始初始化")
        await this.initNewArchitecture()
      } catch (error) {
        console.error("❌ 蓝牙初始化检查失败:", error)
        this.showInitFailedTip(error.message)
      }
    },

    // 🎯 检查系统蓝牙状态
    async checkSystemBluetoothState() {
      return new Promise((resolve) => {
        wx.getBluetoothAdapterState({
          success: (res) => {
            console.log("🔍 系统蓝牙状态:", res)
            resolve({
              available: res.available,
              discovering: res.discovering,
            })
          },
          fail: (error) => {
            console.log("⚠️ 获取蓝牙状态失败，可能需要先初始化:", error)
            resolve({ available: false })
          },
        })
      })
    },

    // 🎯 显示蓝牙未开启提示
    showBluetoothDisabledTip() {
      uni.showModal({
        title: "蓝牙未开启",
        content: "请在设备设置中开启蓝牙功能，然后重新进入页面",
        showCancel: true,
        confirmText: "重试",
        cancelText: "返回",
        success: (res) => {
          if (res.confirm) {
            setTimeout(() => {
              this.initBluetoothWithFullCheck()
            }, 1000)
          } else {
            uni.navigateBack()
          }
        },
      })
    },

    // 🎯 显示初始化失败提示
    showInitFailedTip(errorMessage) {
      uni.showModal({
        title: "蓝牙初始化失败",
        content: `初始化失败: ${errorMessage}\n请检查蓝牙设置后重试`,
        showCancel: true,
        confirmText: "重试",
        cancelText: "返回",
        success: (res) => {
          if (res.confirm) {
            setTimeout(() => {
              this.initBluetoothWithFullCheck()
            }, 1000)
          } else {
            uni.navigateBack()
          }
        },
      })
    },

    async initNewArchitecture(retryCount = 0) {
      if (this.isInitializing || this.bluetoothInitialized) {
        console.log("🔄 已在初始化或已初始化，跳过")
        return
      }

      console.log(
        "🎯 开始新架构初始化",
        retryCount > 0 ? `(重试 ${retryCount})` : ""
      )
      this.isInitializing = true

      try {
        // 设置回调函数
        this.setupCallbacks()

        // 初始化蓝牙（包含状态检查、适配器初始化、服务器预创建）
        await toyCarBleUtil.initBluetooth()

        this.bluetoothInitialized = true
        // 🎯 初始化成功后设置连接状态
        this.bBleConnected = true
        this.isInitializing = false // 🔥 重要：初始化成功后设置为false

        console.log("✅ 新架构初始化完成，设置连接状态为已连接")
        this.addEventLog("新架构蓝牙初始化完成")

        // 启动状态更新
        this.startStateUpdates()

        // 🎯 初始化完成后，检查设备是否在线
        setTimeout(async () => {
          await this.checkInitialDeviceStatus()
        }, 3000) // 等待3秒让设备开始广播
      } catch (error) {
        console.error("❌ 新架构初始化失败:", error)
        this.bBleConnected = false
        this.bluetoothInitialized = false

        // 最多重试3次
        if (retryCount < 3) {
          console.log(`🔄 将在2秒后重试初始化 (${retryCount + 1}/3)`)
          setTimeout(() => {
            this.isInitializing = false
            this.initNewArchitecture(retryCount + 1)
          }, 2000)
        } else {
          // 🎯 不显示失败提示，静默处理
          console.log("❌ 蓝牙初始化多次重试失败，静默处理")
          this.isInitializing = false
        }
      } finally {
        if (retryCount >= 3 || this.bluetoothInitialized) {
          this.isInitializing = false
        }
      }
    },

    /**
     * 🎯 新架构：设置回调函数
     */
    setupCallbacks() {
      // 设置数据接收回调
      toyCarBleUtil.setDataCallback((data) => {
        console.log("📱 页面接收到数据:", data)

        if (data === "CLEAR_DATA") {
          this.allDataList = []
          console.log("🧹 清空数据列表")
          return
        }

        const receiveItem = {
          ...data,
          type: "receive",
          meaning: this.interpretCommand(data.data),
        }

        console.log("📝 处理后的接收数据:", receiveItem)

        if (this.isDevelopment) {
          this.allDataList.unshift(receiveItem)
          if (this.allDataList.length > 100) {
            this.allDataList = this.allDataList.slice(0, 100)
          }
          // console.log(
          //   "✅ 数据已添加到列表，当前列表长度:",
          //   this.allDataList.length
          // )
        } else {
          console.log("⚠️ 非开发模式，数据未添加到列表")
        }
      })

      // 设置发送数据回调
      toyCarBleUtil.setSendDataCallback(
        (hexCommand, commandType, options = {}) => {
          const sendItem = {
            time:
              new Date().toLocaleTimeString() +
              " " +
              new Date().getMilliseconds(),
            data: hexCommand,
            type: "send",
            meaning: this.interpretCommand(hexCommand),
            broadcastStatus: options.broadcastStatus || "success",
            broadcastMessage: options.broadcastMessage || "",
          }

          if (this.isDevelopment) {
            this.allDataList.unshift(sendItem)
            if (this.allDataList.length > 100) {
              this.allDataList = this.allDataList.slice(0, 100)
            }
          }
        }
      )

      // 🎯 不再使用工具类的连接状态回调，直接在页面管理
      // toyCarBleUtil.setConnectionCallback() - 已删除

      // 🎯 启动蓝牙状态监听（完全复制 toycar-new 的逻辑）
      this.startBluetoothStateListener()

      // 设置错误回调
      toyCarBleUtil.setErrorCallback((error) => {
        console.error("🚨 蓝牙错误:", error)
        uni.showModal({
          title: "蓝牙错误",
          content: error,
          showCancel: false,
        })
      })
    },

    /**
     * 🎯 新架构：启动状态更新
     */
    startStateUpdates() {
      if (this.stateUpdateTimer) {
        clearInterval(this.stateUpdateTimer)
      }

      // 每100ms更新一次状态
      this.stateUpdateTimer = setInterval(() => {
        this.updateStates()
      }, 100)
    },

    /**
     * 🎯 新架构：停止状态更新
     */
    stopStateUpdates() {
      if (this.stateUpdateTimer) {
        clearInterval(this.stateUpdateTimer)
        this.stateUpdateTimer = null
      }
    },

    /**
     * 🎯 清理所有定时器
     */
    cleanupTimers() {
      // 清理状态更新定时器
      this.stopStateUpdates()

      // 🎯 清理倒计时定时器
      this.stopCountdown()

      // 清理动画定时器
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }

      console.log("🧹 所有定时器已清理")
    },

    /**
     * 🎯 新架构：更新状态
     */
    updateStates() {
      try {
        // 从新架构蓝牙模块获取状态
        this.buttonStates = toyCarBleUtil.getButtonStates()
        this.activeBroadcasts = toyCarBleUtil.getActiveBroadcasts()

        // 更新驻车状态
        this.isParkingActive = this.buttonStates.parking || false

        // 🎯 更新设备搜索状态
        this.deviceSearchStatus = toyCarBleUtil.getDeviceSearchStatus()
      } catch (error) {
        console.error("❌ 更新状态失败:", error)
        // 如果获取状态失败，停止状态更新定时器
        this.stopStateUpdates()
      }
    },

    // 🎯 新架构：清空数据
    clearData() {
      toyCarBleUtil.clickClearResult()
    },

    // 🎯 切换开发模式
    toggleDevelopmentMode() {
      this.isDevelopment = !this.isDevelopment
      console.log("🔧 开发模式:", this.isDevelopment ? "开启" : "关闭")
    },

    // 🎯 处理档位变化（包含限制检查）
    handleGearChange(gear) {
      console.log(`🎯 档位变化请求: ${gear}`)

      // 🎯 检查驻车和摇摆模式限制
      if (this.isParkingBrakeEnabled) {
        uni.showModal({
          title: "提示",
          content: "请先关闭驻车模式，再进行该操作",
          showCancel: false,
          confirmText: "确定",
        })
        // 🎯 阻止档位变化，恢复到当前档位
        this.$refs.steeringWheel?.resetGear()
        return
      }

      if (this.isSwingEnabled) {
        uni.showModal({
          title: "提示",
          content: "请先关闭摇摆模式，再进行该操作",
          showCancel: false,
          confirmText: "确定",
        })
        // 🎯 阻止档位变化，恢复到当前档位
        this.$refs.steeringWheel?.resetGear()
        return
      }

      // 🎯 没有限制，正常处理档位变化
      this.sendCommand("gear", gear)
    },

    // 🎯 检测平台并显示提示
    async checkPlatformAndShowTip() {
      // 检测当前运行平台
      // #ifdef MP-ALIPAY
      console.log("🎯 检测到支付宝小程序，生成微信小程序二维码")
      await this.generateWechatQRCodeForAlipay()
      // #endif

      // #ifdef MP-WEIXIN
      console.log("🎯 检测到微信小程序，正常运行")
      // #endif

      // #ifdef H5
      console.log("🎯 检测到H5环境，显示提示信息")
      this.showH5PlatformTip()
      // #endif
    },

    // 🎯 关闭二维码弹窗
    closeQRCodeModal() {
      this.showCustomQRCodeModal = false
      // 可以选择返回上一页
      uni.navigateBack()
    },

    // 🎯 为支付宝小程序生成微信小程序二维码
    async generateWechatQRCodeForAlipay() {
      try {
        console.log("🎯 开始为支付宝小程序生成微信小程序二维码")

        // 使用页面保存的参数
        const options = this.pageParams || {}

        console.log("🎯 当前页面参数:", options)
        console.log("🎯 页面参数详情:", JSON.stringify(options, null, 2))

        // 构建微信小程序跳转链接
        let qrcodeUrl = "https://ppj.handaiwulian.com/mini/customer/romote"

        // 添加参数，特别是 order_sn
        const params = []
        if (options.order_sn) {
          params.push(`order_sn=${encodeURIComponent(options.order_sn)}`)
          console.log("🎯 添加订单号参数:", options.order_sn)
        } else {
          console.log("⚠️ 没有找到订单号参数")
        }

        // 添加其他参数
        Object.keys(options).forEach((key) => {
          if (key !== "order_sn" && key !== "__ob__") {
            params.push(`${key}=${encodeURIComponent(options[key])}`)
            console.log("🎯 添加其他参数:", key, "=", options[key])
          }
        })

        if (params.length > 0) {
          qrcodeUrl += `?${params.join("&")}`
        }

        console.log("🎯 生成的二维码链接:", qrcodeUrl)

        // 显示自定义二维码弹窗
        this.showCustomQRCodeModal = true

        // 开始加载状态
        this.qrcodeLoading = true

        // 使用草料二维码 API 生成二维码
        console.log("🎯 使用草料二维码 API 生成二维码")
        this.generateQRCodeWithCaoLiao(qrcodeUrl)
      } catch (error) {
        console.error("🎯 生成二维码失败:", error)
        this.showSimpleTextModal()
      }
    },

    // 🎯 保存二维码到相册
    async saveQRCodeToAlbum() {
      if (!this.qrcodeImageUrl) {
        uni.showToast({
          title: "二维码还未生成",
          icon: "none",
        })
        return
      }

      if (this.qrcodeLoading) {
        uni.showToast({
          title: "二维码生成中，请稍候",
          icon: "none",
        })
        return
      }

      try {
        console.log("🎯 开始保存二维码到相册")

        // 显示加载提示
        uni.showLoading({
          title: "保存中...",
        })

        // 先下载图片到本地
        const downloadResult = await new Promise((resolve, reject) => {
          uni.downloadFile({
            url: this.qrcodeImageUrl,
            success: (res) => {
              console.log("🎯 图片下载成功:", res.tempFilePath)
              resolve(res)
            },
            fail: (err) => {
              console.error("🎯 图片下载失败:", err)
              reject(err)
            },
          })
        })

        // 保存到相册
        await new Promise((resolve, reject) => {
          uni.saveImageToPhotosAlbum({
            filePath: downloadResult.tempFilePath,
            success: () => {
              console.log("🎯 保存到相册成功")
              resolve()
            },
            fail: (err) => {
              console.error("🎯 保存到相册失败:", err)
              reject(err)
            },
          })
        })

        uni.hideLoading()
        uni.showToast({
          title: "保存成功",
          icon: "success",
        })
      } catch (error) {
        uni.hideLoading()
        console.error("🎯 保存二维码失败:", error)

        // 根据错误类型显示不同提示
        if (error.errMsg && error.errMsg.includes("auth")) {
          uni.showModal({
            title: "需要授权",
            content: "保存图片需要访问相册权限，请在设置中开启",
            showCancel: false,
          })
        } else {
          uni.showToast({
            title: "保存失败",
            icon: "none",
          })
        }
      }
    },

    // 🎯 二维码图片加载完成
    onQRCodeImageLoad() {
      console.log("🎯 二维码图片加载完成")
      this.qrcodeLoading = false
    },

    // 🎯 二维码图片加载失败
    onQRCodeImageError(e) {
      console.log("🎯 二维码图片加载失败")
      console.log("🎯 错误详情:", e)
      console.log("🎯 当前图片URL:", this.qrcodeImageUrl)

      this.qrcodeLoading = false

      // 尝试使用备用的二维码 API
      const originalUrl = this.qrcodeImageUrl
      const urlMatch = originalUrl.match(/text=([^&]+)/)

      if (urlMatch) {
        const decodedUrl = decodeURIComponent(urlMatch[1])
        console.log("🎯 尝试备用二维码 API，原始URL:", decodedUrl)
        // 备用 API 列表
        const backupApis = [
          `https://api.qrserver.com/v1/create-qr-code/?size=400x400&data=${encodeURIComponent(
            decodedUrl
          )}`,
          `https://chart.googleapis.com/chart?chs=400x400&cht=qr&chl=${encodeURIComponent(
            decodedUrl
          )}`,
          `https://qr-server.com/api/v1/create-qr-code/?size=400x400&data=${encodeURIComponent(
            decodedUrl
          )}`,
        ]

        // 随机选择一个备用 API
        const randomIndex = Math.floor(Math.random() * backupApis.length)
        const backupApiUrl = backupApis[randomIndex]

        console.log("🎯 使用备用 API:", backupApiUrl)
        this.qrcodeImageUrl = backupApiUrl
        return
      }

      uni.showToast({
        title: "二维码显示失败",
        icon: "none",
      })
    },

    // 🎯 使用草料二维码 API 生成二维码
    generateQRCodeWithCaoLiao(url) {
      console.log("🎯 开始使用草料二维码 API:", url)

      // 草料二维码 API 地址
      const apiUrl = `https://api.2dcode.biz/v1/create-qr-code?data=${encodeURIComponent(
        url
      )}&size=400x400`

      console.log("🎯 草料二维码 API URL:", apiUrl)

      // 草料 API 直接返回图片，可以作为 img src 使用
      this.qrcodeImageUrl = apiUrl
      this.qrcodeLoading = false

      console.log("🎯 草料二维码设置完成")

      // 支付宝小程序兼容的成功提示
      // #ifdef MP-ALIPAY
      uni.showToast({
        title: "二维码生成成功",
        icon: "success",
      })
      // #endif
    },

    // 🎯 获取订单信息显示内容
    getOrderInfoDisplay() {
      try {
        if (!this.orderInfo) {
          return "暂无订单信息"
        }

        const orderDisplay = {
          order_sn: this.orderInfo.order_sn || "无",
          device_sn: this.orderInfo.device_sn || "无",
          machine_type: this.orderInfo.machine_type || "无",
          start_time: this.orderInfo.start_time
            ? new Date(this.orderInfo.start_time * 1000).toLocaleString()
            : "无",
          end_time: this.orderInfo.end_time
            ? new Date(this.orderInfo.end_time * 1000).toLocaleString()
            : "无",
          length_time: this.orderInfo.length_time || "无",
          status: this.orderInfo.status || "无",
        }

        return JSON.stringify(orderDisplay, null, 2)
      } catch (error) {
        return "订单信息解析失败"
      }
    },

    // 🎯 获取API状态显示内容
    getApiStatusDisplay() {
      try {
        const status = {
          是否调用了获取订单API: this.orderInfo ? "是" : "否",
          订单号: this.pageParams?.order_sn || "无",
          API调用时间: this.apiCallTime || "未调用",
          页面参数数量: Object.keys(this.pageParams || {}).length,
          是否检测到支付宝: this.isAlipay ? "是" : "否",
          最后更新时间: new Date().toLocaleString(),
        }

        return JSON.stringify(status, null, 2)
      } catch (error) {
        return "API状态解析失败"
      }
    },

    // 🎯 格式化时间显示（秒转换为 HH:MM:SS 格式）
    formatTime(seconds) {
      try {
        if (!seconds || seconds <= 0) {
          return "00:00:00"
        }

        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        const secs = seconds % 60

        return `${hours.toString().padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
      } catch (error) {
        return "00:00:00"
      }
    },

    // 🎯 处理页面参数（解码和格式化）
    processPageParams(rawParams) {
      console.log("🔧 开始处理页面参数:", rawParams)

      let processedParams = {}

      // 🎯 特殊处理：如果有 q 参数，说明是微信扫码跳转
      if (rawParams.q) {
        console.log("🔧 检测到微信扫码跳转，q 参数:", rawParams.q)

        try {
          // 解码 q 参数中的 URL
          const decodedUrl = decodeURIComponent(rawParams.q)
          console.log("🔧 解码后的 URL:", decodedUrl)

          // 手动解析URL参数（小程序兼容）
          console.log("🔧 开始手动解析URL参数")

          // 找到 ? 的位置
          const questionMarkIndex = decodedUrl.indexOf("?")
          if (questionMarkIndex !== -1) {
            const queryString = decodedUrl.substring(questionMarkIndex + 1)
            console.log("🔧 查询字符串:", queryString)

            // 分割参数
            const paramPairs = queryString.split("&")
            console.log("🔧 参数对数组:", paramPairs)

            paramPairs.forEach((pair) => {
              const equalIndex = pair.indexOf("=")
              if (equalIndex !== -1) {
                const key = pair.substring(0, equalIndex)
                const value = pair.substring(equalIndex + 1)
                const decodedValue = decodeURIComponent(value)
                console.log(`🔧 解析参数 ${key}:`, decodedValue)
                processedParams[key] = decodedValue
              }
            })
          } else {
            console.log("🔧 URL中没有找到查询参数")
          }

          // 保留原始的扫码信息
          processedParams.scancode_time = rawParams.scancode_time
          processedParams.original_q = rawParams.q
        } catch (e) {
          console.log("🔧 解析 q 参数失败:", e.message)
          // 如果解析失败，使用原始参数
          processedParams = { ...rawParams }
        }
      } else {
        // 🎯 普通参数处理
        Object.keys(rawParams).forEach((key) => {
          let value = rawParams[key]

          // 尝试解码参数值
          try {
            const decoded = decodeURIComponent(value)
            console.log(`🔧 参数 ${key}: "${value}" → "${decoded}"`)
            processedParams[key] = decoded
          } catch (e) {
            console.log(`🔧 参数 ${key} 无需解码:`, value)
            processedParams[key] = value
          }
        })
      }

      // 🎯 验证订单号参数
      if (processedParams.order_sn) {
        console.log("✅ 找到订单号参数:", processedParams.order_sn)
      } else {
        console.log("⚠️ 没有找到 order_sn 参数")

        // 检查是否有其他可能的订单号参数名
        const possibleOrderKeys = ["orderSn", "orderId", "order_id", "sn"]
        possibleOrderKeys.forEach((possibleKey) => {
          if (processedParams[possibleKey]) {
            console.log(
              `🔧 发现可能的订单号参数 ${possibleKey}:`,
              processedParams[possibleKey]
            )
            // 统一转换为 order_sn
            processedParams.order_sn = processedParams[possibleKey]
          }
        })
      }

      return processedParams
    },

    // 🎯 测试获取订单数据
    testGetOrderData() {
      console.log("🧪 ========== 手动测试获取订单数据 ==========")
      console.log("🧪 测试开始时间:", new Date().toLocaleString())

      if (!this.testOrderSn) {
        console.log("🧪 ❌ 测试失败：没有输入订单号")
        uni.showToast({
          title: "请输入订单号",
          icon: "none",
        })
        return
      }

      console.log("🧪 📝 输入的订单号:", this.testOrderSn)
      console.log("🧪 📝 订单号类型:", typeof this.testOrderSn)
      console.log("🧪 📝 订单号长度:", this.testOrderSn.length)

      // 手动设置页面参数
      const testParams = { order_sn: this.testOrderSn }
      this.pageParams = testParams

      console.log("🧪 📝 设置的页面参数:", JSON.stringify(testParams, null, 2))
      console.log(
        "🧪 📝 当前页面参数状态:",
        JSON.stringify(this.pageParams, null, 2)
      )

      // 记录API调用前的状态
      console.log("🧪 📝 API调用前状态:")
      console.log("🧪   - 当前订单信息:", this.orderInfo)
      console.log("🧪   - 当前总时长:", this.totalTime)
      console.log("🧪   - 当前剩余时长:", this.remainingTime)

      // 调用获取订单数据
      console.log("🧪 🚀 开始调用 doGetUserOrderByNo API")
      console.log("🧪 🚀 传递的参数:", this.testOrderSn)

      this.doGetUserOrderByNo(this.testOrderSn)

      console.log("🧪 ✅ API调用已发起")
      console.log("🧪 ========== 手动测试日志结束 ==========")

      uni.showToast({
        title: "开始测试获取订单数据",
        icon: "none",
      })
    },

    // 🎯 扫描二维码测试
    scanQRCode() {
      console.log("📷 ========== 扫描二维码测试 ==========")
      console.log("📷 开始扫描二维码")

      uni.scanCode({
        success: (res) => {
          console.log("📷 扫描成功:", res)
          console.log("📷 扫描结果:", res.result)
          console.log("📷 扫描类型:", res.scanType)

          // 显示扫描结果
          uni.showModal({
            title: "扫描结果",
            content: `扫描到的内容：\n${res.result}`,
            showCancel: true,
            cancelText: "关闭",
            confirmText: "模拟跳转",
            success: (modalRes) => {
              if (modalRes.confirm) {
                console.log("📷 用户选择模拟跳转")
                this.simulateQRCodeJump(res.result)
              } else {
                console.log("📷 用户取消模拟跳转")
              }
            },
          })
        },
        fail: (err) => {
          console.error("📷 扫描失败:", err)
          uni.showToast({
            title: "扫描失败",
            icon: "none",
          })
        },
      })
    },

    // 🎯 模拟二维码跳转
    simulateQRCodeJump(qrContent) {
      console.log("🔄 ========== 模拟二维码跳转 ==========")
      console.log("🔄 二维码内容:", qrContent)

      try {
        // 模拟微信扫码跳转的参数格式
        const mockParams = {
          scancode_time: Math.floor(Date.now() / 1000).toString(),
          q: encodeURIComponent(qrContent),
        }

        console.log("🔄 模拟的扫码参数:", mockParams)

        // 重新处理参数（模拟onLoad过程）
        this.pageParams = this.processPageParams(mockParams)

        console.log("🔄 模拟跳转后的处理结果:", this.pageParams)

        // 如果有订单号，自动调用API
        if (this.pageParams.order_sn) {
          console.log(
            "🔄 ✅ 模拟跳转成功，找到订单号:",
            this.pageParams.order_sn
          )
          console.log("🔄 🚀 开始获取订单数据")
          this.doGetUserOrderByNo(this.pageParams.order_sn)
        } else {
          console.log("🔄 ❌ 模拟跳转后没有找到订单号")
          console.log(
            "🔄 处理后的参数:",
            JSON.stringify(this.pageParams, null, 2)
          )
        }

        uni.showToast({
          title: "模拟跳转完成",
          icon: "success",
        })

        console.log("🔄 ========== 模拟跳转完成 ==========")
      } catch (error) {
        console.error("🔄 ❌ 模拟跳转失败:", error)
        uni.showToast({
          title: "模拟跳转失败",
          icon: "none",
        })
      }
    },

    // 🎯 重试二维码生成
    retryQRCodeGeneration() {
      console.log("🔄 用户手动重试二维码生成")

      // 重置状态
      this.qrcodeLoading = true

      // 重新生成二维码URL
      const options = this.pageParams || {}
      let qrcodeUrl = "https://ppj.handaiwulian.com/mini/customer/romote"

      if (options.order_sn) {
        qrcodeUrl += `?order_sn=${options.order_sn}`
      }

      // 使用草料二维码 API 重新生成
      this.generateQRCodeWithCaoLiao(qrcodeUrl)
    },

    // 🎯 显示简单文字提示（备用方案）
    showSimpleTextModal() {
      uni.showModal({
        title: "平台提示",
        content: "当前功能仅支持微信小程序，请使用微信小程序访问",
        showCancel: true,
        cancelText: "返回",
        confirmText: "知道了",
        success: (res) => {
          if (res.cancel) {
            uni.navigateBack()
          }
        },
      })
    },

    // 🎯 显示蓝牙权限弹窗
    showBluetoothPermissionModal() {
      uni.showModal({
        title: "提示",
        content: "请打开小程序蓝牙权限",
        showCancel: true,
        cancelText: "取消",
        confirmText: "去设置",
        success: (res) => {
          if (res.confirm) {
            // 打开小程序设置页面
            wx.openSetting({
              success: (settingRes) => {
                console.log("🎯 设置页面返回:", settingRes)
                if (settingRes.authSetting["scope.bluetooth"]) {
                  uni.showToast({
                    title: "权限已开启",
                    icon: "success",
                    duration: 2000,
                  })
                  // 延迟重新检测
                  setTimeout(() => {
                    this.handleReconnectBluetooth()
                  }, 2000)
                }
              },
              fail: (error) => {
                console.error("🎯 打开设置页面失败:", error)
                // uni.showToast({
                //   title: "打开设置失败",
                //   icon: "none",
                // })
              },
            })
          }
        },
      })
    },

    // 🎯 显示蓝牙开启弹窗
    showBluetoothEnableModal() {
      uni.showModal({
        title: "提示",
        content: "请打开手机蓝牙和定位",
        showCancel: true,
        cancelText: "取消",
        confirmText: "确定",
      })
    },

    // 🎯 显示重新连接弹窗（已连接状态）
    showReconnectModal() {
      this.initNewArchitecture()
    },

    // 🎯 显示连接弹窗（未连接状态）
    showConnectModal() {
      uni.showModal({
        title: "提示",
        content: "蓝牙已开启但未连接，是否连接？",
        showCancel: true,
        cancelText: "取消",
        confirmText: "连接",
        success: (res) => {
          if (res.confirm) {
            console.log("🎯 用户选择连接")
            this.initNewArchitecture()
          }
        },
      })
    },

    // 🎯 清理蓝牙连接
    cleanupBluetooth() {
      console.log("🧹 清理现有蓝牙连接")
      try {
        // 停止所有广播和服务器
        toyCarBleUtil.cleanup()
        // 重置连接状态
        this.bBleConnected = false
        this.bluetoothInitialized = false
        console.log("✅ 蓝牙连接清理完成")
      } catch (error) {
        console.error("❌ 清理蓝牙连接失败:", error)
      }
    },

    // ===== 🎯 新架构：核心控制方法 =====

    /**
     * 🎯 新架构：发送指令
     * @param {string} command - 指令类型
     * @param {*} value - 指令参数（可选）
     */
    async sendCommand(command, value) {
      console.log(`🎯 发送指令: ${command}`, value)

      // 🎯 先检查设备是否在线（除了非关键指令）
      const nonCriticalCommands = ["speed", "gear"] // 非关键指令，不需要检查设备状态
      if (!nonCriticalCommands.includes(command)) {
        const deviceOnline = await this.checkDeviceBeforeCommand()
        if (!deviceOnline) {
          console.log(`❌ 设备离线，取消发送指令: ${command}`)
          return
        }
      }

      // 🎯 检查驻车和摇摆模式限制
      if (this.isParkingBrakeEnabled && command !== "parking") {
        uni.showModal({
          title: "提示",
          content: "请先关闭驻车模式，再进行该操作",
          showCancel: false,
          confirmText: "确定",
        })
        return
      }

      if (this.isSwingEnabled && command !== "swing") {
        uni.showModal({
          title: "提示",
          content: "请先关闭摇摆模式，再进行该操作",
          showCancel: false,
          confirmText: "确定",
        })
        return
      }

      // 🔥 如果正在初始化，暂时忽略指令，避免弹出错误提示
      if (this.isInitializing) {
        console.log("⏳ 蓝牙正在初始化中，暂时忽略指令")
        return
      }

      // 🔥 如果未初始化，先检查蓝牙是否开启
      if (!this.bluetoothInitialized) {
        console.log("⚠️ 蓝牙未初始化，检查蓝牙状态...")

        // 先检查蓝牙和授权（只弹一次modal）
        const ok = await this.checkBluetoothAndAuth()
        if (!ok) {
          // checkBluetoothAndAuth 已弹窗，这里不再弹
          return
        }
        // 蓝牙开启且授权了才初始化
        try {
          await this.initNewArchitecture()
          // 等待一下确保初始化完成
          await new Promise((resolve) => setTimeout(resolve, 500))
        } catch (error) {
          console.error("❌ 蓝牙初始化失败:", error)
          // 静默失败，不弹Toast/Modal
          return
        }
      }

      try {
        // 🎯 统一处理指令发送和UI状态更新
        switch (command) {
          case "forward":
            this.buttonStates.forward = true
            await toyCarBleUtil.sendForward()
            this.currentActionStatus = "正在前进，松开停止前进"
            break
          case "backward":
            this.buttonStates.backward = true
            await toyCarBleUtil.sendBackward()
            this.currentActionStatus = "正在后退，松开停止后退"
            break
          case "left":
            this.isLeftTurnActive = true
            await toyCarBleUtil.sendLeft()
            this.currentActionStatus = "正在左转，松开停止左转"
            break
          case "right":
            this.isRightTurnActive = true
            await toyCarBleUtil.sendRight()
            this.currentActionStatus = "正在右转，松开停止右转"
            break
          case "brake":
            this.isStop = true
            await toyCarBleUtil.sendBrake()
            break
          case "parking":
            // 🎯 先发送指令，然后更新状态
            await toyCarBleUtil.sendParking() // 切换状态，不需要参数
            // 更新本地状态（与蓝牙工具类状态同步）
            this.isParkingBrakeEnabled = !this.isParkingBrakeEnabled
            this.isParkingActive = this.isParkingBrakeEnabled // 同步UI状态
            console.log(
              "🎯 驻车状态更新:",
              this.isParkingBrakeEnabled ? "启用" : "禁用"
            )
            break
          case "swing":
            // 🎯 切换摇摆模式状态（像驻车一样）
            this.isSwingEnabled = !this.isSwingEnabled
            this.isSwingActive = this.isSwingEnabled // 同步UI状态
            console.log(
              "🎯 摇摆模式切换:",
              this.isSwingEnabled ? "开启" : "关闭"
            )
            await toyCarBleUtil.sendSwing(this.isSwingEnabled ? 1 : 0)
            break
          case "central":
            // 🎯 切换中控状态（静默切换，不弹窗）
            this.isRemoteOnlyEnabled = !this.isRemoteOnlyEnabled
            this.isCentralActive = this.isRemoteOnlyEnabled // 同步UI状态
            console.log(
              "🎯 中控模式切换:",
              this.isRemoteOnlyEnabled ? "只遥控" : "遥控和面板"
            )
            await toyCarBleUtil.sendRemoteControl(
              this.isRemoteOnlyEnabled ? 0 : 1
            )
            break
          case "gear":
            await toyCarBleUtil.sendGear(value)
            break
          case "speed":
            await toyCarBleUtil.sendCustomSpeed(value)
            break
          default:
            console.warn(`🚨 未知指令: ${command}`)
            this.addEventLog(`未知指令: ${command}`)
            return
        }

        this.addEventLog(
          `指令发送: ${this.getCommandName(command)} ${value || ""}`
        )
      } catch (error) {
        console.error(`❌ 指令发送失败: ${command}`, error)
        this.addEventLog(`指令发送失败: ${command} - ${error.message}`)
      }
    },

    /**
     * 🎯 新架构：按钮释放处理
     * @param {string} buttonType - 按钮类型
     */
    async handleButtonRelease(buttonType) {
      console.log(`🎯 按钮释放: ${buttonType}`)

      try {
        // 🎯 统一处理指令停止和UI状态重置
        switch (buttonType) {
          case "forward":
            this.buttonStates.forward = false
            await toyCarBleUtil.stopForward()
            this.currentActionStatus = ""
            break
          case "backward":
          case "back":
            this.buttonStates.backward = false
            await toyCarBleUtil.stopBackward()
            this.currentActionStatus = ""
            break
          case "left":
            this.isLeftTurnActive = false
            await toyCarBleUtil.stopLeft()
            this.currentActionStatus = ""
            break
          case "right":
            this.isRightTurnActive = false
            await toyCarBleUtil.stopRight()
            this.currentActionStatus = ""
            break
          case "brake":
            this.isStop = false
            await toyCarBleUtil.stopBrake()
            break
          case "parking":
            // 驻车是切换状态，不需要停止
            console.log("🎯 驻车释放 - 无需操作")
            break
          case "steer":
            // 方向盘释放，回中
            this.wheelAngle = 0
            this.steeringAngle = 0
            break
          case "swing":
            // 🎯 摇摆模式是切换状态，释放时无需操作
            console.log("🎯 摇摆模式释放 - 无需操作（切换模式）")
            break
          case "central":
            // 🎯 中控是切换状态，释放时无需操作
            console.log("🎯 中控释放 - 无需操作（切换模式）")
            break
          default:
            console.warn(`🚨 未知按钮释放: ${buttonType}`)
            return
        }

        this.addEventLog(`按钮释放: ${this.getCommandName(buttonType)}`)
      } catch (error) {
        console.error(`❌ 按钮释放失败: ${buttonType}`, error)
        this.addEventLog(`按钮释放失败: ${buttonType} - ${error.message}`)
      }
    },

    /**
     * 计算角度
     */
    calculateAngle(x, y) {
      return Math.atan2(y - this.wheelCenter.y, x - this.wheelCenter.x)
    },

    /**
     * 标准化角度
     */
    normalizeAngle(angle) {
      while (angle > Math.PI) angle -= 2 * Math.PI
      while (angle < -Math.PI) angle += 2 * Math.PI
      return angle
    },

    /**
     * 回中动画
     */
    returnToCenter() {
      const startAngle = this.wheelAngle
      const duration = 400
      const startTime = Date.now()

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // 使用缓动函数
        const easeProgress = this.easeOutElastic(progress)
        this.wheelAngle = startAngle * (1 - easeProgress)

        if (progress < 1) {
          this.animationTimer = setTimeout(animate, 16)
        } else {
          this.wheelAngle = 0
          this.animationTimer = null
        }
      }

      this.animationTimer = setTimeout(animate, 16)
    },

    /**
     * 停止动画
     */
    stopAnimation() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }
    },

    /**
     * 缓动函数
     */
    easeOutElastic(t) {
      const p = 0.3
      return (
        Math.pow(2, -10 * t) * Math.sin(((t - p / 4) * (2 * Math.PI)) / p) + 1
      )
    },
    /**
     * 初始化方向盘位置
     */
    initWheelPosition() {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this)
        query
          .select(".steering-wheel")
          .boundingClientRect((rect) => {
            if (rect) {
              this.wheelCenter = {
                x: rect.left + rect.width / 2,
                y: rect.top + rect.height / 2,
              }
            }
            resolve()
          })
          .exec()
      })
    },
    /**
     * 触摸开始
     */
    async onTouchStart(e) {
      this.stopAnimation()
      await this.initWheelPosition()

      const touch = e.touches[0]
      this.startAngle = this.calculateAngle(touch.clientX, touch.clientY)
      this.isTouching = true
      this.handleTouchStart()
    },

    onTouchMove(e) {
      if (!this.isTouching || !this.wheelCenter.x) return

      const touch = e.touches[0]
      const currentAngle = this.calculateAngle(touch.clientX, touch.clientY)
      let angleDiff = currentAngle - this.startAngle

      // 标准化角度差
      angleDiff = this.normalizeAngle(angleDiff)

      // 计算目标角度（灵敏度增强）
      const targetAngle = angleDiff * (180 / Math.PI) * 2.5
      this.wheelAngle = Math.min(Math.max(targetAngle, -90), 90)
      console.log("方向盘角度", this.wheelAngle)
      this.handleSteerChange(this.wheelAngle)
    },

    onTouchEnd() {
      if (!this.isTouching) return

      this.isTouching = false
      this.returnToCenter()
      this.handleButtonRelease("steer")
    },

    /**
     * 方向盘触摸开始
     */
    handleTouchStart(event) {
      console.log("🎯 方向盘触摸开始")
    },

    /**
     * 方向盘转向变化
     */
    handleSteerChange(angle) {
      this.steeringAngle = angle
      console.log("🎯 方向盘角度变化:", angle)
    },

    // 🎯 播放按钮处理
    handlePlayPress() {
      this.isPlay = !this.isPlay
      console.log("🎯 播放状态切换:", this.isPlay)
      this.addEventLog(`播放状态: ${this.isPlay ? "开启" : "关闭"}`)
    },

    // 🎯 重新连接蓝牙（智能判断状态）
    async handleReconnectBluetooth() {
      console.log("🔄 蓝牙状态按钮被点击")
      this.addEventLog("用户点击蓝牙状态按钮")

      // 🎯 检查蓝牙状态和权限
      const bluetoothResult = await this.checkBluetoothEnabled()

      // 🎯 根据不同状态执行不同操作
      if (bluetoothResult === "auth_denied") {
        // 1. 小程序蓝牙权限未授权
        this.showBluetoothPermissionModal()
      } else if (!bluetoothResult) {
        // 2. 蓝牙未开启或定位未开启
        this.showBluetoothEnableModal()
      } else if (this.bBleConnected) {
        // 3. 蓝牙适配器已连接 - 检查设备搜索状态
        if (this.deviceSearchStatus && !this.deviceSearchStatus.deviceFound) {
          // 蓝牙连接但未找到目标设备
          this.showDeviceNotFoundModal()
        } else {
          // 设备也找到了，可以重新启动
          this.showReconnectModal()
        }
      } else {
        // 4. 蓝牙开启但未连接 - 尝试连接
        this.showConnectModal()
      }
    },

    // 🎯 初始化完成后检查设备状态
    async checkInitialDeviceStatus() {
      try {
        console.log("🔍 初始化完成，检查设备是否在线...")

        // 进行设备搜索检查（5秒超时）
        const deviceFound = await Promise.race([
          toyCarBleUtil.checkDeviceOnline(),
          new Promise((resolve) => setTimeout(() => resolve(false), 5000)), // 5秒超时
        ])

        // 更新设备搜索状态
        this.deviceSearchStatus = toyCarBleUtil.getDeviceSearchStatus()

        if (deviceFound) {
          console.log("✅ 初始化检查：设备在线")
        } else {
          console.log("❌ 初始化检查：未找到设备")
          // 🎯 重置设备搜索状态，确保UI显示正确
          toyCarBleUtil.resetDeviceSearchStatus()
        }
      } catch (error) {
        console.error("❌ 初始化设备检查失败:", error)
      }
    },

    // 🎯 主动检查设备状态
    async checkDeviceStatus() {
      try {
        console.log("🔍 开始主动检查设备状态")

        // 显示检查中的提示
        uni.showLoading({
          title: "检查设备中...",
          mask: true,
        })

        // 调用蓝牙工具类的主动检查方法
        const deviceFound = await toyCarBleUtil.checkDeviceOnline()

        // 更新设备搜索状态
        this.deviceSearchStatus = toyCarBleUtil.getDeviceSearchStatus()

        uni.hideLoading()

        console.log(
          `🔍 设备检查结果: ${deviceFound ? "找到设备" : "未找到设备"}`
        )
        return deviceFound
      } catch (error) {
        console.error("❌ 检查设备状态失败:", error)
        uni.hideLoading()
        return false
      }
    },

    // 🎯 在发送指令前检查设备状态（快速检查，不显示loading）
    async checkDeviceBeforeCommand() {
      try {
        // 如果蓝牙未连接，直接返回false
        if (!this.bBleConnected) {
          console.log("❌ 蓝牙未连接，无法发送指令")
          this.showDeviceNotFoundModal()
          return false
        }

        console.log("🔍 用户操作时检查设备状态...")

        // 进行快速设备检查（1秒超时）
        const deviceFound = await Promise.race([
          toyCarBleUtil.checkDeviceOnline(),
          new Promise((resolve) => setTimeout(() => resolve(false), 1000)), // 1秒超时
        ])

        // 🎯 无论成功失败都更新设备搜索状态，让UI能正确显示
        this.deviceSearchStatus = toyCarBleUtil.getDeviceSearchStatus()

        if (!deviceFound) {
          console.log("❌ 操作时检查：未找到设备")
          // 🎯 重置设备搜索状态，确保UI显示正确
          toyCarBleUtil.resetDeviceSearchStatus()
          this.showDeviceNotFoundModal()
          return false
        }

        console.log("✅ 操作时检查：设备在线")
        return true
      } catch (error) {
        console.error("❌ 指令前设备检查失败:", error)
        return false
      }
    },

    // 🎯 显示设备未找到弹窗
    showDeviceNotFoundModal() {
      // 🎯 确保设备搜索状态正确反映未找到设备的情况
      this.deviceSearchStatus = toyCarBleUtil.getDeviceSearchStatus()

      const deviceName = this.deviceSearchStatus.targetDeviceName || "TEMP"
      uni.showModal({
        title: "设备未找到",
        content: `未搜索到设备 ，请确认设备已开启并在附近`,
        showCancel: true,
        cancelText: "取消",
        confirmText: "确定",
        // success: (res) => {
        //   if (res.confirm) {
        //     console.log("🔄 用户选择重新搜索设备")
        //     this.initNewArchitecture() // 重新初始化蓝牙，开始搜索
        //   }
        // },
      })
    },

    // ===== 辅助方法 =====

    /**
     * 添加事件日志
     */
    addEventLog(message) {
      const logItem = {
        time:
          new Date().toLocaleTimeString() + " " + new Date().getMilliseconds(),
        data: message,
        type: "event",
        meaning: message,
      }

      if (this.isDevelopment) {
        this.allDataList.unshift(logItem)
        if (this.allDataList.length > 100) {
          this.allDataList = this.allDataList.slice(0, 100)
        }
      }
    },

    /**
     * 解释指令含义
     */
    interpretCommand(data) {
      if (!data || typeof data !== "string") {
        return "未知指令"
      }

      const upperData = data.toUpperCase()

      // 运动指令 (0x10)
      if (upperData.includes("81000210")) {
        // 精确匹配数据位置：81000210[XX] 其中XX是数据位
        const match = upperData.match(/81000210(.{2})/)
        if (match) {
          const dataValue = match[1]
          if (dataValue === "01") return "前进指令"
          else if (dataValue === "02") return "后退指令"
          else if (dataValue === "00") return "停止指令"
          else if (dataValue === "03") return "刹车指令"
          else return `运动指令(${dataValue})`
        }
        return "运动指令"
      }

      // 运动响应 (0x11)
      if (upperData.includes("81000211")) {
        let meaning = ""
        // 精确匹配数据位置：81000211[XX] 其中XX是数据位
        const match = upperData.match(/81000211(.{2})/)
        if (match) {
          const dataValue = match[1]
          if (dataValue === "01") meaning = "前进响应"
          else if (dataValue === "02") meaning = "后退响应"
          else if (dataValue === "00") meaning = "停止响应"
          else if (dataValue === "03") meaning = "刹车响应"
          else meaning = `运动响应(${dataValue})`
        } else {
          meaning = "运动响应"
        }

        return meaning
      }

      // 速度设置指令 (0x12)
      if (upperData.includes("81000212")) {
        const speedMatch = upperData.match(/81000212(.{2})/)
        if (speedMatch) {
          const speedHex = speedMatch[1]
          const speedDec = parseInt(speedHex, 16)
          return `速度设置指令 (${speedDec}%)`
        }
        return "速度设置指令"
      }

      // 速度响应 (0x13)
      if (upperData.includes("81000213")) {
        return "速度设置响应"
      }

      // 转向指令 (0x14)
      if (upperData.includes("81000214")) {
        // 精确匹配数据位置：81000214[XX] 其中XX是数据位
        const match = upperData.match(/81000214(.{2})/)
        if (match) {
          const dataValue = match[1]
          if (dataValue === "01") return "左转指令"
          else if (dataValue === "02") return "右转指令"
          else if (dataValue === "00") return "停转指令"
          else return `转向指令(${dataValue})`
        }
        return "转向指令"
      }

      // 转向响应 (0x15)
      if (upperData.includes("81000215")) {
        // 精确匹配数据位置：81000215[XX] 其中XX是数据位
        const match = upperData.match(/81000215(.{2})/)
        if (match) {
          const dataValue = match[1]
          if (dataValue === "01") return "左转响应"
          else if (dataValue === "02") return "右转响应"
          else if (dataValue === "00") return "停转响应"
          else return `转向响应(${dataValue})`
        }
        return "转向响应"
      }

      // 驻车指令 (0x1A)
      if (upperData.includes("8100021A")) {
        if (upperData.includes("01")) return "开启驻车指令"
        if (upperData.includes("00")) return "关闭驻车指令"
        return "驻车指令"
      }

      // 驻车响应 (0x1B)
      if (upperData.includes("8100021B")) {
        if (upperData.includes("01")) return "开启驻车响应"
        if (upperData.includes("00")) return "关闭驻车响应"
        return "驻车响应"
      }

      return "未知指令"
    },

    /**
     * 获取指令名称
     */
    getCommandName(command) {
      const nameMap = {
        forward: "前进",
        backward: "后退",
        back: "后退",
        left: "左转",
        right: "右转",
        turn: "转向",
        brake: "刹车",
        parking: "驻车",
        gear: "档位",
        speed: "速度",
        steer: "方向盘",
      }
      return nameMap[command] || command
    },

    // 🎯 蓝牙状态文本
    getWifiStatusText() {
      // 🎯 优先判断权限状态
      if (!this.wxBluetoothAuth) return "未授权"
      if (!this.bluetoothOn) return "手机蓝牙未打开"
      return "已连接"
    },

    // 🎯 导航栏左侧按钮点击处理
    handleLeftClick() {
      console.log("🎯 导航栏左侧按钮被点击")
      // 检查页面栈
      const pages = getCurrentPages()
      console.log("🎯 当前页面栈长度:", pages.length)

      if (pages.length === 1) {
        // 如果页面栈只有1个页面，尝试多种方式关闭小程序
        console.log("🎯 页面栈只有1个页面，尝试关闭小程序")

        // #ifdef MP-WEIXIN
        // 方式1：使用 wx.exitMiniProgram
        wx.exitMiniProgram({
          success: () => {
            console.log("🎯 微信小程序关闭成功")
          },
          fail: (err) => {
            console.error("🎯 wx.exitMiniProgram 失败:", err)
            // 方式2：尝试使用 uni.exitMiniProgram
            uni.exitMiniProgram({
              success: () => {
                console.log("🎯 uni.exitMiniProgram 成功")
              },
              fail: (err2) => {
                console.error("🎯 uni.exitMiniProgram 也失败:", err2)
                // 方式3：显示提示让用户手动关闭
                uni.showModal({
                  title: "提示",
                  content: "请手动关闭小程序",
                  showCancel: false,
                })
              },
            })
          },
        })
        // #endif
      } else {
        // 如果页面栈有多个页面，正常返回上一页
        uni.navigateBack({
          delta: 1,
        })
      }
    },

    // 🎯 启动简单倒计时（基于服务器返回的剩余时间）
    startCountdown() {
      // 清除之前的定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }

      console.log("🎯 启动倒计时，剩余时长:", this.remainingTime, "秒")

      // 每秒更新一次
      this.countdownTimer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--

          // 每10秒输出一次日志，避免日志过多
          if (this.remainingTime % 10 === 0) {
            // console.log("⏰ 倒计时更新，剩余:", this.remainingTime, "秒")
          }

          // 时间到了
          if (this.remainingTime <= 0) {
            console.log("⏰ 时间到！停止倒计时")
            this.stopCountdown()
            this.handleTimeUp()
          }
        }
      }, 1000)
    },

    // 🎯 停止倒计时
    stopCountdown() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
        console.log("🎯 倒计时已停止")
      }
    },

    // 🎯 时间到处理
    handleTimeUp() {
      console.log("⏰ 时间到了，订单已结束")

      uni.showModal({
        title: "时间到",
        content: "该订单已结束，即将关闭页面",
        showCancel: false,
        success: () => {
          // 用户点击确定，立即返回上一页
          // uni.navigateBack()
        },
      })

      // 🎯 2秒后自动关闭页面（即使用户不点击确定）
      // setTimeout(() => {
      //   console.log("⏰ 2秒后自动关闭页面")
      //   uni.navigateBack()
      // }, 2000)
    },

    // 🎯 订单信息查询（和 gaming 页面完全一样）
    doGetUserOrderByNo(order_sn) {
      console.log("🌐 ========== API调用开始 ==========")
      console.log("🌐 📞 调用 doGetUserOrderByNo 方法")
      console.log("🌐 📞 传入的订单号:", order_sn)

      // 记录API调用时间
      this.apiCallTime = new Date().toLocaleString()
      console.log("🌐 📞 API调用时间:", this.apiCallTime)

      const { getUserOrderByNoAll } = require("@/common/http/api")

      let params = {
        order_sn,
      }
      console.log("🌐 📞 API请求参数:", JSON.stringify(params, null, 2))

      console.log("🌐 🚀 开始发送API请求...")
      getUserOrderByNoAll(params)
        .then((res) => {
          console.log("🌐 ========== API响应成功 ==========")
          console.log("🌐 ✅ API响应完整对象:", res)
          console.log("🌐 ✅ API响应数据:", res.data)
          console.log("🌐 ✅ 响应数据类型:", typeof res.data)
          console.log("🌐 ✅ 响应数据是否为null:", res.data === null)
          console.log("🌐 ✅ 响应数据是否为undefined:", res.data === undefined)

          if (res.data) {
            console.log(
              "🌐 ✅ 订单数据详情:",
              JSON.stringify(res.data, null, 2)
            )
            this.orderInfo = res.data

            // 🎯 设置全局数据（和 gaming 页面一样）
            res.data?.device_sn &&
              (getApp().globalData.device_sn = this.orderInfo?.device_sn)
            res.data?.machine_type &&
              (getApp().globalData.device_type = this.orderInfo?.machine_type)

            // 🎯 更新蓝牙配置
            try {
              console.log("🎯 开始更新蓝牙配置，订单数据:", {
                bind_ble_device: res.data?.bind_ble_device,
                bind_ble_mac: res.data?.bind_ble_mac,
              })
              const success = updateConfigFromOrderData(res.data)
              if (success) {
                console.log("✅ 蓝牙配置更新成功")
              } else {
                console.log("⚠️ 蓝牙配置更新失败或无变化")
              }
            } catch (error) {
              console.error("❌ 更新蓝牙配置失败:", error)
            }

            // 🎯 使用和 gaming 页面完全相同的时间计算逻辑
            const startTime = this.orderInfo.start_time * 1000 // 开始时间
            const endTime = this.orderInfo.end_time * 1000 // 结束时间
            const nowTime = new Date().getTime() // 现在时间

            // 🎯 总时长直接使用 length_time 参数（分钟转秒）
            const totalSeconds = Math.floor(
              Number(this.orderInfo.length_time) * 60
            )

            // 计算剩余时间（秒）
            const remainingSeconds = Math.max(
              0,
              Math.floor((endTime - nowTime) / 1000)
            )

            console.log("🌐 时间计算:")
            console.log("开始时间:", new Date(startTime).toLocaleString())
            console.log("结束时间:", new Date(endTime).toLocaleString())
            console.log("当前时间:", new Date(nowTime).toLocaleString())
            console.log("总时长:", totalSeconds, "秒")
            console.log("剩余时长:", remainingSeconds, "秒")

            // 更新时间数据
            this.totalTime = totalSeconds
            this.remainingTime = remainingSeconds

            // 🎯 启动倒计时
            if (remainingSeconds > 0) {
              this.startCountdown()
            } else {
              this.handleTimeUp()
            }
          }
        })
        .catch((error) => {
          console.error("🌐 获取订单信息失败:", error)

          // 🎯 网络请求失败时，使用默认值
          console.log("🎯 网络请求失败，使用默认值启动倒计时")
          if (this.remainingTime > 0) {
            this.startCountdown()
          }
        })
    },
  },

  mounted() {
    // 初始化方向盘位置
    console.log("🎯 组件挂载完成")
  },

  beforeDestroy() {
    // 清理动画定时器
    this.cleanupTimers()
  },
}
</script>

<style lang="scss" scoped>
/* 速度控制样式 */
.speed-control-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 10rpx;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 8rpx;
  width: 50%; /* 只占一半位置 */
  justify-content: center;
}

.speed-input {
  width: 60rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(0, 255, 255, 0.6);
  border-radius: 4rpx;
  color: white;
  text-align: center;
  font-size: 20rpx;
  padding: 0;
  line-height: 40rpx;
}

.speed-unit {
  color: white;
  font-size: 20rpx;
}
.back-button {
  position: absolute;
  height: 60rpx;
  right: 350rpx;
  top: 20px;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 255, 255, 0.2);
  padding: 8rpx 25rpx;
  border-radius: 25rpx;
  border: 1rpx solid rgba(0, 255, 255, 0.6);
  backdrop-filter: blur(8rpx);
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 8rpx;
}

// .back-button:hover {
//   background: rgba(0, 0, 0, 0.8);
//   border-color: rgba(255, 255, 255, 0.5);
//   transform: translateY(-2rpx);
// }

// .back-button:active {
//   transform: translateY(0);
//   background: rgba(0, 0, 0, 0.9);
// }

.back-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  font-weight: bold;
}

.toggle-dev-mode-button {
  position: absolute;
  width: 40px;
  height: 40px;
  left: 180px;
  top: 10px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.8);
  border: 2px solid #333;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.speed-btn {
  width: 50rpx;
  height: 40rpx;
  background: rgba(0, 255, 255, 0.2);
  border: 1rpx solid rgba(0, 255, 255, 0.6);
  border-radius: 4rpx;
  color: white;
  font-size: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 🎯 页面包装器 */
.page-wrapper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

/* 🎯 导航栏占位符 */
// .navbar-placeholder {
//   height: 88rpx; /* 导航栏高度 */
//   width: 100%;
//   background: transparent;
// }

/* 关键样式修正 */
.landscape-container {
  width: 100vh;
  height: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  transform-origin: 0 0;
  transform: rotate(90deg) translateY(-100%);
  justify-content: space-between;
  transition: all 0.3s ease;
  margin-top: 88rpx;
  // #ifdef MP-ALIPAY
  margin-top: 0rpx;
  // #endif
}

/* 🎯 有导航栏时的样式调整 */
// .landscape-container.with-navbar {
//   /* 简单粗暴：直接加 top 边距 */
//   top: 44px; /* 导航栏高度 */
// }

.landscape-container {
  .bg-image {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  .content-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: space-between;
    align-items: flex-end;
    position: relative;
  }
  /* 右侧方向盘容器 - 固定右侧定位 */
  .steering-wheel-container {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 10;
  }

  .content-wrapper {
    .ellipse {
      position: absolute;
      bottom: -250rpx;
      left: -10%;
      width: 120%;
      height: 500rpx;
      border-radius: 50%;
      background-color: rgb(34, 34, 34);
    }
  }
}

/* 页面参数显示样式 */
.page-params-log {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 50;
  width: 600rpx;
  max-height: 800rpx;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 15rpx;
  padding: 20rpx;
  overflow-y: auto;
}

.params-header {
  margin-bottom: 15rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
  padding-bottom: 10rpx;
}

.params-title {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.params-content {
  max-height: 120rpx;
  overflow-y: auto;
}

.params-text {
  color: #90ee90;
  font-size: 20rpx;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.test-input {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 5rpx;
  padding: 10rpx;
  font-size: 20rpx;
  margin-bottom: 10rpx;
  width: 100%;
}

.test-button {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 5rpx;
  padding: 10rpx 20rpx;
  font-size: 20rpx;
  margin-top: 5rpx;
  width: 100%;
}

.scan-button {
  background: #ff9500 !important;
  font-weight: bold;
}

.no-params {
  color: #ccc;
  font-size: 20rpx;
  font-style: italic;
}

/* 接收数据显示列表样式 */
.communication-log {
  position: absolute;
  top: 20rpx;
  left: 55%;
  transform: translateX(-50%);
  z-index: 50;
  width: 600rpx;
  height: 650rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

/* 发送数据项样式 */
.send-item {
  background: rgba(0, 255, 0, 0.1) !important;
  border-left: 3rpx solid #00ff00 !important;
  color: #333 !important;
}

/* 接收数据项样式 */
.receive-item {
  background: rgba(0, 150, 255, 0.1) !important;
  border-left: 3rpx solid #0096ff !important;
  color: #333 !important;
}

/* 事件数据项样式 */
.event-item {
  background: rgba(255, 165, 0, 0.1) !important;
  border-left: 3rpx solid #ffa500 !important;
  color: #333 !important;
  font-style: italic;
}

/* 🎯 广播状态样式 */
.broadcast-status {
  font-size: 24rpx;
  font-weight: bold;
  margin-left: 10rpx;
}

.broadcast-success {
  color: #00aa00;
}

.broadcast-failed {
  color: #ff4444;
}

.broadcast-error {
  font-size: 22rpx;
  color: #ff6666;
  margin-left: 5rpx;
  font-style: italic;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.log-count {
  font-size: 24rpx;
  color: #666;
}

.viewDivider {
  width: 100%;
  height: 2rpx;
  background-color: #a1a1a1;
  margin-top: 10rpx;
  margin-bottom: 15rpx;
}

.scanResultGroup {
  flex-grow: 1;
  overflow-y: scroll;
  max-height: 550rpx;
  padding-right: 10rpx;
}

.result-content {
  margin-top: 15rpx;
  white-space: normal;
  word-break: break-all;
  padding: 10rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 5rpx;
  font-size: 24rpx;
  line-height: 1.4;
  color: #333;
  border-left: 3rpx solid #007aff;
}

/* 🎯 自定义标题栏样式 */
.custom-title-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-text {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 新布局样式 */
/* 左上角 WiFi 状态 */
.wifi-status-corner {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 30;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  cursor: pointer;
}

.wifi-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.wifi-connected {
  filter: brightness(1.2);
}

.wifi-disconnected {
  filter: grayscale(1) brightness(0.8);
}

.wifi-text {
  font-size: 24rpx;
  font-weight: 500;
}

.text-connected {
  color: #00ff88;
}

.text-disconnected {
  color: #ff6b35;
}

.top-speed-control {
  position: absolute;
  top: 50rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  z-index: 20;
  width: 50%;
  display: flex;
  justify-content: center;
}

.main-controls-layout {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 40%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 50rpx;
}

.left-side {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.right-side {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

/* 底部仪表盘区域 */
.bottom-gauge-area {
  position: absolute;
  bottom: 0rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  // background-color: pink;
  .steering-wheel {
    width: 300rpx;
    height: 300rpx;
    // background-color: pink;
    position: absolute;
    z-index: 10;
    bottom: 10rpx;
    left: 50%; /* 将元素左侧边缘对齐父元素中心 */
    transform-origin: center center; /* 设置旋转中心点 */
    .wheel-img {
      width: 100%;
      height: 100%;
      top: 0rpx;
    }
  }
}

.fuel-gauge-container {
  position: relative;
  z-index: 1;
}

.steering-overlay {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

/* 当前操作状态提示样式 */
.action-status-tip {
  position: absolute;
  bottom: 440rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 40rpx;
  font-weight: bold;
  color: #00fff0;
  background: rgba(0, 0, 0, 0.5);
  padding: 10rpx 30rpx;
  border-radius: 20rpx;
  z-index: 99;
  text-align: center;
}

/* 🎯 二维码弹窗样式 */
.qrcode-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.qrcode-modal {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90%;
  overflow: hidden;
}

.qrcode-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.qrcode-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.qrcode-content {
  padding: 40rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-container {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 30rpx;
}

.qrcode-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.qrcode-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.qrcode-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 10rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.retry-btn {
  padding: 20rpx 40rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 26rpx;
}

.qrcode-tip {
  font-size: 28rpx;
  color: #666;
}

.qrcode-buttons {
  display: flex;
  border-top: 1rpx solid #eee;
}

.qrcode-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
  background: white;
  cursor: pointer;
}

.qrcode-btn.cancel {
  color: #666;
}

.qrcode-btn.confirm {
  color: #007aff;
  font-weight: bold;
}

.qrcode-btn.cancel {
  color: #999;
  border-right: 1rpx solid #eee;
}

.qrcode-btn.confirm {
  color: #007aff;
  font-weight: bold;
}
</style>
