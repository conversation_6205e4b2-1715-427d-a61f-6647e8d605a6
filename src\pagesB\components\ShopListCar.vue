<template>
    <!--  订单列表项-->
    <view class="shop-item" @click="goDateil()">
        <veiw >
            <view class="shop-img"  @click.stop="goDateil()">
                <image class="shop-image" :src="info.src" />
            </view>
            <view class="shop-title">{{ info.title }}</view>
            <view class="shop-center"></view>
            <view class="shop-bottum">
                <view class="shop-price">
                    <span class="shop-money">￥</span>
                    {{ info.price.toFixed(2).split('.')[0] }}
                    <span class="shop-money">
                        .{{ info.price.toFixed(2).split('.')[1] }}
                    </span>
                </view>
                <view class="shop-sale">
                    <BaseIcon name="shopping-cart" color="orangered" />

                </view>
            </view>
        </veiw>
    </view>


</template>

<script>
import BaseIcon from '@/components/base/BaseIcon.vue';
export default {
    name: "ShopListCard",
    components: { BaseIcon },
    props: {
        info: {
            type: Object,

            default: () => { }
        }
    },

    data() {
        return {
        };
    },

    methods: {
        goDateil() {
            console.log('点击')
            this.$emit('goDateil', this.info)
        },

        showShoping() {
            this.$emit('showShoping')
        },
    },

};
</script>

<style scoped lang="scss">
/* 允许子元素换行 */

.shop-item {
    display: inline-block;

}

.shop-title {
    margin: 10rpx 15rpx 0 15rpx;
    display: -webkit-box;
    /* 使用WebKit的盒子模型 */
    -webkit-box-orient: vertical;
    /* 设置盒子模型为垂直方向 */
    -webkit-line-clamp: 2;
    /* 限制显示的行数为2 */
    overflow: hidden;
    /* 隐藏超出容器的部分 */
    text-overflow: ellipsis;
    /* 虽然对多行文本无效，但保留以防万一 */
    line-height: 35rpx;
    /* 根据字体大小调整行高 */
    font-size: 25rpx;
    font-weight: bold;
}

.shop-img {
    width: 320rpx;
    height: 320rpx;
}

.shop-image {
    width: 100%;
    height: 100%;
}

.shop-center {
    margin: 10rpx 20rpx;
    line-height: 35rpx;
    height: 35rpx;
}


.shop-bottum {
    margin: 0 15rpx 30rpx 15rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .shop-money {
        font-size: 20rpx;
    }

    .shop-price {
        font-weight: bold;
        font-size: 35rpx;
        color: orangered;
    }

    .sale-text {

        margin-right: 10rpx;
    }

    .shop-sale {
        font-size: 20rpx;
    }
}
</style>