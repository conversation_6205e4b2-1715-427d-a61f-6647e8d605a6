<template>
  <view class="container">
    <BaseNavbar :title="vAppName" :isShowSlot="true" :bold="true">
      <image class="user-info-img" :src="vMemberInfo&&vMemberInfo.headimgurl || '/static/icon/logo.png'" @click="gpProfile" />
    </BaseNavbar>
    <BaseTabs :list="productArrTab" :isScroll="true" @change="changeTabs" />
    <MapBox :selectProductTab="selectProductTab" @onMark="onMark" />
    <view class="bottom" :style="{ bottom: vIphoneXBottomHeight + 40 + 'rpx' }">
      <BaseTransition :show.sync="isShowTabbar" mode="fade">
        <Tabbar />
      </BaseTransition>

      <BaseTransition :show.sync="isShowPointPlace" mode="fade">
        <PointPlaceCard :info="selectPointPlaceInfo" />
      </BaseTransition>
    </view>
    <CommonAd :ad="vAd.homeInsertScreenAd" type="inter" />

  </view>
</template>

<script>
import BaseNavbar from "../../components/base/BaseNavbar.vue";
import MapBox from "./components/MapBox.vue";
import { getProduct } from "@/common/http/api";
import BaseTabs from "../../components/base/BaseTabs.vue";
import Tabbar from './components/Tabbar.vue';
import PointPlaceCard from '../../components/PointPlaceCard.vue';
import BaseTransition from '../../components/base/BaseTransition.vue';
import CommonAd from '../../components/WxAd/CommonAd.vue';

export default {
  components: { BaseNavbar, MapBox, BaseTabs, Tabbar, PointPlaceCard, BaseTransition, CommonAd },
  data() {
    return {
      title: "Hello",
      productList: [], //产品列表
      productArrTab: [],
      selectProductTab: {}, //选中的tab
      isShowPointPlace: false,//是否显示位置信息card
      isShowTabbar: true,//是否显示底部tabbar
      selectPointPlaceInfo: {},//选择的位置信息
    };
  },

  methods: {
    gpProfile() {
      uni.navigateTo({ url: '/pagesC/profile/Profile' })
    },
    onMark(flag, item) {
      this.isShowPointPlace = flag;
      this.isShowTabbar = !flag
      this.selectPointPlaceInfo = item

    },
    changeTabs(i) {
      console.log("🚀 ~ i", i);
      this.selectProductTab = i;
    },
    //获取产品
    doGetProduct() {
      getProduct().then((res) => {
        this.productList = res || [];
        // this.productArrTab = res || [];
        this.productArrTab.unshift({
          name: "全部",
          machine_type: "",
          id: "",
        });
      });
    },
  },
  onLoad(opt) {
    this.doGetProduct(); //获取产品
    if (opt?.scanCodeStatus == 0) {
      uni.showModal({
        title: "提示",
        content: "您扫描的二维码不正确，请重新扫描",
        showCancel: true,
        success: ({ confirm, cancel }) => { },
      });
    }
  },
 /*  #ifdef MP-ALIPAY */ 
  onShow() {
    uni.setNavigationBarTitle({
      title: this.vAppName
    });
  },
  /* #endif */
};
</script>

<style lang="scss">
page {
  background-color: #fff;
}
</style>


<style lang="scss" scoped>

.container {
  width: 100%;
  height: 100vh;
}

.user-info-img {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}

.bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 40rpx;
  z-index: 99;
  margin: 0 30rpx;
}
</style>
