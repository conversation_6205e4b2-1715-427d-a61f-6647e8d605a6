<template>
    <!--  订单列表项-->
    <view class="comContent" @click="gotoOrderDetail">
        <view class="topLine ">
            <view class="leftNo">订单编号：{{ itemInfo.order_sn }}</view>
            <view class="rightStatus">{{ status[itemInfo.this_status] || '订单异常' }}</view>
        </view>

        <view class="bottomInfo">
            <view class="left">
                <image class="cover" :src="itemInfo.goods_img" />
            </view>

            <view class="rightInfo">
                <view class="name">
                    {{ itemInfo.goods_name }}二十日说如果生意火热十二个人黑色人色号人黑色火热
                </view>
                <view class="count">{{ '84抽*5包' }}</view>
                <view class="count">{{ itemInfo.add_time }}</view>


            </view>
            <view class="right">
                <view class="right-price">￥{{ itemInfo.order_amount }}</view>
                <view class="right-number">x{{ itemInfo.outnum }}</view>
            </view>
        </view>
        <view class="priceLine">
            <view class="realPay ">
                <view class="label">应付款：</view>
                <view class="payValue">
                    <view class="value">{{ itemInfo.prom_type == 1 ? itemInfo.order_amount : '0.00' }}</view>
                    <view class="unit">元</view>
                </view>
            </view>

        </view>
        <view class="buttom">
            <view class="btn-left" @click="deleteOrder()">删除订单</view>
            <view class="btn-right" @click="goToOrder()">再来一单</view>
        </view>
    </view>
</template>

<script>
export default {
    name: "ShopCard",
    props: {
        itemInfo: Object,
    },

    data() {
        return {

            status: {
                1: '已完成',
                2: '订单异常',
                3: '订单已取消',
                4: '订单异常',
                50: '退款中',
                0: '待付款',
                60: '已出货',
                70: '已退款',
                80: '出货失败',
            },
            orderType: {
                1: '普通订单',
                2: '免费订单',
                3: '免费订单',
                5: '积分订单'

            }
        };
    },

    methods: {
        //删除订单
        deleteOrder(){

        },
        // 再来一单
        goToOrder() {
            uni.navigateTo({
                url: `/pagesB/shop/shopingOrder`,

            }); 
        },
        gotoOrderDetail() {
           
        },
    },
};
</script>

<style scoped lang="scss">
.comContent {
    background: white;
    box-shadow: 0px 3rpx 7rpx 0px rgba(32, 32, 32, 0.15);
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;

    .topLine {
        @include flexRowVertCenter();

        .leftNo {
            color: $textGray;
            font-size: $font-size-xsmall;
        }

        .rightStatus {
            margin-left: auto;
            font-size: $font-size-xsmall;
            color: $mainRed;
        }
    }

    .bottomInfo {
        margin-top: 30rpx;
        display: flex;
        height: 190rpx;

        .left {
            width: 190rpx;
            height: 190rpx;
            border-radius: 10rpx;
            flex-shrink: 0;

            .cover {
                width: 100%;
                height: 100%;
                border-radius: 10rpx;
            }
        }

        .rightInfo {
            flex: 1;
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            height: 190rpx;
            overflow: hidden;

            .name {
                font-size: 28rpx;
                color: $textBlack;
                // font-weight: bold;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
            }

            .count {
                margin-top: 10rpx;
                font-size: $font-size-xsmall;
                color: $textDarkGray;
            }


        }
    }

    .priceLine {
        @include flexRowVertCenter();
        margin: 20rpx;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .realPay {
            @include flexRowVertCenter();

            .label {
                font-size: $font-size-xsmall;
                color: $textDarkGray;
            }

            .payValue {
                margin-left: 0rpx;
                color: $mainRed;
                white-space: nowrap;
                display: flex;
                align-items: flex-end;

                .value {
                    font-size: $font-size-xlarge;
                    font-weight: bold;
                }

                .unit {
                    font-size: $font-size-xsmall;
                    margin-left: 8rpx;
                    font-weight: bold;
                    margin-bottom: 5rpx;
                }
            }
        }

        .btn {
            margin-left: auto;
        }
    }

    .buttom {
        // text-align: right;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 20rpx;
        font-size: 28rpx;

        .btn-left {
            padding: 10rpx 20rpx;
            width: 150rpx;
            border: 1rpx solid #999999;
            border-radius: 50rpx;
            text-align: center;


        }

        .btn-right {
            padding: 10rpx 20rpx;
            width: 150rpx;
            margin-left: 20rpx;
            border: 1rpx solid $mainRed;
            border-radius: 50rpx;
            text-align: center;
            color: $mainRed;

        }
    }

    .right {
        text-align: right;

        .right-number {
            font-size: 30rpx;
            color: #999999;
        }
    }
}
</style>