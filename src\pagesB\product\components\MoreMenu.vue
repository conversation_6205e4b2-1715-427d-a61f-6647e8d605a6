<template>
    <view class="list-card">
        <UniSwiperDot :info="moreList" :current="current" :dotsStyles="{
            backgroundColor: 'rgba(83, 200, 249, 0.3)',
            selectedBackgroundColor: 'rgba(83, 200, 249, 0.9)',
            bottom: '0',
        }" v-if="moreList.length > 0">
            <swiper class="swiper-box" :style="{ height: moreList[0].length > 4 ? '310rpx' : '160rpx' }"
                @change="change">
                <swiper-item v-for="(list, index) in moreList" :key="index">
                    <view class='list'>
                        <view class="list-item" v-for="item in list" :key="item.id" @click="handleLink(item)">
                            <view class="list-item-box">
                                <image class="list-item-box-img" :src="item.img" />
                                <view class="list-item-box-name">{{ item.name }}</view>
                            </view>
                        </view>
                        <view style="width:25%"></view>
                        <view style="width:25%"></view>
                    </view>
                </swiper-item>
            </swiper>

        </UniSwiperDot>
    </view>
</template>
<script>
import { getMiniMenu } from "@/common/http/api"
import UniSwiperDot from "@/components/uni/uni-swiper-dot/uni-swiper-dot.vue"
export default {
    name: "MoreMenu",
    props: {
        deviceSn: { type: String, default: "" },
        miniFoodInfo: { type: Object, default: {} },
    },
    components: { UniSwiperDot },
    data() {
        return {
            moreList: [],
            //定义参数规则
            params: {
                device_sn: this.deviceSn,
                tableid: this.miniFoodInfo?.tableid,
                store_id: this.miniFoodInfo?.store_id,
            },
            current: 0,
        };
    },
    methods: {
        change(e) {
            this.current = e.detail.current;
        },
        handleLink(item) {
            let { link_type, link, scene, msg, appid, env_version } = item;

            let pathUrl = link;
            if (scene) {
                scene = JSON.parse(scene);

                let newScene = scene?.map(item => `${item}=${this.params[item]}`) || [];
                if (newScene?.length > 0) {
                    pathUrl = link + (pathUrl.includes("?") ? "" : "?") + newScene?.join("&");
                }
            }
            // console.log("🚀 ~ pathUrl", pathUrl)
            if (link_type === "local_path") {
                uni.navigateTo({
                    url: pathUrl
                });
            }
            else if (link_type === "url") {
                uni.navigateTo({
                    url: `/pagesC/webView/WebView?url=${encodeURIComponent(pathUrl)}`,
                });
            }
            else if (link_type === "other_path") {
                if (!appid)
                    return uni.showToast({
                        title: "该功能暂时无法使用~",
                        icon: "none"
                    });

                uni.navigateToMiniProgram({
                    appId: appid,
                    path: pathUrl,
                    envVersion: env_version || "release",
                    success: (result) => { console.log("🚀 ~ result", result); },
                    fail: (error) => { console.log("🚀 ~ error", error); }
                });


            }
            else if (link_type === "notice") {
                uni.showToast({
                    title: msg,
                    icon: "none"
                });
            }
        },
        getMiniMenuHandle() {
            getMiniMenu().then(res => {


                let itemIndex = 0,
                    colNum = 8; //每页显示数量
                let funcsList = res
                while (itemIndex < funcsList.length) {
                    this.moreList.push(
                        funcsList.slice(itemIndex, itemIndex + colNum)
                    );
                    itemIndex += colNum;
                }
                // console.log("🚀 ~  this.moreList ", this.moreList)
            });
        }
    },
    created() {
        this.getMiniMenuHandle();
    },

}
</script>


<style scoped  lang='scss'>
.list-card {
    padding-bottom: 30rpx;
}

.list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;

    &-item {
        width: 25%;

        &-box {
            display: flex;
            flex-direction: column;

            align-items: center;

            &-img {
                width: 80rpx;
                height: 80rpx;
                border-radius: 50%;
                box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.1);
            }

            &-name {
                font-size: 28rpx;
                color: #333333;
                margin-top: 16rpx;
            }
        }

        &:nth-child(n+5) {
            margin-top: 20rpx;
        }
    }
}
</style>