<template>
  <!-- 游戏详情页 -->
  <view class="content">
    <!-- #ifndef MP-ALIPAY -->
    <BaseNavbar
      :isShowSlot="true"
      bgColor="#ffffff"
      color="#333"
      :bold="true"
      :title="title"
      @leftClick="onClickHome(false)"
    >
      <!-- #ifdef MP-WEIXIN -->
      <u-icon
        :name="isMorePage ? 'arrow-left' : 'home'"
        size="24"
        color="#333"
      />
      <!-- #endif -->
    </BaseNavbar>
    <!-- #endif -->
    <view class="chargingInfo">
      <view class="deviceSn comText"
        >设备编号：{{ orderInfo.device_sn || "" }} ({{
          orderInfo.um_status_str
        }})</view
      >
      <view class="hotelName comText" :style="{ margin: '10rpx 0' }"
        >商户名称：{{ orderInfo.hotelName || "" }}</view
      >
      <view class="circular flexRowHorzCenter">
        <image
          v-if="orderInfo.is_car_device"
          class="chargingImg"
          :src="gameStatus == 3 || gameStatus == 4 ? carImg.end : carImg.in"
        />
        <image
          v-else-if="orderInfo.is_cannon_device"
          class="chargingImg"
          :src="
            gameStatus == 3 || gameStatus == 4 ? cannonImg.end : cannonImg.in
          "
        />
        <image
          v-else
          class="chargingImg"
          :src="
            gameStatus == 3 || gameStatus == 4 ? gamingImg.end : gamingImg.in
          "
        />
        <!-- <view class="circularTop">
          <view class="line"></view>
        </view>
        <view class="circularBottom">
          <view class="line"></view>
        </view> -->
        <view class="time">
          <view
            class="timeContent flexColumnHorzCenter"
            :class="gameStatus != 3 && gameStatus != 4 ? 'timeContentIng' : ''"
          >
            <view class="title">{{ text }}剩余时长</view>
            <view class="timeTitle">
              <view>时</view>
              <view>分</view>
              <view>秒</view>
            </view>

            <view class="Timing">
              <u-count-down
                ref="countDown"
                :time="remainTime * 60 * 1000"
                format="HH:mm:ss"
                :autoStart="true"
                :millisecond="false"
                @change="onChange"
                @finish="finish"
              >
                <view class="Timing_time">
                  <view class="Timing_time__custom">
                    <text class="Timing_time__custom__item">{{
                      timeData.hours
                    }}</text>
                  </view>
                  <view class="Timing_time__doc">:</view>
                  <view class="Timing_time__custom">
                    <text class="Timing_time__custom__item">{{
                      timeData.minutes
                    }}</text>
                  </view>
                  <view class="Timing_time__doc">:</view>
                  <view class="Timing_time__custom">
                    <text class="Timing_time__custom__item">{{
                      timeData.seconds
                    }}</text>
                  </view>
                </view>
              </u-count-down>
              <!-- <view class="hour">{{ "00" }}</view>
              <view class="spot">:</view>
              <view class="min">{{ "01" }}</view>
              <view class="spot">:</view>
              <view class="second">{{ "30" }}</view> -->
            </view>

            <view class="status">{{ getGameStatus() }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="deviceInfo">
      <view class="deviceInfoItem flexColumnAllCenter">
        <view class="params">{{ orderInfo.order_sn || "未知" }}</view>
        <view class="title">订单编号</view>
      </view>
      <view class="deviceInfoItem flexColumnAllCenter">
        <view class="params">{{ orderInfo.order_amount || "未知" }}</view>
        <view class="title">订单金额</view>
      </view>
      <view class="deviceInfoItem flexColumnAllCenter">
        <view class="params">{{ order_time }}</view>
        <view class="title"> 订单时间 </view>
      </view>
      <view class="deviceInfoItem flexColumnAllCenter">
        <view class="params">{{
          toHourMinute(Math.round(orderInfo.length_time))
        }}</view>
        <view class="title">总时长</view>
      </view>
    </view>
    <view class="home flexRowAllCenter" @click="onClickHome(true)"
      >返回首页</view
    >
    <!-- 
    <view
      class="refund"
      v-if="
        orderInfo.charge_status === 2 &&
        orderInfo.pay_status === 2 &&
        remainTimeData > 0 &&
        user_recharge == 1
      "
    >
      <BaseButton
        text="申请退款"
        shape="circle"
        width="100%"
        type="error"
        :plain="true"
        @onClick="refund"
      />
    </view> -->

    <u-modal
      :show="showModal"
      title="温馨提示"
      :content="content"
      :showCancelButton="true"
      @cancel="showModal = false"
      @confirm="confirmRefund"
    />
    <!-- v-if="chargeStatus != 3 && chargeStatus != 4" -->
    <button class="doubt again" @click="chargeHandle">
      设备未启动？点击重新启动~
    </button>
    <!-- 遥控按钮 -->
    <!--  
    
      -->
    <button
      class="doubt remote-btn"
      @click="goToRemote"
      v-if="
        Number(remainTime) > 0 &&
        orderInfo.bind_ble_device &&
        orderInfo.bind_ble_mac
      "
    >
      🎮 遥控操作
    </button>
    <!-- #ifdef MP-WEIXIN -->
    <button open-type="contact" class="doubt">对此订单有疑问？</button>
    <!-- #endif -->

    <!-- #ifdef MP-ALIPAY -->
    <view class="serverAli">
      <view class="doubt">对此订单有疑问？</view>
      <contact-button
        tnt-inst-id="D4u_nXVF"
        scene="SCE00151022"
        size="100rpx"
        color=""
        icon=""
      />
    </view>
    <!-- #endif -->
    <CommonAd :ad="vAd.gameDetailBannerAd || ''" type="banner" />
    <CommonAd :ad="vAd.screenAdGameDetail || ''" type="inter" />
  </view>
</template>

<script>
import { bleMixin } from "@/mixins/bleMixin"
import { getUserOrderByNo, openDoorByOrderSN } from "@/common/http/api"
import { globalCodes } from "@/global/globalCodes"
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import BaseButton from "@/components/base/BaseButton.vue"
import CommonAd from "@/components/WxAd/CommonAd.vue"
import ble from "@/utils/ble/newBle"
import utils from "@/utils/utils"
export default {
  components: {
    BaseNavbar,
    BaseButton,
    CommonAd,
  },
  mixins: [bleMixin],
  data() {
    return {
      gamingImg: {
        in: require("../static/running.gif"),
        end: require("../static/running_end.png"),
      },
      cannonImg: {
        in: require("../static/cannoning.gif"),
        end: require("../static/cannon.png"),
      },
      carImg: {
        in: require("../static/car.gif"),
        end: require("../static/carend.gif"),
      },
      waterGunImg: {
        in: require("../static/cannoning.gif"),
        end: require("../static/cannon.png"),
      },
      bBleConnected: false,
      isCheckFlag: false, //是否执行查询机器充电状态
      isRefundFlag: false, //是否执行申请退款
      prom_type: {
        1: "普通订单",
        2: "免费订单",
        3: "返佣订单",
        4: "抖音订单",
        5: "积分订单",
      },
      isMorePage: false,
      showModal: false,
      content: "", //退款申请的提示内容
      refundInfo: {
        money: "",
        rest_time: "",
      },
      user_recharge: 0,
      gameStatus: 0, //充电状态 3：结束 4退款
      oldGameStatus: 3,
      orderInfo: {}, //订单信息
      remainTime: 0, //剩余时间
      timeData: {}, //倒计时时间
      order_time: "", // 订单时间
    }
  },
  methods: {
    // 跳转到遥控页面
    goToRemote() {
      // 🎯 直接传递订单号，让 remote 页面自己调用接口获取最新时间
      const orderSn = this.orderInfo.order_sn
      console.log("跳转到遥控页面，订单号:", orderSn)
      uni.navigateTo({
        url: `/pagesD/remote/index?order_sn=${orderSn}`,
      })
    },
    //分钟转换成 00:00:00格式
    toHourMinute(minutes) {
      if (minutes) {
        let h = Math.floor(minutes / 60)
        if (h.toString().length < 2) h = "0" + h
        let m = minutes % 60
        if (m.toString().length < 2) m = "0" + m
        return h + ":" + m + ":" + "00"
      } else {
        return "00:00:00"
      }
    },
    onClickHome(flag) {
      if (!flag && this.isMorePage) return uni.navigateBack()
      /* #ifndef H5 */
      uni.redirectTo({
        url: "/pages/index/index",
      })
      /* #endif */
      /* #ifdef H5 */
      uni.redirectTo({
        url: "/pages/index/index?from=H5",
      })
      /* #endif */
    },
    //订单状态··
    getGameStatus() {
      const { gameStatus, orderInfo } = this
      const isCarDevice = orderInfo.is_car_device

      const statusMapping = isCarDevice
        ? globalCodes.carOrderStatus.nameArr
        : orderInfo.is_cannon_device
        ? globalCodes.cannonOrderStatus.nameArr
        : globalCodes.gameOrderStatus.nameArr

      switch (gameStatus) {
        case globalCodes.gameOrderStatus.STATUS_GAME_TO:
        case globalCodes.gameOrderStatus.STATUS_GAME_ING:
        case globalCodes.gameOrderStatus.STATUS_GAME_END:
        case globalCodes.gameOrderStatus.STATUS_GAME_ERR:
          return statusMapping[gameStatus]
        default:
          return "订单状态异常"
      }
    },
    //订单信息查询
    doGetUserOrderByNo(order_sn) {
      let params = {
        order_sn,
      }
      getUserOrderByNo(params).then((res) => {
        // console.log("--订单返回--", res);
        this.orderInfo = res.data
        // console.log("--订单信息--", this.orderInfo);
        res.data?.device_sn &&
          (getApp().globalData.device_sn = this.orderInfo?.device_sn)
        res.data?.machine_type &&
          (getApp().globalData.device_type = this.orderInfo?.machine_type)
        this.user_recharge = this.orderInfo?.user_recharge
        this.order_time = utils.parseTime(this.orderInfo?.add_time)
        // console.log("-----订单时间：", this.order_time);

        this.oldTime = this.orderInfo.start_time * 1000
        let endTime = this.orderInfo.end_time * 1000
        this.gameStatus = this.orderInfo.game_status
        // if (this.gameStatus > globalCodes.gameOrderStatus.STATUS_CHARGE_ING) {
        //   this.oldGameStatus = this.gameStatus;
        // }

        let nowTime = new Date().getTime() //现在时间
        //套餐时间 - （ 现在时间 - 开始时间） = 剩余充电时间
        this.remainTime = ((endTime - nowTime) / 1000 / 60).toFixed(2)
        let start_count_time = this.remainTime * 60 * 1000

        // console.log(
        //   "--时间--",
        //   this.orderInfo.start_time,
        //   this.orderInfo.end_time,
        //   endTime,
        //   nowTime,
        //   this.remainTime,
        //   start_count_time
        // );

        // 添加自动跳转逻辑
        if (
          Number(this.remainTime) > 0 &&
          this.orderInfo.bind_ble_device &&
          this.orderInfo.bind_ble_mac
        ) {
          setTimeout(() => {
            const orderSn = this.orderInfo.order_sn
            console.log("满足条件，自动跳转到遥控页面，订单号:", orderSn)
            uni.navigateTo({
              url: `/pagesD/remote/index?order_sn=${orderSn}`,
            })
          }, 2000)
        }
      })
    },
    //处理充电
    chargeHandle() {
      // 重新启动
      // if (this.bBleConnected) {
      //   this.isCheckOrder(this.chargeStatus);
      // } else {
      //   this.isCheckFlag = true;
      //   this.startBleConnect();
      // }
      let params = {
        order_sn: this.orderInfo.order_sn,
      }
      openDoorByOrderSN(params).then((res) => {})
    },
    isCheckOrder() {
      //设备未充电
      // console.log("🚀 ~ 设备开始充电》》》》》》》》");
      let nowTime = new Date().getTime() //现在时间
      // console.log("🚀 ~ nowTime", nowTime);
      let chargeTime =
        this.orderInfo.duration *
        (this.orderInfo?.rule_unit == 2 ? 60 : 1) *
        60 *
        1000 //套餐时间
      // console.log("🚀 ~ chargeTime", chargeTime);
      //套餐时间 - （ 现在时间 - 开始时间） = 剩余充电时间
      this.remainTime = Math.ceil(
        (chargeTime - (nowTime - this.oldTime)) / 1000 / 60
      )
      // console.log("🚀 ~ this.oldTime", this.oldTime);
      // console.log("🚀 ~ remainTime", this.remainTime);
      if (this.remainTime > 0) {
        return this.onRecharge(this.remainTime, true)
      }
    },
    //申请退款弹窗
    refund() {
      // let nowDate = new Date().getTime();
      // console.log("🚀 ~ nowDate", nowDate)
      // let endDate = this.orderInfo.end_time * 1000
      // console.log("🚀 ~ endDate", endDate)

      // let diffDate = (endDate - nowDate) / 1000 / 60;//分钟
      let diffDate = this.remainTimeData / 60 //分钟
      let diffHalf = Math.floor(diffDate / 30) //相差多少个30分钟，向下取整
      let diffHour = diffHalf * 0.5 //相差多少小时
      // console.log("🚀 ~ diffDate", diffDate);
      // console.log("🚀 ~ diffHalf", diffHalf);
      // console.log("🚀 ~ diffHour", diffHour);
      let refundAmount =
        (diffHour *
          (this.orderInfo.rule_unit === 1 ? 60 : 1) *
          (parseFloat(this.orderInfo.rule_money) * 1000)) /
        1000

      if (refundAmount < 0.01) {
        return uni.showToast({
          title: `您可退金额${refundAmount}少于0.01,暂时不支持退款~`,
          icon: "none",
          duration: 2000,
        })
      }
      this.content = ` 当前可以申请退款${diffHour}小时，金额为${refundAmount}，是否确认申请退款？`
      this.refundInfo = {
        money: refundAmount,
        rest_time: diffHour,
      }
      this.showModal = true
    },
    //申请退款
    confirmRefund() {
      this.showModal = false
      if (!this.bBleConnected) {
        uni.showToast({ title: "蓝牙未连接，开始连接蓝牙~", icon: "none" })
        this.isRefundFlag = true
        this.startBleConnect()
      } else {
        // 结束充电回调
        ble.rechargeDevice(1, this.btRechargeCallback)
      }
    },
    btRechargeCallback(flag) {
      if (flag) {
        let data = {
          ...this.refundInfo,
          order_sn: this.orderInfo.charge_sn,
        }
        endChargeOrder(data)
          .then((res) => {
            this.isShowSuccess("已申请退款", 0, () => {
              uni.redirectTo({
                url: `/pagesB/order/Order`,
              })
            })
          })
          .catch((err) => {
            uni.showToast({
              title: "退款申请提交失败,请联系客服",
              icon: "none",
              duration: 5000,
            })
          })
      } else {
        uni.showToast({
          title: "结束充电指令执行失败~",
          icon: "none",
          duration: 2000,
        })
      }
    },
    onChange(time) {
      // console.log("🚀 ~ time", time);
      time.hours = (time.hours.toString()?.length == 1 ? "0" : 0) + time.hours
      time.minutes =
        (time.minutes.toString()?.length == 1 ? "0" : 0) + time.minutes
      time.seconds =
        (time.seconds.toString()?.length == 1 ? "0" : 0) + time.seconds
      this.timeData = time
    },
    finish(item) {
      // console.log("🚀 ~ item  倒计时结束", item);
      this.gameStatus = globalCodes.gameOrderStatus.STATUS_GAME_END
      // console.log("🚀 ~ item  倒计时结束 状态", this.gameStatus);
    },
  },
  computed: {
    text() {
      return this.orderInfo.is_car_device
        ? "驾驶"
        : this.orderInfo.is_cannon_device
        ? "运行"
        : "运动"
    },
    title() {
      return this.orderInfo.is_car_device
        ? "驾驶详情"
        : this.orderInfo.is_cannon_device
        ? "大炮运行详情"
        : "骑行详情"
    },
  },
  onLoad(opt) {
    // console.log("🚀 ~ opt", opt);
    this.isMorePage = getCurrentPages()?.length >= 2
    if (opt.order_sn) {
      //执行查询订单
      this.doGetUserOrderByNo(opt.order_sn)
    }
  },
  onUnload() {},
}
</script>

<style lang="scss" scoped>
.content {
  height: 100vh;
  padding: 0 30rpx 30rpx;
  background: #fff;
}

.chargingInfo {
  .comText {
    text-align: center;
    color: $textBlack;
    font-size: $font-size-small;
  }

  .deviceSn {
    // margin-top: 20rpx;
  }

  .circular {
    width: 100%;
    display: flex;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20rpx;

    .chargingImg {
      width: 300rpx;
      height: 220rpx;
    }

    .time {
      // position: absolute;
      // top: 50%;
      // left: 50%;
      width: 330rpx;
      height: 240rpx;
      // transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      justify-content: center;

      .timeContent {
        width: 100%;
        height: 100%;
        justify-content: center;

        .title {
          font-size: $font-size-large;
          font-weight: 700;
          margin-bottom: 20rpx;
          color: rgb(68, 161, 251);
        }

        .timeTitle {
          width: 330rpx;
          display: flex;
          margin-bottom: 16rpx;

          view {
            width: 110rpx;
            text-align: center;
            font-size: $font-size-xsmall;
          }
        }

        .Timing {
          display: flex;
          width: 330rpx;
          height: 80rpx;
          background-color: rgb(227, 241, 255);
          box-sizing: border-box;
          padding: 0 32rpx;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20rpx;
          border-radius: 10px;
          color: rgb(68, 161, 251);

          ::v-deep .u-count-down {
            width: 100%;
            height: 100%;
          }

          // > view {
          //   flex: 20%;
          //   flex-shrink: 0;
          //   font-size: 40rpx;
          // }
          &_time {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;

            :nth-child(n) {
              flex: 20%;
              flex-shrink: 0;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            &__custom {
              width: 25%;
              height: 100%;
            }
          }
        }

        .status {
          font-size: $font-size-base;
        }
      }

      .timeContentIng {
        color: #206bc5 !important;

        .Timing {
          background-color: #f2f8ff !important;
        }
      }
    }

    // .circularTop,
    // .circularBottom {
    //   width: 424rpx;
    //   height: 212rpx;
    //   box-sizing: border-box;
    //   padding: 14rpx;
    //   overflow: hidden;
    //   .line {
    //     width: 100%;
    //     height: 212rpx;
    //     background: #fff;
    //   }
    // }
    // .circularTop {
    //   border-radius: 212rpx 212rpx 0 0;
    //   background-image: linear-gradient(left, #0eade2, #fff);
    //   .line {
    //     border-radius: 212rpx 212rpx 0 0;
    //   }
    // }
    // .circularBottom {
    //   border-radius: 0 0 212rpx 212rpx;
    //   background-image: linear-gradient(to left, #206bc5, #fff);
    //   .line {
    //     border-radius: 0 0 212rpx 212rpx;
    //     margin-top: -14rpx;
    //   }
    // }
  }
}

.deviceInfo {
  display: flex;
  flex-wrap: wrap;

  .deviceInfoItem {
    width: 345rpx;
    height: 120rpx;
    box-sizing: border-box;
    text-align: center;
    color: $textBlack;
    border-bottom: 2rpx solid #e5e5e5;
    border-right: 2rpx solid #e5e5e5;

    .params {
      font-size: $font-size-xxxlarge;
      font-weight: 700;
    }

    .title {
      font-size: $font-size-xsmall;
      margin-top: 14rpx;
    }

    &:nth-child(2n) {
      border-right: 0;
    }

    &:nth-child(n + 3) {
      border-bottom: 0;
    }
  }
}

.home {
  width: 100%;
  height: 80rpx;
  color: #ffffff;
  background: linear-gradient(-81deg, #0c6eb8, #0eade2);
  border-radius: 40rpx;
  margin-top: 60rpx;
}

.refund {
  width: 100%;
  margin-top: 20rpx;
}

.doubt {
  text-align: center;
  color: $themeColor;
  font-size: $font-size-xsmall;
  margin-top: 20rpx;
  border: none;
  outline: none;
  background-color: white;

  &::after {
    border: none;
  }
}

.again {
  text-decoration: underline;
  font-size: $font-size-large;
  font-weight: 700;
  color: black;
}

.remote-btn {
  background: linear-gradient(-81deg, #ff6b35, #f7931e, #ffd23f) !important;
  color: white !important;
  font-weight: bold;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  .serverAli {
    position: relative;

    ::v-deep .a-contact-button {
      position: absolute;
      z-index: 100;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      opacity: 0;
    }
  }
}

.adContainer {
  margin-top: 40rpx;
}
</style>
