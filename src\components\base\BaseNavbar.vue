<template>
      <!-- #ifndef MP-ALIPAY -->
  <block>
    <u-navbar :title="title" v-if="isShowSlot" :placeholder="placeholder" :bgColor="bgColor"
      :titleStyle="{ color, fontWeight: bold ? 700 : 400 }" :safeAreaInsetTop="true" @rightClick="rightClick"
      @leftClick="leftClick">
      <view slot="left">
        <slot />
      </view>
    </u-navbar>
    <u-navbar v-else @rightClick="rightClick" :autoBack="autoBack" :title="title" :placeholder="placeholder"
      :bgColor="bgColor" :leftIconColor="color" :safeAreaInsetTop="true" :leftIconSize="leftIconSize"
      :titleStyle="{ color, fontWeight: bold ? 700 : 400 }" />
  </block>
      <!-- #endif -->
</template>
<script>
export default {
  name: "BaseNavbar",
  props: {
    title: { type: String, default: "" },
    isShowSlot: { type: Boolean, default: false },
    placeholder: { type: Boolean, default: true },
    bgColor: { type: String, default: "#fff" },
    color: { type: String, default: "#333" },
    bold: { type: Boolean, default: false },
    autoBack: { type: Boolean, default: true },
    leftIconSize: { type: String | Number, default: "40rpx" },
  },
  data() {
    return {};
  },

  methods: {
    leftClick() {
      this.$emit("leftClick");
    },
  },
  onLoad() { },
};
</script>

<style scoped lang="scss">
.container {
  font-weight: bold;
}
</style>
