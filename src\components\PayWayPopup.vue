<template>
  <!--支付方式弹窗-->
  <view class="popupContent">
    <view class="titleLine">
      <view class="title">支付中心</view>
    </view>

    <view class="payWayList">
      <block v-for="item in payWayList" :key="item.id">
        <view v-if="item.id == 1 ? isShowBalanceType : true" class="payWayItem" @click="onSelectPayWay(item)">
          <image class="iconPay" :style="item.style" :src="item.icon"></image>
          <view class="nameWrap">
            <view class="name">{{ item.name }}</view>
            <view class="remain" v-if="item.id == 1">（剩余{{ balance }}元）</view>
          </view>
          <image v-if="item.id == 2" class="iconRecom" :src="imgRecom" />
          <image class="imgCheck" :src="imgChecked" v-if="curSelectPayWayId == item.id"></image>
          <image class="imgCheck" :src="imgUncheck" v-if="curSelectPayWayId != item.id"></image>
        </view>
      </block>
    </view>

    <view class="btn">
      <BaseButton  height="95rpx" text="确 认 支 付" @onClick="comfirmSelectPayWay" />
    </view>
  </view>
</template>

<script>
import BaseButton from './base/BaseButton.vue'
export default {
  name: "PayWayPopup",
  components: { BaseButton },
  props: {
    isShowBalanceType: { type: Boolean, default: false },
    balance: { type: String | Number, default: 0, }
  },
  data() {
    return {
      payWayList: [
        {
          id: 1,
          name: "钱包余额支付",
          icon: require("@/static/public/ic_pay_wallet_theme.png"),
          style: "width:50rpx;height:46rpx",
        },
        /* #ifdef MP-WEIXIN ||MP-TOUTIAO */
        {
          id: 2,
          name: "微信支付",
          icon: require("@/static/public/ic_pay_weixin_theme.png"),
          style: "width:56rpx;height:46rpx",
        },
        /* #endif */
        /* #ifdef MP-ALIPAY */
        {
          id: 2,
          name: "支付宝支付",
          icon: require("@/static/public/ic_pay_ali_theme.png"),
          style: "width:50rpx;height:50rpx",
        },
        /* #endif */
      ],

      imgChecked: require("@/static/public/ic_checked.png"),
      imgUncheck: require("@/static/public/ic_uncheck.png"),
      imgRecom: require("@/static/public/ic_pay_recom.png"),
      curSelectPayWayId: 2,
    };
  },

  methods: {
    onSelectPayWay(item) {
      this.curSelectPayWayId = item.id;
    },
    comfirmSelectPayWay() {
      let item = this.payWayList.filter(
        (item) => item?.id == this.curSelectPayWayId
      )?.[0];
      this.$emit("selectPayWay", item);
    },
  },

  mounted() {
    this.curSelectPayWayId = 2
  },
};
</script>

<style scoped lang="scss">
.popupContent {
  background-color: white;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  padding: 40rpx;

  .titleLine {
    @include flexRowVertCenter();

    .title {
      font-size: $font-size-xxxlarge;
      color: $textBlack;
      font-weight: bold;
    }

    .close {
      margin-left: auto;
      width: 26rpx;
      height: 27rpx;
    }
  }

  .payWayList {
    margin-top: 40rpx;

    .payWayItem {
      @include flexRowVertCenter();
      padding: 50rpx 0;
      border-bottom: 1rpx solid $dividerColor;

      &:last-child {
        border-bottom: none;
      }

      &:nth-child(2) {
        .icon {
          width: 59rpx;
          height: 48rpx;
        }
      }

      // .iconWeixin {
      //   width: 53rpx;
      //   height: 51rpx;
      // }
      // .iconAlipay {
      //   width: 50rpx;
      //   height: 50rpx;
      // }
      .iconRecom {
        width: 44rpx;
        height: 22rpx;
      }

      .nameWrap {
        display: flex;
        align-items: flex-end;

        .name {
          font-size: $font-size-xlarge;
          color: $textBlack;
          margin-left: 30rpx;
        }

        .remain {
          font-size: $font-size-xsmall;
          margin-left: 10rpx;
          color: $textGray;
        }
      }

      .imgCheck {
        margin-left: auto;
        width: 36rpx;
        height: 36rpx;
      }
    }
  }

}
</style>
