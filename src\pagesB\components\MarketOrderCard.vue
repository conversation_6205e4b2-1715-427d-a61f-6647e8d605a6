<template>
    <view class="card">
        <view class="content">
            <view class="content-box">
                <view>设备编号：</view>
                <view>{{ info.device_sn }}</view>
            </view>
            <view class="content-box">
                <view>订单编号：</view>
                <view>{{ info.order_sn }}</view>
            </view>
            <view class="content-box">
                <view>采购商品：</view>
                <view>
                    <view v-for="(item, index) in info.goods_data" :key="index">
                        {{ item.goods_name }} - {{ item.goods_num }} x
                        {{ item.goods_price }}
                    </view>
                </view>
            </view>
            <view class="content-box">
                <view>商品总价：</view>
                <view>{{ info.amount.toFixed(2) }}</view>
            </view>
            <view class="content-box">
                <view>商品运费：</view>
                <view>{{ info.transportation_cost }}</view>
            </view>

            <view class="content-box">
                <view>收货姓名：</view>
                <view>{{ info.receiver_name }}</view>
            </view>
            <view class="content-box">
                <view>收货号码：</view>
                <view>{{ info.receiver_phone }}</view>
            </view>
            <view class="content-box">
                <view>详细地址：</view>
                <view>{{ info.receiver_address }}</view>
            </view>

            <view class="content-box">
                <view>订单状态：</view>
                <view class="order-status">{{ statusData }}</view>
            </view>
            <view class="content-box">
                <view>订单时间：</view>
                <view>{{
                        $u.timeFormat(info.time * 1000, "yyyy-mm-dd hh:MM:ss")
                }}</view>
            </view>
            <block v-if="info.status == 3">
                <view class="content-box">
                    <view>快递名称：</view>
                    <view>{{ info.expressName }}</view>
                </view>
                <view class="content-box">
                    <view>快递单号：</view>
                    <view>{{ info.expressNumber }}</view>
                </view>
            </block>
        </view>
    </view>
</template>

<script>
export default {
    name: "MarketOrderCard",
    props: {
        info: { type: Object, default: {} },
    },
    computed: {
        statusData() {
            if (this.info.pay_status === 0) return "待付款";

            switch (this.info.status) {
                case 0:
                    return "待审核";
                case 1:
                    return "待发货";
                case 2:
                    return "发货完成";
                default:
                    return "异常";
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.card {
    position: relative;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
}

.content {
    &-box {
        display: flex;
        font-size: $font-size-base;
        line-height: 1.8;

        .order-status {
            color: $themeColor !important;
        }

        >view {
            &:first-child {
                text-align: right;
                width: 140rpx;
                color: $textGray;
                white-space: nowrap;
                flex-shrink: 0;
            }

            &:last-child {
                color: $textBlack;
            }
        }
    }
}

.btn {
    position: absolute;
    right: 10rpx;
    bottom: 20rpx;
    display: flex;
    justify-content: flex-end;

    &-box {}
}

.express-number {
    margin-top: 20rpx;
}
</style>