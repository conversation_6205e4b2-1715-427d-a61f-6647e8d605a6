<template>
    <!--帮助中心页面-->
    <view class="content">
        <view class="topBtn">
            <view class="serverPhone btnBottom" @click="onClickServerPhone">客服热线</view>
            <!-- #ifdef MP-WEIXIN ||MP-TOUTIAO -->
            <button open-type="contact" class="serverPhone">在线客服</button>
            <!-- #endif -->

            <!-- #ifdef MP-ALIPAY -->

            <view class="serverOnLine">
                <view class="serverPhone">在线客服</view>
                <contact-button class="contactButton" tnt-inst-id="D4u_nXVF" scene="SCE00151022" size="100rpx" color=""
                    icon="" />
            </view>
            <!-- #endif -->
        </view>

        <view class="issueWrap">
            <view class="mainTitle">常见问题</view>
            <BaseCollapse :list="vBaseInfo.question" />
        </view>
        <view class="bottomBar" :style="{ paddingBottom: vIphoneXBottomHeight + 50 + 'rpx' }">
            <view class="useInst btnBottom" @click="gotoUseInstPage">使用说明</view>
            <view class="failureReport btnBottom" @click="onClickFailureReport">

                <!-- #ifndef MP-TOUTIAO -->
                故障上报
                <!-- #endif -->
            </view>
        </view>
        <SafeBlock height="50" />
    </view>
</template>

<script>
import BaseCollapse from "@/components/base/BaseCollapse.vue";
import SafeBlock from '../../components/list/SafeBlock.vue';
export default {
    name: "index",
    components: { BaseCollapse, SafeBlock },
    data() {
        return {

        };
    },

    methods: {


        onClickServerPhone() {
            uni.makePhoneCall({
                phoneNumber: this.vServicePhone,
                // 成功回调
                success: (res) => { },
                // 失败回调
                fail: (res) => { },
            });
        },

        onClickFailureReport() {
            // this.$refs.failureReportPopup.open();
        },

        gotoUseInstPage() {
            uni.navigateTo({
                url: `/pagesC/useInstruction/index`,
            });
        },
    },
    onLoad() {

    },
};
</script>

<style lang="scss">
/*当button里面包含图文时，图文之间会有间距，消除间距的方式是首先对button采用flex竖向布局，然后将子元素的margin和padding设置为0*/
button {
    &:after {
        border: none;
    }

    .button[plain] {
        border: none;
    }

    &:first-child {
        margin-top: 0;
    }

    &:active {
        background-color: white;
    }

    background-color: white;
    border-radius: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
</style>

<style lang="scss">
page {
    background-color: $pageBgColor;
}

.content {
    position: relative;

    .btnBottom {
        color: $textBlack;
        font-size: $font-size-large;
    }

    .topBtn {
        @include flexRowVertCenter();
        height: 120rpx;
        padding: 0 120rpx;
        background-color: white;


        .serverPhone {
            color: $textBlack;
            font-size: $font-size-large;
        }

        button {
            border: none;
            outline: none;
            background-color: white;
            margin-left: auto;
            margin-right: 0 !important;
        }

        .serverOnLine {
            position: relative;
            margin-left: auto;
            margin-right: 0 !important;

            .label {}

            contact-button {
                position: absolute;
                z-index: 100;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                opacity: 0;
            }
        }
    }

    .issueWrap {
        margin-top: 20rpx;
        padding: 60rpx 30rpx 30rpx;
        background-color: white;

        .mainTitle {
            font-size: $font-size-xlarge;
            color: $textBlack;
            font-weight: bold;
        }

        .issueList {
            margin-top: 50rpx;

            .issueItem {
                margin-bottom: 60rpx;
                transition: all 0.3;

                &:last-child {
                    margin-bottom: 0;
                }

                .titleLine {
                    .title {
                        color: $textBlack;
                        font-size: $font-size-base;
                    }

                    .arrowRight {
                        margin-left: auto;
                        width: 14rpx;
                        height: 24rpx;
                    }

                    .arrowDown {
                        margin-left: auto;
                        width: 24rpx;
                        height: 14rpx;
                    }
                }

                .answerLine {
                    font-size: $font-size-small;
                    margin-top: 30rpx;
                    color: $textBlack;
                    line-height: 1.6;
                }
            }
        }
    }

    .adContainer {
        margin: 30rpx 0;

        wx-ad {}
    }

    .bottomBar {
        display: flex;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 0 120rpx;
        padding-top: 45rpx;
        justify-content: space-between;
        background-color: white;
        box-sizing: border-box;

        .useInst {}

        .failureReport {}
    }

    .safeArea {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        z-index: 99999;
    }
}
</style>
