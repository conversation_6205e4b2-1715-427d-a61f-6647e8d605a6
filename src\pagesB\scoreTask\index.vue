<template>
    <view>
        <CommonAd :ad="vAd.personalCenterListCustomAd" type="custom" />
        <view class="task">
            <!-- 任务列表 -->
            <view class="task-list">
                <view class="top">
                    <view class="title">积分任务列表</view>
                    <view class="record" @click="goRecord">积分记录</view>
                </view>
            </view>

            <ComList :loadingType="loadingType">
                <view class="list-item" v-for="(taskItem, index) in listData" :key="taskItem.id">
                    <view class="item-left">
                        <view class="name">任务{{ index + 1 }}：{{ taskItem.name }}</view>
                        <view class="reward">
                            奖励：
                            <text class="reward_num">{{ taskItem.point_number }}</text>
                            积分
                        </view>
                    </view>
                    <view class="item-right" bindtap="freeVideoGet" @click="freeVideoGet(taskItem)">
                        <view>
                            <text class="finish_num">{{ taskItem.done_times }}</text>
                            <text>/ {{ taskItem.task_times }}</text>
                        </view>
                        <view class="btn" :hover-class="
                            taskItem.task_times - taskItem.done_times <= 0 ? '' : 'bind_btn'
                        " :class="
                            taskItem.task_times - taskItem.done_times <= 0
                                ? 'finish_btn'
                                : 'normal_btn'
                        ">{{ taskItem.task_cat }}</view>
                    </view>
                </view>
            </ComList>
        </view>
        <CommonAd :ad="vAd.personalCenterInsertScreenAd" type="inter" />
        <LoginPopup />
    </view>
</template>

<script>
import { getTaskAll, doneTask } from "@/common/http/api";
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull";
import CommonAd from '@/components/WxAd/CommonAd.vue';
import { adMixin } from "@/mixins/adMixin"
import LoginPopup from '../../components/LoginPopup.vue';
export default {
    components: { ComList, CommonAd, LoginPopup },
    mixins: [myPull(), adMixin],
    data() {
        return {
            taskList: [],
            device_sn: "",
        };
    },

    methods: {
        //获取任务
        getList(page, done) {
            let data = {
                page,
                limit: 10,
                device_sn: this.device_sn, //"164437785289322"
            };
            getTaskAll(data).then((res) => {
                done(res.data);
            });
        },
        //完成任务 激励视频播放回调
        onRewardAdEndCallbck() {
            let data = { task_id: this.taskId };
            doneTask(data).then((res) => {
                this.isShowSuccess('积分领取成功', 0, () => {
                    this.refresh();
                })
            });
        },

        freeVideoGet(item) {
            if (item.task_times - item.done_times <= 0) {
                return uni.showToast({
                    title: "该任务没有剩余可完成次数，请试试其他任务吧~",
                    icon: "none",
                    duration: 2500,
                });
            }
            this.taskId = item.id;
            this.showRewardAd()
        },
        goRecord() {
            uni.navigateTo({ url: "/pagesB/scoreTask/ScoreRecord" });
        },
    },
    onLoad(opt) {
        this.device_sn = opt?.device_sn || "";
        this.refresh()
    },


};
</script>

<style lang="scss">
page {
    background-color: #f4f4f4;
}
</style>
<style lang="scss" scoped>
.task-list {
    padding: 50rpx 30rpx 0;
}

.task-list .top {
    display: flex;
    justify-content: space-between;
}

.task-list .top .title {
    color: #333;
    font-size: 32rpx;
    font-weight: bold;
}

.task-list .top .record {
    color: #00b3ab;
    font-size: 28rpx;
    font-weight: bold;
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 32rpx;
    margin-bottom: 30rpx;
}

.list-item .item-left {
    color: #333;
    font-size: 26rpx;
}

.item-left .reward {
    margin-top: 20rpx;
}

.item-left .reward_num {
    color: red;
    margin-right: 10rpx;
}

.list-item .item-right {
    display: flex;
    align-items: flex-end;
}

.list-item .item-right .finish_num {
    color: red;
    margin-right: 10rpx;
}

.list-item .item-right .btn {
    width: 200rpx;
    text-align: center;
    padding: 20rpx 10rpx;
    border-radius: 20rpx;
    margin-left: 20rpx;
    color: #fff;
    transition: all 0.5;
}

.normal_btn {
    background: linear-gradient(98deg, #00b3ab, #1cbbb4, #2be0d8);
    box-shadow: 0px 5rpx 5rpx 0px rgba(116, 208, 103, 0.58);
}

.finish_btn {
    background-color: gray;
}

.bind_btn {
    opacity: 0.4;
    scale: 1.1;
}
</style>
