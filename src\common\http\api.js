const http = uni.$u.http
const setConfig = (lodingTitle, isShowLoading) => {
    return {
        isShowLoading,
        lodingTitle,
    }
}

//根据设备类型获取附近设备
export const getNearbyMachine = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getNearbyMachine', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//获取产品列表
export const getProduct = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getProduct', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//根据产品ID获取附近设备
export const getNearbyMachineByProductId = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getNearbyMachineByProduct', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//通过虚拟码获取设备信息 vscode虚拟码 获取mid或device_sn
export const getDeviceAllInfoByVscode = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getDeviceAllInfoByVscode', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//根据设备mid获取设备信息
export const getDeviceInfoByMid = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getDeviceAllInfoById', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//根据设备编码获取设备信息
export const getDeviceAllInfo = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi//machine/getDeviceAllInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



// 下单（充电线是创建订单同时返回了支付信息）
export const createOrder = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createOrder', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//能否免费领取
export const isGetFree = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/isGetFree', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//用户登录
export const login = (data = {}, loadingText = "登录中", showLoading = true) => {
    return http.post('/waapi/public/login', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//更新用户信息
export const updateUserInfo = (data = {}, loadingText = "正在获取信息", showLoading = true) => {
    return http.post('/waapi/user/updateUserInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//创建免费订单并出货
export const createFreeOrderAndOut = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createRakeOrderAndOut', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//订单  获取订单列表
export const getOrderList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getUserOrderList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//订单 获取订单详情
export const getOrderDetail = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/order/getOrderStatus', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//获取网站基础配置信息：
export const baseInfo = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/index/baseInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取底部菜单
export const getMiniMenu = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/public/getMiniMenu', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



//获取广告信息
export const getShopdAndAd = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getShopAndAd', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取用户手机号

export const getPhoneNumber = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/public/getPhoneNumber', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//更新用户手机号

export const updateUserPhone = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/updateUserPhone', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//投资设备 获取 投资设备总价
export const getInvestPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getInvestPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取精选商城订单
export const getMySelectOrderList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/select_mall/getMySelectOrderList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//广告订单 获取我投放的广告订单
export const getMemberApplyAdList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberApplyAdList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告订单 获取我投放的广告订单详情
export const getMemberApplyAdDetail = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberApplyAdDetail', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告 获取设备的广告出价
export const getAdPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getAdPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告 设备可投放时间段
export const decideAdTime = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/decideAdTime', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告 申请广告投放并支付
export const createAdOrderAndPrepay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createAdOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//投资设备 获取附近设备
export const getHotelInvestPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getHotelInvestPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//投放广告 获取附近设备
export const getMachinAdPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getMachinAdPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
///投资设备 获取投放订单
export const getMemberInvestList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberInvestList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//投资设备 提交订单
export const createInvestOrderAndPrepay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createInvestOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}




//根据订单编号获取支付信息
export const getWxPayInfo = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/pay/prepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//获取精选商城商品列表
export const getGoodsList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/Select_Mall/getGoodsList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//购买精选商城商品：
export const createGoodsOrderAndPay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/Select_Mall/createGoodsOrderAndPay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//统计广告
export const setAdLog = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/setAdLog', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//是否能看视频免费领取
export const getRemainCountMini = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getRemainCountMini', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//创建积分订单
export const createPointOrder = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createPointOrder', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//根据积分订单出货
export const pointOrderOut = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/PointOrderOut', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



//获取积分任务
export const getTaskAll = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/getTaskAll', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//添加积分
export const doneTask = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/doneTask', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//获取完成的积分任务
export const getTaskList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/getTaskList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//积分记录
export const getPointList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/getPointList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//优惠券 获取优惠券列表
export const getCoupon = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getCoupon', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//优惠券 获取优惠券列表
export const orderOutGoodsStatus = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/orderOutGoodsStatus', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//优惠券 获取优惠券列表
export const getUserInfo = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/user/getUserInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//查询支付状态
export const getOrderPayStatus = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/order/getOrderPayStatus', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//更新电量
export const updateUMEle = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/updateUMEle', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//发送出货指令接口
export const sendOpenDoorOrderComplete = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/order/sendOpenDoorOrderComplete', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//发送开门指令接口

export const recOnceOpenDoorOrderComplete = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/order/recOnceOpenDoorOrderComplete', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//发送关门指令接口

export const recOnceCloseDoorOrderComplete = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/order/recOnceCloseDoorOrderComplete', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//发送用户绑定首次设备
export const bindFirstDevice = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/user/bindFirstDevice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//根据订单编号重新开门/出货
export const openDoorByOrderSN = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/order/sendOpenDoorOrderComplete', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

