//用于初始化/显示广告（主要是开屏/插屏/激励视频广告）
export const adMixin = {
    methods: {
        //显示激励广告
        showRewardAd() {
            let rewardedVideoAd = this.vRewardedVideoAd
            console.log("🚀 ~ rewardedVideoAd", rewardedVideoAd)

            if (rewardedVideoAd) {
                rewardedVideoAd.show().catch(() => {
                    // 失败重试
                    rewardedVideoAd
                        .load()
                        .then(() => videoAd.show())
                        .catch((err) => {
                            uni.showToast({ title: "暂无视频,请稍后尝试~", icon: "none", duration: 2000, });
                            console.error('播放激励视频广告出现错误：', err)
                        });
                });


            } else {
                uni.showToast({ title: "暂无视频广告资源,请稍后尝试~", icon: "none", duration: 2000, });
                console.error('暂时没有视频广告资源')
            }
        },
        // 初始化
        init() {
            //初始化激励视频广告
            let rewardedVideoAdId = this.vAd?.rewardedVideoAdId;
            let rewardedVideoAd = this.vRewardedVideoAd;
            // console.log('激励视频，rewardedVideoAdId=', rewardedVideoAdId);
            if (rewardedVideoAdId) {
                if (uni.createRewardedVideoAd) {
                    // 加载激励视频广告
                    rewardedVideoAd = uni.createRewardedVideoAd({
                        adUnitId: rewardedVideoAdId
                    })
                    //捕捉错误
                    rewardedVideoAd.onError(err => {
                        // 进行适当的提示
                        console.log('加载激励视频广告错误：', err)
                        this.isShowErr('视频广告加载错误~')

                    })
                    // 监听关闭
                    rewardedVideoAd.onClose((status) => {
                        if (status && status.isEnded || status === undefined) {
                            // 正常播放结束，下发奖励
                            console.log('激励视频广告正常播放结束')
                            //播放完成回调
                            if (this.onRewardAdEndCallbck) {
                                this.onRewardAdEndCallbck()
                            }
                        } else {
                            // 播放中途退出，进行提示
                            console.log('激励视频广告中途退出')
                        }
                    })
                    console.log("🚀 ~ rewardedVideoAd 存放", rewardedVideoAd)
                    this.$u.vuex('vRewardedVideoAd', rewardedVideoAd)

                }
            }


        }
    },


    onLoad() {
        this.init()
    }
}