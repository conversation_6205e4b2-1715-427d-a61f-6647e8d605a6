<template>
    <view class="container">

        <view class="point">
            <PointDetails :pointPlaceInfo="vPointInfo" :bBleConnected="bBleConnected" />
        </view>
        <view class="tips">
            <view class="tips-tltle">温馨提示：</view>
            <view class="tips-list">
                <view>1、支付成功后请点击完成,否则可能失败</view>
                <view v-if="bleTypeHandle == globalCodes.orderGategory.CHARGE">
                    2、如付款后未出货,请重新扫描二维码</view>
            </view>
        </view>
        <BasePopup :show.sync="isShowPopup">
            <PayWayPopup @selectPayWay="selectPayWay" />
        </BasePopup>
        <view class="fixed-btn">
            <FixedBtn @click="clickPay" :price="price || 0" :btnTitle="btnTitle" />
        </view>
    </view>
</template>
<script>

import FixedBtn from '@/components/fixedBtn/FixedBtn.vue';
import PointDetails from '../../components/PointDetails.vue';
import BasePopup from '../../components/base/BasePopup.vue';
import PayWayPopup from '../../components/PayWayPopup.vue';
import { payOrder } from "@/utils/pay"
import { getWxPayInfo, pointOrderOut, getOrderPayStatus } from "@/common/http/api"
import {
    isBleDevice,
    isWet_Tissu
} from "@/global/globalCodes";
import { globalEvents } from "@/global/globalEvents";
import { bleMixin } from "@/mixins/bleMixin";
export default {
    components: { PointDetails, FixedBtn, BasePopup, PayWayPopup },
    mixins: [bleMixin],
    data() {

        return {
            isShowPopup: false,
            btnTitle: '去支付',
            price: '0',
            bBleConnected: false,
            type: '',
            isPay: false,
        };
    },

    methods: {
        isBleDevice(type) {
            return isBleDevice(type);
        },
        clickPay() {
            console.log('type', this.type)
            if (this.bBleConnected) {
            } else {
                this.startBleConnect();
            }
            if (this.getWay == 2) {
                this.onClickPointExchange()
            } else {
                /* #ifdef MP-TOUTIAO */
                //直接调用抖音收银台
                this.requestWxPay();
                /* #endif */
                /* #ifndef MP-TOUTIAO */
                this.$u.vuex('vPayStart', true)
                this.isShowPopup = true
                /* #endif */
            }
        },
        selectPayWay(e) {
            this.isShowPopup = false;
            if (e?.id == 2) {
                console.log("开始调用支付");
                //微信支付
                // 
                if (isBleDevice(this.type) || isWet_Tissu(this.type)) {
                    this.bBleConnected = getApp().globalData.connected;

                    if (getApp().globalData.connected == false) {
                        this.startBleConnect();
                    } else {

                        this.shop()
                    }
                } else {
                    this.requestWxPay();
                }
            }
        },
        async shop() {
            let that = this;
            let type = this.type

            //取袋机支付
            let params = {
                order_sn: this.vCreateOrderInfo?.orderInfo?.order_sn,
                device_sn: this.vCreateOrderInfo?.pointPlaceInfo?.device_sn,
            };

            console.log('params-vPayStart', params, this.vPayStart)
            //更改状态
            const res = await getWxPayInfo(params)

            let payInfo = res.data;
            // console.log('this.payOrderInfo.orderInfo', this.payOrderInfo.orderInfo)
            //请求微信支付
            uni.requestPayment({
                /* #ifdef MP-WEIXIN */
                provider: "wxpay",
                timeStamp: payInfo.timeStamp,
                nonceStr: payInfo.nonceStr,
                package: payInfo.package,
                signType: payInfo.signType,
                paySign: payInfo.paySign,
                /* #endif */
                /* #ifdef MP-ALIPAY */
                provider: "alipay",
                orderInfo: payInfo.trade_no,
                /* #endif */
                /* #ifdef MP-TOUTIAO */
                orderInfo: payInfo,
                service: 5,//固定值，拉起小程序收银台
                /* #endif */
                success(res) {
                    console.log("取袋机支付成功", res, this.vPayStart);
                    /* #ifdef MP-ALIPAY */
                    if (res.resultCode != 9000) {
                        that.$u.vuex('vPayStart', false)
                        clearInterval(time)
                        uni.showToast({
                            title: "支付失败",
                            icon: "none",
                        });
                        return uni.navigateBack();
                    }

                    /* #endif */
                    //跳转到订单详情页面
                    // console.log('订单号', that.payOrderInfo.orderInfo.order_sn)

                    if (that.bBleConnected) {
                        if (!that.isPay) {
                            that.isPay = true
                            clearInterval(time)
                            if (isWet_Tissu(type)) {
                                that.onOpen()
                            } else {

                            }
                        }
                    } else {
                        that.startBleConnect();
                    }

                },
                fail(res) {
                    clearInterval(time)
                    console.log("支付失败", res);
                    that.$u.vuex('vPayStart', false)
                    uni.showToast({
                        title: "支付失败",
                        icon: "none",
                    });
                    return uni.navigateBack();
                },
                complete(res) {
                    clearInterval(time)
                    console.log("支付complete", res);
                    //clearInterval(that.sti);
                },
            });
            const time = setInterval(() => {
                console.log("支付", that.isPay);
                if (!that.isPay) {
                    console.log('轮询查询')
                    getOrderPayStatus(params).then(res => {
                        console.log('获取用户信息成功', res)
                        if (res.pay_status == 1) {
                            clearInterval(time)
                            that.isPay = true;
                            that.onOpen()
                        }

                    }).catch(err => {
                        console.log('获取用户信息失败', err)
                        that.isShowErr('获取用户信息失败')
                    })


                }
            }, 1000)

        },
        requestWxPay() {
            let that = this;
            let params = {
                order_sn: this.vCreateOrderInfo?.orderInfo?.order_sn,
                device_sn: this.vCreateOrderInfo?.pointPlaceInfo?.device_sn,
            };
            getWxPayInfo(params).then((res) => {
                console.log("🚀 ~ res", res)
                let payInfo = res.data;
                //请求支付
                payOrder(this, payInfo, `/pagesB/order/OrderDetails?order_sn=${this.vCreateOrderInfo?.orderInfo?.order_sn}`)

            });

        },
        //积分兑换
        //点击了确认兑换
        onClickPointExchange() {
            let params = {
                order_sn: this.vCreateOrderInfo.orderInfo.order_sn,
            };
            pointOrderOut(params).then((res) => {

                if (res?.prepay_info) {
                    this.isShowSuccess('兑换成功', 0, () => {
                        uni.redirectTo({
                            url: `/pagesB/order/OrderDetails?order_sn=${this.vCreateOrderInfo?.orderInfo?.order_sn}`,
                        })

                    });
                } else {
                    this.isShowErr('兑换失败~')
                }


            });

        },
    },
    onLoad(opt) {
        console.log("🚀 ~ opt", opt)
        //1是立即购买 2是积分兑换
        this.getWay = opt?.getWay ?? 1
        if (this.getWay == 2) {
            this.btnTitle = '确认兑换'

            this.price = (this.vCreateOrderInfo.curSelMeal.exchange_integral ?? 0) + '积分'
            console.log("🚀 ~  this.price", this.price)


        } else {
            this.btnTitle = '去支付'
            this.price = this.vCreateOrderInfo.orderInfo.order_price
        }
        this.type = getApp().globalData.device_type;

        if (isBleDevice(this.type) || isWet_Tissu(this.type)) {
            this.bBleConnected = getApp().globalData.connected;

            if (getApp().globalData.connected == false) {
                this.startBleConnect();
            }
        }
        console.log('蓝牙是否链接', this.bBleConnected)
        //监测蓝牙连接成功事件
        uni.$off(globalEvents.EVENT_BLE_CONNECT_CHANGED);
        uni.$on(globalEvents.EVENT_BLE_CONNECT_CHANGED, (e) => {
            console.log("comfrm，收到蓝牙连接状态改变事件，是否连接：", e.connected);
            this.bBleConnected = e.connected;
            getApp().globalData.connected = e.connected;
        });

    },
}
</script>


<style lang='scss'>
page {
    background-color: $pageBgColor;
}
</style>

<style scoped lang='scss'>
::v-deep .u-button__text {
    font-size: 35rpx !important;
}

.tips {
    padding: 20rpx;

    &-tltle {
        color: #666;
        font-size: $font-size-middle;
        font-weight: 700;
    }

    &-list {
        color: #999;
        font-size: $font-size-base;

        >view {
            line-height: 1.8;
        }
    }
}

.container {
    height: 100vh;
}

.point {
    padding: 30rpx;
    background-color: #fff;
}

.fixed-btn {
    @include fixedBottom();
}
</style>