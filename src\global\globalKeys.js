//本地存储/全局变量存储等的key
export const globalKeys = {
    KEY_IF_READ_USE_INSTRUCTION_BLE: 'if_read_use_instruction_ble',  //是否已经读取过使用说明（使用蓝牙）
    KEY_IF_READ_USE_INSTRUCTION_PASS: 'if_read_use_instruction_pass',  //是否已经读取过使用说明（使用密码）
    KEY_GET_LOC_PERMISSION: 'bGetLocPermission',             //是否获取到定位权限
    KEY_IF_AGREE_PROTOCOL: 'key_if_agree_protocol',          //是否同意服务协议
    KEY_CUR_ORDER_INFO: 'key_cur_order_info',                //当前用户支付的订单信息

    AFTER_PERMISSION_JUMP_PAGE: 'after_permission_jump_page',    //同意授权后的跳转信息
    LAST_SELECT_PAY_WAY_ID: 'last_select_pay_way_id',            //最后一次选择的支付方式id
    NOW_CHARGING_INFO: 'now_charging_info',                      //当前正在充电订单信息

    CUR_SELECT_MACHINE_INFO: 'cur_select_machine_info',          //点击选择的点位/设备信息
    CUR_LATITUDE: 'cur_latitude',        //当前纬度
    CUR_LONGITUDE: 'cur_longitude',      //当前经度

}