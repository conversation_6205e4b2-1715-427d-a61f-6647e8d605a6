<template>
    <view>
        <ComList :loadingType="loadingType">
            <NearbyMerchantsDeviceCard v-for="item in listData" :key="item.id" :info="item"
                @selectItem="selectItem(item)" />
        </ComList>
        <view class="fixed-btn flexRowBetween">
            <label class="radio" @click="selectAll(!selectInfo.isAllCheck)">
                <radio :checked="selectInfo.isAllCheck" color="#0DACE0" />
                全选({{ selectInfo.selectNum }})
            </label>
            <view class="btn" @click="confirm">确 认</view>
        </view>
    </view>
</template>
<script>
import ComList from "../../components/list/ComList.vue";
import NearbyMerchantsDeviceCard from "./components/NearbyMerchantsDeviceCard.vue";
export default {
    components: { ComList, NearbyMerchantsDeviceCard },
    computed: {
        selectInfo() {
            this.selectDeviceList = this.listData?.filter((el) => el.isCheck);
            let selectNum = this.selectDeviceList?.length ?? 0;
            let isAllCheck = selectNum === this.listData.length && selectNum !== 0;

            return {
                isAllCheck,
                selectNum,
            };
        },
    },
    data() {
        return {
            listData: [],
            selectDeviceList: [],
            loadingType: 2,
        };
    },
    methods: {
        selectItem(item) {
            if (typeof item.isCheck == "undefined") {
                this.$set(item, "isCheck", true);
            } else {
                item.isCheck = !item.isCheck;
            }

            console.log("🚀 ~   this.listData ", this.listData);
        },
        selectAll(isAll) {
            this.listData = this.listData.map((el) => ({ ...el, isCheck: isAll }));
        },
        confirm() {
            if (this.selectDeviceList?.length <= 0)
                return uni.showToast({ title: "请先选择设备~", icon: "none" });
            let pages = getCurrentPages();
            let currPage = pages[pages.length - 1]; //当前页面
            let prevPage = pages[pages.length - 3]; //上2个页面
            //直接调用上一个页面的setData()方法，把数据存到上2个页面中去
            prevPage.setData({
                item: this.selectDeviceList,
            });
            uni.navigateBack({ delta: 2 });
        },
    },
    onLoad() {
        let info = JSON.parse(uni.getStorageSync("select_place"));
        this.listData = info.map((el) => ({ ...el, isCheck: false }));
        this.loadingType = this.listData?.length > 0 ? 2 : 3
    },
};
</script>
<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.fixed-btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    background-color: $uni-bg-color;
    z-index: 99;

    .total {
        &-box {
            display: flex;
            align-items: flex-end;
            color: $textBlack;
            font-size: $font-size-base;

            &-mark {
                color: red;
                font-size: $font-size-xsmall;
            }

            &-price {
                color: red;
                font-size: $font-size-middle;
            }
        }

        &-num {
            color: $textDarkGray;
            font-size: $font-size-small;
        }
    }

    .btn {
        padding: 20rpx 50rpx;
        color: #fff;
        font-size: $font-size-xlarge;
        background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
        border-radius: 20rpx;
    }
}
</style>
