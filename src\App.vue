<script>
import { calcBottomBarHeight } from "@/utils/safeArea"

export default {
  onLaunch: function () {
    console.log("App Launch");
    //计算iphoneX小黑条高度
    this.$u.vuex("vIphoneXBottomHeight", calcBottomBarHeight() || 0);
    this.$u.vuex('vPayStart', false)
    this.getBaseInfo();//获取网站基本配置
    this.checkForUpdate()
  },
  onShow: function (options) {
    console.log("App Show");
    /* #ifdef MP-ALIPAY */
    // 支付宝扫码普通二维码
    if (options && options.query && options.query.qrCode) {
      // 使用 getApp() 来访问全局的 app 实例
      getApp().globalData.aliScanCode = options.query.qrCode;
    }
    /* #endif */
    if (this.globalData.timeout_id) {
      clearTimeout(this.globalData.timeout_id)
      this.globalData.timeout_id = null
    }
  },
  onHide: function () {
    console.log("App Hide");
    const ble = require("./wxutil/newBle");
    console.log("vPayStart=", this.vPayStart);
    if (this.vIsBleDevice && !this.vPayStart) {
      console.log("延迟15S 关闭蓝牙,ble=", ble);
      this.globalData.timeout_id = setTimeout(() => {
        console.log("进入计时器查询出货状态", this.vPayStart);
        if (!this.vPayStart) {
          // console.log("关闭蓝牙");

          uni.hideLoading();
          ble.default.closeBle();
          clearTimeout(this.globalData.timeout_id)
        }
      }, 4000)

    }
  },
  globalData: {
    // 蓝牙状态信息
    searching: false, // 蓝牙是否在搜索
    connected: false, // 蓝牙是否链接
    connectedDeviceId: "", // 蓝牙设备的DevicesId
    services: {}, // 蓝牙特征值集合
    characteristics: {},
    devicesList: {}, // 存储搜索出来的蓝牙设备
    electric: -1, // 设备电量
    mkey: '',//蓝牙keY值

    // 设备相关信息
    device_sn: "", //设备编号   9988c6976aab913c  FM95b7b05c8210fe21
    device_id: "", // 设备ID
    device_type: "", // 设备类型
    is_recharge: false, //是否是充电设备
    vscode: "", //设备蓝牙虚拟码
    device_info: {}, //设备信息
    is_ble_device: true, // 是否是蓝牙设备
    wifiInfo: {
      wifi_name: '',
      wifi_psd: '',
    },//wifi信息
    wifiConnected: false,//wifi是否连接
    mtu: '',//mtu

  },
  methods: {
    getBaseInfo() {
      this.$u.http.post('/waapi/index/baseInfo').then(res => {
        this.$u.vuex('vBaseInfo', res)
      })
      this.$u.http.post('waapi/user/getUserInfo').then((res) => {
        uni.$u.vuex('vMemberInfo', res)
      })
    },
    //检测版本更新
    checkForUpdate() {
      const updateManager = uni.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        // 请求完新版本信息的回调
        console.log('【版本更新】', res.hasUpdate);
      });

      updateManager.onUpdateReady(function (res) {
        uni.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success(res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate();
            }
          }
        });

      });

      updateManager.onUpdateFailed(function (res) {
        console.log("🚀 ~ res", res)
        // 新的版本下载失败
        console.log('【版本更新】更新失败')
      });
    },


  },

};
</script>

<style lang="scss">
/*每个页面公共css */
@import "uview-ui/index.scss";
</style>
