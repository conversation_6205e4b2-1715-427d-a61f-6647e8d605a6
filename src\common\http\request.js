// 此vm参数为页面的实例，可以通过它引用vuex中的变量
module.exports = (vm) => {
	// 初始化请求配置
	uni.$u.http.setConfig((config) => {
		config.timeout = 10000; // 设置请求超时时间为10秒 
		/* config 为默认全局配置*/
		config.baseURL = vm.vBaseURL; /* 根域名 */
		// 注：如果局部custom与全局custom有同名属性，则后面的属性会覆盖前面的属性，相当于Object.assign(全局，局部)
		config.custom = {
			isShowLoading: false,
			lodingTitle: '加载中'
		} // 全局自定义参数默认值
		return config
	})

	// 请求拦截
	uni.$u.http.interceptors.request.use((config) => { // 可使用async await 做异步操作
		let session3rd = vm.vSession3rd
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		config.data = config.data || {}
		if (config.custom.isShowLoading) {
			uni.showLoading({ title: config.custom.lodingTitle })
		}
		setTimeout(() => {
			if (config.custom.isShowLoading) {
				uni.hideLoading()
			}
		}, 5000)

		config.header = {
			'content-type': 'application/x-www-form-urlencoded',
			// #ifdef MP-WEIXIN 
			'XX-App-Type': 'wx',
			// #endif
			// #ifdef MP-ALIPAY
			'XX-App-Type': 'ali',
			// #endif
			/* #ifdef MP-TOUTIAO */
			'XX-App-Type': 'dy',
			/* #endif */
			'session3rd': session3rd,
		}
		return config
	}, config => { // 可使用async await 做异步操作
		return Promise.reject(config)
	})

	// 响应拦截
	uni.$u.http.interceptors.response.use((response) => { /* 对响应成功做点什么 可使用async await 做异步操作*/

		if (response.config.custom.isShowLoading) {
			uni.hideLoading()
		}
		const data = response.data


		if (data.code === 1) {
			return data.data === undefined ? {} : data.data
		} else {
			if (['未登录', '授权已失效', '登陆异常'].some(el => data.msg.includes(el))) {
				uni.clearStorageSync()
				uni.$u.vuex('vIsLogin', false)
				uni.$u.vuex('vSession3rd', '')
				uni.$u.vuex('vMemberInfo', '');
				uni.$u.toast('未授权,请先授权~', 3000);
				uni.$emit('onlogon')
			} else {
				uni.$u.toast(data.msg, 3000)
			}

			return new Promise(() => { })
		}
	}, (response) => {
		// 对响应错误做点什么 （statusCode !== 200）
		return Promise.reject(response)
	})

}